# Elysium Database Archiving System - Comprehensive Architecture Documentation

## Table of Contents
1. [System Overview](#system-overview)
2. [Architecture Components](#architecture-components)
3. [Component Interactions](#component-interactions)
4. [Data Flow](#data-flow)
5. [Security Architecture](#security-architecture)
6. [Error Handling and Monitoring](#error-handling-and-monitoring)
7. [Cost Optimization](#cost-optimization)
8. [Future Enhancements](#future-enhancements)

## System Overview

The Elysium Database Archiving System is a comprehensive solution designed to automate the process of archiving old database records to AWS S3 storage. The system provides cost-effective data management by moving infrequently accessed data from expensive database storage to cheaper object storage while maintaining data integrity and accessibility.

### Key Features
- Automated database archiving based on configurable retention policies
- Multi-threaded processing for optimal performance
- Support for both partitioned and non-partitioned tables
- Comprehensive error handling and notification system
- Cost calculation and savings tracking
- Secure SSH-based connectivity to remote database servers
- Web-based management interface

## Architecture Components

### 1. Frontend Application (Elysium Frontend)
**Location**: `elysium-frontend/`
**Technology**: React/Vue.js (Web-based interface)

**Responsibilities**:
- User authentication and authorization
- Schedule configuration and management interface
- Database connection setup and testing
- Real-time monitoring dashboard
- Cost savings visualization
- Archive status tracking

**Key Features**:
- Schedule Dashboard (`src/components/scheduleDashboard/`)
- Database configuration forms
- Archive progress monitoring
- Cost analysis reports

### 2. Backend API (Elysium Backend)
**Location**: `elysium-backend/`
**Technology**: Laravel PHP Framework

**Responsibilities**:
- RESTful API endpoints for frontend communication
- User authentication and session management
- Database configuration management
- Schedule processing and execution
- Agent communication handling
- Notification dispatch system

**Key Components**:
- **Schedule Controllers** (`app/Http/Controllers/API/Schedule/`)
  - `ProcessScheduleController.php`: Handles schedule execution
  - Schedule CRUD operations
- **Console Commands** (`app/Console/Commands/`)
  - `ScheduleProcessing.php`: Main scheduler command
- **API Endpoints**:
  - `/api/get_db_response`: Receives database information from agents
  - `/api/schedule_response`: Receives archival process status updates
  - `/api/agentfailure`: Handles error notifications from agents

### 3. Database Layer
**Technology**: MySQL/PostgreSQL
**Purpose**: Configuration and metadata storage

**Key Tables**:
- **database_job_schedule**: Database-level scheduling configuration
- **table_job_schedule**: Table-level archival parameters
  - `data_retention_days`: Retention period configuration
  - `batch_size`: Processing batch size
  - `archive_day_of_week`: Schedule bitmap
  - `archive_start_at_utc`/`archive_untill_date_utc`: Time windows
  - `is_partitioned`: Table partitioning flag
- **archival_info**: Database and table metadata
- **db_stats**: Database size and archival statistics
- **table_stats**: Table-level archival tracking

### 4. Bastion Server
**Purpose**: Secure gateway for database access
**Technology**: Linux server with SSH access

**Responsibilities**:
- Secure SSH tunneling to remote database servers
- Agent script execution environment
- Database connectivity proxy
- Script storage and execution

**Key Scripts**:
- `db_archive.sh`: Main archival script
- `db_info.sh`: Database information collection
- `schedule_test.sh`: Schedule execution wrapper

### 5. Agent System
**Location**: Bastion server
**Technology**: Bash scripts

**Core Functions**:
- **Size Calculation**: Determines data volume for archival
- **Export/Archive**: Extracts data to TSV files
- **Data Deletion**: Removes archived data from source
- **S3 Integration**: Uploads archived files to AWS S3
- **Compression**: Applies compression to reduce storage costs
- **Error Handling**: Comprehensive error detection and reporting

### 6. Remote Database Servers
**Purpose**: Source databases containing data to be archived
**Access Method**: SSH tunneling through bastion server

**Characteristics**:
- Production database instances
- Accessed via MySQL login paths for security
- Support for both partitioned and non-partitioned tables
- Primary and foreign key relationship handling

### 7. AWS S3 Storage
**Purpose**: Long-term storage for archived data
**Features**:
- Cost-effective object storage
- Configurable storage classes
- Compression support (70% compression rate assumed)
- Secure file upload with verification

## Component Interactions

### 1. Database Discovery and Configuration
```
Frontend → Backend API → Bastion Server → db_info.sh → Remote DB
                ↓
Backend API ← Agent (via /api/get_db_response)
```

### 2. Schedule Execution Flow
```
Laravel Scheduler → ScheduleProcessing Command → ProcessScheduleController
                                                        ↓
Bastion Server ← SSH Connection ← Backend API
                ↓
db_archive.sh → Remote Database → AWS S3
                ↓
Backend API ← Agent Response (via /api/schedule_response)
```

### 3. Error Handling Flow
```
Agent Error → Email/Slack Notification
            → Backend API (via /api/agentfailure)
            → Database Owner Notification
```

## Data Flow

### 1. Initialization Phase
1. User configures database connections through frontend
2. System executes `db_info.sh` to discover database structure
3. Database and table metadata stored in configuration database
4. Retention policies and schedules configured per table

### 2. Scheduled Archival Process
1. Laravel scheduler triggers every minute
2. System identifies eligible tables based on:
   - Day of week configuration
   - Time window settings
   - Active status
3. SSH connection established to bastion server
4. `db_archive.sh` executed with table-specific parameters
5. Multi-threaded processing begins for eligible tables

### 3. Archival Execution
1. **Data Identification**: Query database for records older than retention period
2. **Export Process**: Extract data in configurable batches to TSV files
3. **Validation**: Verify row counts between database and exported files
4. **S3 Upload**: Upload compressed files to AWS S3 with verification
5. **Data Deletion**: Remove archived data from source database
6. **Status Update**: Report completion status back to backend API

### 4. Post-Processing
1. Update database statistics
2. Calculate cost savings
3. Send completion notifications
4. Clean up temporary files
5. Update schedule status for next execution

## Security Architecture

### 1. Authentication and Authorization
- User authentication through backend API
- Role-based access control for different user types
- Session management and token-based authentication

### 2. Database Connectivity
- MySQL login paths for secure credential storage
- No hardcoded passwords in scripts or configuration
- SSH key-based authentication for bastion server access

### 3. Network Security
- SSH tunneling for all database connections
- Bastion server as single point of database access
- Encrypted data transmission
- VPC/network isolation where applicable

### 4. Data Security
- Secure file upload to S3 with verification
- Temporary file cleanup after processing
- Audit logging of all operations
- Error notification without sensitive data exposure

## Error Handling and Monitoring

### 1. Multi-Level Error Detection
- **Script Level**: Bash error trapping with `set -Eeu`
- **Application Level**: Laravel exception handling
- **Database Level**: Transaction rollback on failures
- **Infrastructure Level**: SSH connection monitoring

### 2. Notification System
- **Email Notifications**: Detailed error reports with log attachments
- **Slack Integration**: Real-time alerts via webhooks
- **Database Logging**: Persistent error tracking
- **Frontend Alerts**: User interface notifications

### 3. Monitoring and Logging
- Structured logging with timestamps and severity levels
- Performance tracking and execution time monitoring
- Thread management and resource utilization tracking
- S3 upload verification and retry logic

## Cost Optimization

### 1. Storage Cost Reduction
- Migration from expensive database storage to cost-effective S3
- 70% compression rate for archived files
- Configurable retention policies to optimize storage duration

### 2. Processing Optimization
- Multi-threaded processing to reduce execution time
- Batch processing to minimize database load
- Efficient query optimization for large datasets

### 3. Cost Calculation
- Real-time cost savings calculation
- Comparison between RDS and S3 storage costs
- ROI tracking and reporting

## Future Enhancements

### 1. Queue System Implementation
- Implement message queue for archive requests
- Decouple schedule processing from immediate execution
- Improve system scalability and reliability

### 2. Microservices Architecture
- Separate archival processing into dedicated microservices
- Container-based deployment (ECS/Kubernetes)
- Independent scaling of components

### 3. Enhanced Security
- Dynamic agent configuration with unique tokens
- Geo-based load balancing
- Enhanced SSH tunneling alternatives

### 4. Advanced Features
- Machine learning for optimal retention policy suggestions
- Automated cost optimization recommendations
- Enhanced monitoring and alerting capabilities
