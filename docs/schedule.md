# Schedule Component Documentation

## Overview

The schedule component in the Elysium system manages database archiving and export tasks. It provides a framework for scheduling, configuring, and executing database operations using the `db_archive.sh` script. This document explains how the schedule component works and how it integrates with the bash script.

## Architecture

### Components

1. **Frontend Interface**
   - Located in `elysium-frontend/src/components/scheduleDashboard/`
   - Allows users to configure and manage archiving schedules
   - Provides controls to start/pause archiving tasks

2. **Backend API**
   - Located in `elysium-backend/app/Http/Controllers/API/Schedule/`
   - Handles CRUD operations for schedule configuration
   - Processes schedule execution requests

3. **Schedule Processing**
   - Located in `elysium-backend/app/Console/Commands/ScheduleProcessing.php`
   - Executes scheduled tasks based on configured time windows
   - Invokes the `ProcessScheduleController` to handle task execution

4. **Database Models**
   - `ScheduleProcessingList` (table_job_schedule table)
   - `DBJobSchedule` (database_job_schedule table)
   - Stores configuration for archiving tasks

## Schedule Configuration

### Database Schema

The schedule configuration is stored in two main tables:

1. **database_job_schedule**
   - Stores database-level scheduling information
   - Links client databases to their respective servers

2. **table_job_schedule**
   - Stores table-level scheduling details
   - Contains configuration parameters like:
     - Retention days
     - Batch size
     - Time windows for execution
     - S3 backup location

### Configuration Parameters

Key parameters in the `table_job_schedule` table:

| Parameter | Description |
|-----------|-------------|
| `data_retention_days` | Number of days to retain data before archiving |
| `batch_size` | Number of rows to process in each batch |
| `is_partitioned` | Whether the table uses partitioning |
| `archive_day_of_week` | Bitmap of days when archiving should run |
| `archive_start_at_utc` | Start time for the archiving window (UTC) |
| `archive_untill_date_utc` | End time for the archiving window (UTC) |
| `is_active` | Whether the schedule is currently active |

## Execution Flow

### Schedule Trigger Mechanism

The schedule execution is triggered through Laravel's task scheduling system, configured in `elysium-backend/app/Console/Kernel.php`:

```php
protected function schedule(Schedule $schedule): void
{
    $schedule->command('schedule-processing')->everyMinute()->withoutOverlapping();
}
```

This configures the `schedule-processing` command to run every minute, with an overlap prevention mechanism.

### Schedule Processing Command

The `ScheduleProcessing` command (in `elysium-backend/app/Console/Commands/ScheduleProcessing.php`) is the entry point for schedule execution:

```php
public function handle()
{
    try
    {
        $start_time=Carbon::now();
        $shedules = new ProcessScheduleController();
        $shedules->getSchedule();
        $end_time=Carbon::now();
        Log::info("schedule-processing function call time:$start_time");
        Log::info("schedule-processing function end time: $end_time");
    }
    catch (\Throwable $th)
    {
        \Illuminate\Support\Facades\Log::info("schedule-processing function error: $th");
    }
}
```

This command invokes the `ProcessScheduleController::getSchedule()` method to process scheduled tasks.

### Schedule Selection Logic

The `ProcessScheduleController::getSchedule()` method:

1. Determines the current day of week
2. Calculates the bit value for today (1 for Monday, 2 for Tuesday, 4 for Wednesday, etc.)
3. Queries the `table_job_schedule` table for schedules that:
   - Have the current day bit set in `archive_day_of_week`
   - Current time is within the configured time window
   - Have `is_active` set to 0 (ready to run)

### Execution of db_archive.sh

When a schedule is selected for execution, the system:

1. Updates the schedule status to "in_progress"
2. Establishes an SSH connection to the remote server
3. Executes the `db_archive.sh` script with appropriate parameters

The execution happens in `ProcessScheduleController.php`:

```php
$script_path = self::SCRIPT_PATH; // Points to ~/remote/schedule_test.sh
$command = "$script_path '$params_string'";
$script_intail_outputs = $ssh->exec($command);
```

The `schedule_test.sh` script is a wrapper that ultimately calls `db_archive.sh` with the configured parameters.

## Integration with db_archive.sh

### Parameter Mapping

The schedule component maps its configuration to `db_archive.sh` parameters:

| Schedule Parameter | db_archive.sh Parameter |
|-------------------|-------------------------|
| `database_host` | `-h [host]` |
| `database_name` | `-d [database]` |
| `table_name` | `-t [table]` |
| `batch_size` | Used in SQL queries |
| `data_retention_days` | Used to calculate retention date |

### SSH Connection

The system connects to remote servers using SSH:

1. Uses the SSH key stored at the path defined in `constant.php` (`ssh_keys/id_rsa`)
2. Establishes connection to the server where `db_archive.sh` is installed
3. Executes the script with parameters derived from the schedule configuration

### Status Tracking

After execution, the system:

1. Captures the output from the script execution
2. Updates the schedule status based on the execution result
3. Logs the execution details for auditing and troubleshooting

## Cron Jobs

The system uses two levels of scheduling:

1. **Laravel Scheduler**
   - Configured in `elysium-backend/app/Console/Kernel.php`
   - Runs the `schedule-processing` command every minute
   - This is the primary scheduler that checks for tasks to execute

2. **System Cron Job**
   - A system-level cron job must be configured to run Laravel's scheduler
   - Typically set up as:
     ```
     * * * * * cd /path/to/elysium-backend && php artisan schedule:run >> /dev/null 2>&1
     ```
   - This ensures Laravel's scheduler runs every minute

The Laravel scheduler is responsible for determining which tasks should run based on the configured schedules, while the system cron job ensures that the Laravel scheduler itself runs regularly.

## Workflow Summary

1. System cron job triggers Laravel's scheduler every minute
2. Laravel scheduler runs the `schedule-processing` command
3. `ScheduleProcessing` command invokes `ProcessScheduleController::getSchedule()`
4. `getSchedule()` identifies schedules that should run based on day and time
5. For each eligible schedule, the system:
   - Updates status to "in_progress"
   - Connects to the remote server via SSH
   - Executes `db_archive.sh` with appropriate parameters
   - Updates status based on execution result

This multi-layered approach ensures that database archiving tasks run at the configured times without manual intervention.