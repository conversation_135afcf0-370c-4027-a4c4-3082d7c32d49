# Database Archiving Script Documentation

## Overview

The `db_archive.sh` script automates the process of archiving database tables. It identifies data older than a specified retention period, exports it to TSV files, uploads these files to AWS S3, and then removes the data from the source database. The script supports both partitioned and non-partitioned tables with different archiving strategies.

## Usage

The script can be run in three different modes:

1. **Table Mode**: Archive a specific table
   ```
   ./db_archive.sh -h [host] -d [database] -t [table]
   ```

2. **Schema Mode**: Archive all tables in a database
   ```
   ./db_archive.sh -h [host] -d [database]
   ```

3. **Host Mode**: Archive all tables across all databases on a host
   ```
   ./db_archive.sh -h [host]
   ```

### Optional Parameters

- `-c [column]`: Specify a custom date column (default: `created_at`)
- `-n`: No delete mode - prevents deletion of thread files

## Functions

| Function | Description |
|----------|-------------|
| `log()` | Logs messages with timestamp and severity level |
| `usage()` | Displays script usage information |
| `mysql_cmd()` | Executes MySQL commands using login paths |
| `fail_mail()` | Sends failure notifications via email and Slack |
| `unhandled_err()` | Trap handler for unhandled errors |
| `cap_threads()` | Checks if thread limit has been reached |

## Workflow

1. **Initialization**
   - Parse command-line arguments
   - Determine archiving mode (table, schema, or host)
   - Validate inputs

2. **Query Configuration**
   - Query the `archival_info` database to get tables for archiving
   - Retrieve configuration parameters (retention period, batch size, etc.)

3. **Multi-threaded Processing**
   - Process each table in a separate thread (up to `ARCHIVE_THREAD_LIMIT`)
   - Create a unique table ID for tracking

4. **Data Export**
   - Query the database to find rows older than the retention period
   - Export data in batches to TSV files
   - Validate row counts between database and exported file

5. **S3 Upload**
   - Upload TSV file to S3 with a `.part` suffix
   - Verify file size matches between local and S3
   - Rename from `.part` to final filename
   - Delete local TSV file

6. **Data Deletion**
   - For non-partitioned tables:
     - Delete data in batches using the same ID range as export
     - Verify deletion was successful
   
   - For partitioned tables:
     - Create/verify standby table
     - Swap active table with standby
     - Drop partitions containing old data
     - Swap tables back
     - Fill active table with data from standby
     - Truncate standby table

7. **Cleanup and Reporting**
   - Remove thread tracking files
   - Log total processing time

## Database Connectivity

The script connects to databases using MySQL login paths, which are pre-configured authentication credentials stored securely on the system. This approach avoids storing passwords in the script.

- **Archival Info Database**: Uses `ARCHIVAL_INFO_PATH_LOGIN` to connect to the configuration database
- **Target Databases**: Uses the hostname provided via command line to connect to target databases

## Key Components

### Thread Management

The script uses a temporary directory to track running threads:
- Creates a file for each thread
- Checks thread count before starting new threads
- Waits if thread limit is reached
- Removes thread file upon completion

### Error Handling

- Uses bash's `set -Eeu` for strict error handling
- Implements trap for unhandled errors
- Sends notifications via email and Slack on failures
- Logs detailed error information

### S3 Integration

- Uploads files to the configured S3 bucket
- Uses a `.part` suffix during upload
- Implements retry logic for size verification
- Moves file to final name only after verification

### Partitioned Table Handling

For partitioned tables, the script uses a table-swapping technique:
1. Creates a standby table with the same structure
2. Swaps the active table with the standby
3. Drops partitions from the old table (now standby)
4. Swaps tables back
5. Ensures AUTO_INCREMENT values are properly managed

## Configuration Constants

| Constant | Description |
|----------|-------------|
| `ARCHIVE_DIR` | Directory for temporary TSV files |
| `ARCHIVE_THREAD_LIMIT` | Maximum concurrent threads |
| `S3_BUCKET` | S3 bucket for archived data |
| `EMAIL_TO` | Recipients for failure notifications |
| `WEBHOOK_URL` | Slack webhook for notifications |
| `ARCHIVAL_INFO_PATH_LOGIN` | MySQL login path for config DB |
| `ARCHIVAL_INFO_TABLE_NAME` | Table containing archival configuration |
| `S3_TRY_MAX` | Maximum retry attempts for S3 operations |

## Security Considerations

- Uses MySQL login paths instead of hardcoded credentials
- Implements strict error handling
- Validates data before and after operations
- Sends notifications on failures


## Monitoring and Logging

### Logging System

The script implements a comprehensive logging system that tracks all operations:

1. **Structured Logging**
   - Each log entry includes timestamp, severity level, and message
   - When processing a specific table, the table ID is included in logs
   - Severity levels include INFO, WARN, ERROR, and CRIT

2. **Log File Management**
   - Detects if output is redirected to a file
   - Records the log file path for reference in error notifications
   - Example: `log INFO "Log File: ${log_file}"`

3. **Performance Tracking**
   - Logs the start and completion of major operations
   - Records total execution time at the end of the script
   - Example: `log INFO "Processing took: ${archival_time}"`

### Notification System

The script implements two parallel notification channels for monitoring:

1. **Email Notifications**
   - Sends detailed error reports to configured email recipients
   - Includes log file as attachment when available
   - Recipients configured via `EMAIL_TO` constant
   - Example: `mail -s "${subject}" -A ${log_file} "${EMAIL_TO}"`

2. **Slack Integration**
   - Posts real-time alerts to a Slack channel via webhook
   - Formats messages with appropriate highlighting and code blocks
   - Includes table ID and log file reference
   - Webhook URL configured via `WEBHOOK_URL` constant

### Error Handling and Monitoring

1. **Trap-based Error Catching**
   - Uses bash trap mechanism to catch unhandled errors
   - Automatically sends notifications for unexpected failures
   - Example: `trap unhandled_err EXIT`

2. **Validation Checkpoints**
   - Verifies row counts match between database and exported file
   - Confirms S3 upload size matches local file size
   - Validates successful deletion of archived data
   - Sends notifications when validations fail

3. **Process Monitoring**
   - Tracks thread count to prevent resource exhaustion
   - Implements retry logic with configurable limits
   - Logs detailed information about each processing step

### Integration with External Monitoring

The script's logging and notification system can integrate with external monitoring solutions:

1. **Log Aggregation**
   - Log files can be collected by log aggregation tools
   - Structured format facilitates parsing and analysis
   - Timestamp and severity level enable filtering and alerting

2. **Webhook Extensions**
   - The Slack webhook mechanism can be extended to other systems
   - Additional webhooks can be added for specialized monitoring platforms
   - JSON payload structure allows for rich data transmission

3. **Exit Codes**
   - Script uses meaningful exit codes to indicate success or failure
   - External monitoring can track exit codes for automated alerting
   - Non-zero exit codes trigger immediate notifications