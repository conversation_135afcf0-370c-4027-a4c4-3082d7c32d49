Future improvements
Queue requests 
Implement a queue system for archive schedule, db info update, bastion connection and agent setup.

Schedule decoupling
Instead of relying on Laravel schedules to execute archive commands on the bastion server, create a decoupled microservice to handle the queued process in a separate instance (or ECS container) and report back to backend stats db.

SSH tunneling alternative

Dynamic agent config
Instead of storing api endpoint url in bastion side, send the endpoint url in the agent arguments every time it’s triggered. 
Will be helpful for increasing security by adding a unique token for each request
Can be extended to use different instances to handle the responses with custom config for load balancing. E.g. users segmentation over geo-based instances 
