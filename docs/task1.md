1- Read documention in @docs folder
2- create a comprehensive documentation of the app architecture from architecture_draft1.md that contains all infromation about listed components and their interactions
3- create a sequence diagram for the app architecture
4- create a component diagram for the app architecture




|---------------------|--------------------------|
| `archive_day_of_week` | `--day-of-week` |
| `archive_start_at_utc` | `--start-time` |
- Executes the `db_archive.sh` script on the bastion server
- Passes parameters like host, database, table, and date column
| `archive_end_at_utc` | `--end-time` |
- Handles the response from `db_archive.sh` to update the schedule status and stats
| `archive_batch_size` | `--batch-size` |
| `archive_retention_days` | `--retention-days` |
### Error Handling
The schedule component handles errors by:
- Logging errors to the database
- Sending notifications to the database owner
- Updating the schedule status to failed

## App Architecture Documentation
This documentation provides a comprehensive overview of the app architecture, detailing the components involved, their interactions, and the overall workflow of the system.
## Components
### 1. App
The main application that orchestrates the database archiving process, manages schedules, and interacts with the
backend API.
### 2. Backend API
The backend API handles authentication, subscriptions, and database operations. It includes scheduled tasks for database archiving.
### 3. Database
The database stores configuration parameters, schedule information, and archival data. It is accessed through the bastion server.
### 4. Bastion Server
The bastion server acts as a gateway to connect to remote database servers. It runs the `db_archive.sh` script to archive data.
### 5. Remote Database Servers
The remote database servers contain the data to be archived. They are accessed through the bastion server.
### 6. AWS S3
AWS S3 is used as the object storage to store the archived data.
