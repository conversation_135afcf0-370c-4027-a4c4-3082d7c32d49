# Elysium System Component Diagrams

## High-Level System Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        UI[Web Frontend<br/>React/Vue.js]
    end
    
    subgraph "Application Layer"
        API[Backend API<br/>Laravel PHP]
        AUTH[Authentication<br/>Service]
        SCHED[Schedule<br/>Processor]
    end
    
    subgraph "Data Layer"
        CONFIG_DB[(Configuration<br/>Database)]
        STATS_DB[(Statistics<br/>Database)]
    end
    
    subgraph "Infrastructure Layer"
        BASTION[Bastion Server<br/>SSH Gateway]
        AGENT[Archive Agent<br/>Bash Scripts]
    end
    
    subgraph "External Systems"
        REMOTE_DB[(Remote Database<br/>Servers)]
        S3[AWS S3<br/>Object Storage]
        EMAIL[Email<br/>Service]
        SLACK[Slack<br/>Webhooks]
    end
    
    UI --> API
    API --> AUTH
    API --> SCHED
    API --> CONFIG_DB
    API --> STATS_DB
    SCHED --> BASTION
    BASTION --> AGENT
    AGENT --> REMOTE_DB
    AGENT --> S3
    AGENT --> EMAIL
    AGENT --> SLACK
    AGENT --> API
```

## Detailed Component Architecture

```mermaid
graph TB
    subgraph "Frontend Components"
        DASH[Schedule Dashboard]
        CONFIG[Database Config]
        MONITOR[Archive Monitor]
        COST[Cost Analysis]
        AUTH_UI[Authentication UI]
    end
    
    subgraph "Backend API Components"
        subgraph "Controllers"
            SCHEDULE_CTRL[Schedule Controller]
            DB_CTRL[Database Controller]
            AUTH_CTRL[Auth Controller]
            STATS_CTRL[Statistics Controller]
        end
        
        subgraph "Services"
            SSH_SVC[SSH Service]
            COST_SVC[Cost Calculator]
            NOTIFY_SVC[Notification Service]
        end
        
        subgraph "Models"
            SCHEDULE_MODEL[Schedule Models]
            DB_MODEL[Database Models]
            USER_MODEL[User Models]
        end
    end
    
    subgraph "Console Commands"
        SCHEDULE_CMD[Schedule Processing<br/>Command]
        CLEANUP_CMD[Cleanup Command]
    end
    
    subgraph "Database Schema"
        subgraph "Configuration Tables"
            DB_SCHEDULE[database_job_schedule]
            TABLE_SCHEDULE[table_job_schedule]
            ARCHIVAL_INFO[archival_info]
        end
        
        subgraph "Statistics Tables"
            DB_STATS[db_stats]
            TABLE_STATS[table_stats]
            COST_STATS[cost_savings]
        end
        
        subgraph "User Management"
            USERS[users]
            SESSIONS[sessions]
        end
    end
    
    DASH --> SCHEDULE_CTRL
    CONFIG --> DB_CTRL
    MONITOR --> STATS_CTRL
    COST --> COST_SVC
    AUTH_UI --> AUTH_CTRL
    
    SCHEDULE_CTRL --> SCHEDULE_MODEL
    DB_CTRL --> DB_MODEL
    AUTH_CTRL --> USER_MODEL
    STATS_CTRL --> DB_STATS
    
    SCHEDULE_CMD --> SCHEDULE_CTRL
    SSH_SVC --> SCHEDULE_CTRL
    COST_SVC --> COST_STATS
    
    SCHEDULE_MODEL --> DB_SCHEDULE
    SCHEDULE_MODEL --> TABLE_SCHEDULE
    DB_MODEL --> ARCHIVAL_INFO
    USER_MODEL --> USERS
```

## Agent System Components

```mermaid
graph TB
    subgraph "Bastion Server"
        subgraph "Agent Scripts"
            DB_ARCHIVE[db_archive.sh<br/>Main Archive Script]
            DB_INFO[db_info.sh<br/>Database Discovery]
            SCHEDULE_TEST[schedule_test.sh<br/>Schedule Wrapper]
        end
        
        subgraph "Agent Functions"
            LOG_FUNC[Logging System]
            MYSQL_FUNC[MySQL Commands]
            THREAD_FUNC[Thread Management]
            ERROR_FUNC[Error Handling]
            MAIL_FUNC[Notification System]
        end
        
        subgraph "Configuration"
            SSH_KEYS[SSH Keys]
            MYSQL_PATHS[MySQL Login Paths]
            ENV_CONFIG[Environment Config]
        end
    end
    
    subgraph "Processing Components"
        SIZE_CALC[Size Calculator]
        EXPORT_ENGINE[Export Engine]
        DELETE_ENGINE[Delete Engine]
        S3_UPLOADER[S3 Uploader]
        COMPRESSOR[Data Compressor]
    end
    
    subgraph "External Integrations"
        REMOTE_MYSQL[(Remote MySQL<br/>Servers)]
        AWS_S3[AWS S3<br/>Storage]
        EMAIL_SMTP[SMTP Email<br/>Server]
        SLACK_API[Slack Webhook<br/>API]
    end
    
    DB_ARCHIVE --> LOG_FUNC
    DB_ARCHIVE --> MYSQL_FUNC
    DB_ARCHIVE --> THREAD_FUNC
    DB_ARCHIVE --> ERROR_FUNC
    
    DB_ARCHIVE --> SIZE_CALC
    DB_ARCHIVE --> EXPORT_ENGINE
    DB_ARCHIVE --> DELETE_ENGINE
    DB_ARCHIVE --> S3_UPLOADER
    DB_ARCHIVE --> COMPRESSOR
    
    MYSQL_FUNC --> MYSQL_PATHS
    ERROR_FUNC --> MAIL_FUNC
    MAIL_FUNC --> EMAIL_SMTP
    MAIL_FUNC --> SLACK_API
    
    EXPORT_ENGINE --> REMOTE_MYSQL
    DELETE_ENGINE --> REMOTE_MYSQL
    S3_UPLOADER --> AWS_S3
    SIZE_CALC --> REMOTE_MYSQL
```

## Data Flow Component Diagram

```mermaid
graph LR
    subgraph "Input Sources"
        USER_INPUT[User Configuration]
        CRON_TRIGGER[Cron Trigger]
        API_REQUESTS[API Requests]
    end
    
    subgraph "Processing Pipeline"
        VALIDATOR[Input Validator]
        SCHEDULER[Task Scheduler]
        EXECUTOR[Task Executor]
        MONITOR[Process Monitor]
    end
    
    subgraph "Data Processors"
        QUERY_PROC[Query Processor]
        EXPORT_PROC[Export Processor]
        UPLOAD_PROC[Upload Processor]
        DELETE_PROC[Delete Processor]
    end
    
    subgraph "Output Destinations"
        CONFIG_STORE[(Configuration<br/>Storage)]
        ARCHIVE_STORE[Archive Storage<br/>S3]
        LOG_STORE[Log Storage]
        NOTIFICATION[Notifications]
    end
    
    USER_INPUT --> VALIDATOR
    CRON_TRIGGER --> SCHEDULER
    API_REQUESTS --> VALIDATOR
    
    VALIDATOR --> CONFIG_STORE
    SCHEDULER --> EXECUTOR
    EXECUTOR --> MONITOR
    
    EXECUTOR --> QUERY_PROC
    QUERY_PROC --> EXPORT_PROC
    EXPORT_PROC --> UPLOAD_PROC
    UPLOAD_PROC --> DELETE_PROC
    
    EXPORT_PROC --> ARCHIVE_STORE
    UPLOAD_PROC --> ARCHIVE_STORE
    DELETE_PROC --> CONFIG_STORE
    
    MONITOR --> LOG_STORE
    MONITOR --> NOTIFICATION
```

## Security Component Architecture

```mermaid
graph TB
    subgraph "Authentication Layer"
        LOGIN[Login Controller]
        TOKEN[Token Manager]
        SESSION[Session Manager]
    end
    
    subgraph "Authorization Layer"
        RBAC[Role-Based Access Control]
        PERMISSIONS[Permission Manager]
        MIDDLEWARE[Auth Middleware]
    end
    
    subgraph "Secure Communication"
        SSH_CLIENT[SSH Client]
        TLS[TLS/SSL Handler]
        ENCRYPTION[Data Encryption]
    end
    
    subgraph "Credential Management"
        KEY_STORE[SSH Key Store]
        LOGIN_PATHS[MySQL Login Paths]
        ENV_SECRETS[Environment Secrets]
    end
    
    subgraph "Audit and Monitoring"
        AUDIT_LOG[Audit Logger]
        SECURITY_MONITOR[Security Monitor]
        INTRUSION_DETECT[Intrusion Detection]
    end
    
    LOGIN --> TOKEN
    TOKEN --> SESSION
    SESSION --> RBAC
    RBAC --> PERMISSIONS
    PERMISSIONS --> MIDDLEWARE
    
    SSH_CLIENT --> KEY_STORE
    TLS --> ENCRYPTION
    ENCRYPTION --> ENV_SECRETS
    
    MIDDLEWARE --> AUDIT_LOG
    SSH_CLIENT --> SECURITY_MONITOR
    SECURITY_MONITOR --> INTRUSION_DETECT
```

## Monitoring and Logging Components

```mermaid
graph TB
    subgraph "Log Collection"
        APP_LOGS[Application Logs]
        AGENT_LOGS[Agent Logs]
        SYSTEM_LOGS[System Logs]
        ERROR_LOGS[Error Logs]
    end
    
    subgraph "Log Processing"
        LOG_PARSER[Log Parser]
        LOG_FILTER[Log Filter]
        LOG_AGGREGATOR[Log Aggregator]
    end
    
    subgraph "Monitoring Services"
        PERF_MONITOR[Performance Monitor]
        HEALTH_CHECK[Health Checker]
        ALERT_MANAGER[Alert Manager]
    end
    
    subgraph "Notification Channels"
        EMAIL_ALERTS[Email Alerts]
        SLACK_ALERTS[Slack Alerts]
        DASHBOARD_ALERTS[Dashboard Alerts]
    end
    
    subgraph "Storage and Analytics"
        LOG_DATABASE[(Log Database)]
        METRICS_STORE[(Metrics Store)]
        ANALYTICS[Analytics Engine]
    end
    
    APP_LOGS --> LOG_PARSER
    AGENT_LOGS --> LOG_PARSER
    SYSTEM_LOGS --> LOG_PARSER
    ERROR_LOGS --> LOG_PARSER
    
    LOG_PARSER --> LOG_FILTER
    LOG_FILTER --> LOG_AGGREGATOR
    LOG_AGGREGATOR --> LOG_DATABASE
    
    PERF_MONITOR --> METRICS_STORE
    HEALTH_CHECK --> ALERT_MANAGER
    ALERT_MANAGER --> EMAIL_ALERTS
    ALERT_MANAGER --> SLACK_ALERTS
    ALERT_MANAGER --> DASHBOARD_ALERTS
    
    LOG_DATABASE --> ANALYTICS
    METRICS_STORE --> ANALYTICS
```
