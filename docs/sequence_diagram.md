# Elysium System Sequence Diagrams

## Database Discovery and Configuration Sequence

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Backend
    participant BastionServer
    participant RemoteDB
    participant S3

    User->>Frontend: Configure database connection
    Frontend->>Backend: POST /api/database/configure
    Backend->>BastionServer: SSH connect and execute db_info.sh
    BastionServer->>RemoteDB: Query database structure
    RemoteDB-->>BastionServer: Return table/column metadata
    BastionServer->>Backend: POST /api/get_db_response (database info)
    Backend->>Backend: Store database/table metadata
    Backend-->>Frontend: Configuration success response
    Frontend-->>User: Display database structure
```

## Schedule Execution Sequence

```mermaid
sequenceDiagram
    participant CronJob
    participant LaravelScheduler
    participant ScheduleProcessor
    participant Backend
    participant BastionServer
    participant Agent
    participant RemoteDB
    participant S3
    participant NotificationSystem

    CronJob->>LaravelScheduler: Trigger every minute
    LaravelScheduler->>ScheduleProcessor: Execute schedule-processing command
    ScheduleProcessor->>Backend: ProcessScheduleController.getSchedule()
    Backend->>Backend: Query eligible schedules (day/time/status)
    
    loop For each eligible schedule
        Backend->>Backend: Update status to 'in_progress'
        Backend->>BastionServer: SSH connect and execute schedule_test.sh
        BastionServer->>Agent: Execute db_archive.sh with parameters
        
        Agent->>RemoteDB: Query data older than retention period
        RemoteDB-->>Agent: Return data rows
        Agent->>Agent: Export data to TSV files
        Agent->>Agent: Validate row counts
        Agent->>S3: Upload TSV files (.part suffix)
        S3-->>Agent: Confirm upload
        Agent->>Agent: Verify file size and rename
        Agent->>RemoteDB: Delete archived data
        RemoteDB-->>Agent: Confirm deletion
        
        Agent->>Backend: POST /api/schedule_response (status update)
        Backend->>Backend: Update schedule status and statistics
    end
    
    alt On Error
        Agent->>NotificationSystem: Send email/Slack notification
        Agent->>Backend: POST /api/agentfailure (error details)
        Backend->>NotificationSystem: Notify database owner
    end
```

## Error Handling Sequence

```mermaid
sequenceDiagram
    participant Agent
    participant Backend
    participant EmailSystem
    participant SlackWebhook
    participant DatabaseOwner
    participant AdminTeam

    Agent->>Agent: Detect error during processing
    Agent->>Agent: Log error details with timestamp
    
    par Email Notification
        Agent->>EmailSystem: Send detailed error report with log attachment
        EmailSystem->>AdminTeam: Deliver email notification
    and Slack Notification
        Agent->>SlackWebhook: POST error alert with table ID and log reference
        SlackWebhook->>AdminTeam: Display real-time alert
    end
    
    Agent->>Backend: POST /api/agentfailure with error details
    Backend->>Backend: Log error to database
    Backend->>Backend: Update schedule status to 'failed'
    Backend->>EmailSystem: Send notification to database owner
    EmailSystem->>DatabaseOwner: Deliver failure notification
```

## Data Archival Process Sequence

```mermaid
sequenceDiagram
    participant Agent
    participant RemoteDB
    participant LocalFS
    participant S3
    participant Backend

    Agent->>RemoteDB: Query: SELECT COUNT(*) WHERE date < retention_date
    RemoteDB-->>Agent: Return row count for validation
    
    loop Process in batches
        Agent->>RemoteDB: SELECT * WHERE date < retention_date LIMIT batch_size
        RemoteDB-->>Agent: Return batch data
        Agent->>LocalFS: Write data to TSV file
        Agent->>Agent: Validate exported row count
    end
    
    Agent->>S3: Upload TSV file with .part suffix
    S3-->>Agent: Return upload confirmation
    Agent->>Agent: Verify file size matches
    Agent->>S3: Rename file (remove .part suffix)
    Agent->>LocalFS: Delete local TSV file
    
    alt Non-partitioned table
        Agent->>RemoteDB: DELETE WHERE date < retention_date (in batches)
        RemoteDB-->>Agent: Confirm deletion
    else Partitioned table
        Agent->>RemoteDB: Create standby table
        Agent->>RemoteDB: Swap active with standby
        Agent->>RemoteDB: Drop old partitions
        Agent->>RemoteDB: Swap tables back
        Agent->>RemoteDB: Fill active table from standby
        Agent->>RemoteDB: Truncate standby table
    end
    
    Agent->>Backend: POST /api/schedule_response with completion status
    Backend->>Backend: Update statistics and schedule status
```

## Cost Calculation Sequence

```mermaid
sequenceDiagram
    participant Backend
    participant Database
    participant CostCalculator
    participant S3PricingAPI
    participant RDSPricingAPI
    participant Frontend

    Backend->>Database: Query archived data volume
    Database-->>Backend: Return total archived size
    Backend->>CostCalculator: Calculate savings request
    
    par Get Storage Costs
        CostCalculator->>S3PricingAPI: Get S3 storage rates
        S3PricingAPI-->>CostCalculator: Return S3 pricing
    and Get Database Costs
        CostCalculator->>RDSPricingAPI: Get RDS storage rates
        RDSPricingAPI-->>CostCalculator: Return RDS pricing
    end
    
    CostCalculator->>CostCalculator: Calculate savings (RDS cost - S3 cost)
    CostCalculator->>CostCalculator: Apply 70% compression factor
    CostCalculator-->>Backend: Return calculated savings
    Backend->>Database: Store cost savings data
    Backend-->>Frontend: Return savings report
```

## User Authentication and Authorization Sequence

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Backend
    participant Database
    participant AuthService

    User->>Frontend: Enter login credentials
    Frontend->>Backend: POST /api/auth/login
    Backend->>AuthService: Validate credentials
    AuthService->>Database: Query user credentials
    Database-->>AuthService: Return user data
    AuthService-->>Backend: Authentication result
    
    alt Successful Authentication
        Backend->>Backend: Generate session token
        Backend-->>Frontend: Return token and user data
        Frontend->>Frontend: Store token in session
        Frontend-->>User: Redirect to dashboard
    else Authentication Failed
        Backend-->>Frontend: Return error message
        Frontend-->>User: Display login error
    end
    
    Note over Frontend,Backend: Subsequent requests include token
    Frontend->>Backend: API request with Authorization header
    Backend->>AuthService: Validate token
    AuthService-->>Backend: Token validation result
    Backend-->>Frontend: API response or 401 Unauthorized
```
