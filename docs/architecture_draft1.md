App
DB connectivity
Db_info.sh
Query db through bastion server where agent scripts installed
Post db info to backend server /api/get_db_response
Create db instance record and tables record (db->table->columns)
Key parameters:
Table primary column
Table retention days (defualt=60)
Table date column (created_at/last_update)




DB stats
Tracks db size, archival sizes, and last archived table id
Db severe stats
Db table stats

Schedule
Schedules table archival with retention, batch size, and s3 destinations
Executes using Laravel schedule and cron job
Scheduled triggered agent on the bastion server, passing the archival process parameters and tracks the archival process status 
Agent post archival process back to /api/schedule_response, then updates the stats tables for the related archival table

Bastion server connectivity
System uses SSH key stored at the bastion server in the setup phase to connect using ssh tunneling 
After the connection is established to the bastion server, system executes the scripts for running db_info and db_archive with passing process parameters.


Cost savings
Calculate estimated savings amount after db archival to s3 compressed files
Using s3 and rds rates
Assuming compression rate of %70 for archived files to s3

Agent
Size calc
Export/Archive
Delete Parent/child
S3 integration
Compression
Error handling

Backend notifications
Agent posts errors to /api/agentfailure
Backed uses email to dispatch failure notification db owner

API
