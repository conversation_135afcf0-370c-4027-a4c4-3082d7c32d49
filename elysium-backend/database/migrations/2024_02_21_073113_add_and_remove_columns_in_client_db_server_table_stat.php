<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('client_db_server_table_stat', function (Blueprint $table)
        {
            $table->dropColumn('db_name');
            $table->dropColumn('table_name');
            $table->unsignedBigInteger('client_db_server_table_id')->after('client_db_server_id');
            $table->foreign('client_db_server_table_id')->references('id')->on('client_db_server_table')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('client_db_server_table_stat', function (Blueprint $table)
        {
            $table->dropForeign(['client_db_server_table_id']);
            $table->dropColumn('client_db_server_table_id');
            $table->string('db_name', 64)->default('');
            $table->string('table_name', 100);
        });
    }
};
