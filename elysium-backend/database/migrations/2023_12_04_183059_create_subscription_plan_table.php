<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_plan', function (Blueprint $table) {
            $table->id();
			$table->string('plan_name');
			$table->unsignedBigInteger('subscription_type_id');
			$table->foreign('subscription_type_id')->references('id')->on('subscription_type')->onDelete('cascade');
			$table->string('plan_price')->default(0);
			$table->string('plan_price_effect_date')->nullable();
			$table->string('plan_price_end_date')->nullable();
			$table->string('table_plan_limit')->default(0);
			$table->string('price_per_table')->nullable();
			$table->json('key_benefits')->nullable();
			$table->string('stripe_plan', 50)->nullable();
			$table->boolean('is_active')->default(0);
			$table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_plan');
    }
};
