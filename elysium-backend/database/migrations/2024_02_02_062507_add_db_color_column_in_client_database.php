<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('client_database', function (Blueprint $table) {
            $table->string('color_code', 100)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('client_database', function (Blueprint $table) {
            if (Schema::hasColumn('client_database', 'color_code'))
            {
                $table->dropColumn('color_code');
            }
        });
    }
};
