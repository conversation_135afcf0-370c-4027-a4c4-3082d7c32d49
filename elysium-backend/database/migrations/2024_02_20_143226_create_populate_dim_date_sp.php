<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        $sql = "
        CREATE PROCEDURE PopulateDimDate(start_date DATE, end_date DATE)
        BEGIN
            DECLARE p_current_date DATE;
            SET p_current_date = start_date;
        
            WHILE p_current_date <= end_date DO
                INSERT INTO dim_date (date, day_name,week_number,week_name, month_number, month_name, quarter, year, is_deleted, created_at, updated_at)
                VALUES (
                    p_current_date,
                    DAYNAME(p_current_date),
                    WEEK(p_current_date, 1),
                    concat('Week ',WEEK(p_current_date, 1)),
                    MONTH(p_current_date),
                    MONTHNAME(p_current_date),
                    concat('Q',QUARTER(p_current_date)),
                    YEAR(p_current_date),
                    0,
                    NOW(),
                    NOW()
                );
                SET p_current_date = DATE_ADD(p_current_date, INTERVAL 1 DAY);
            END WHILE;
        END
        ";

        DB::unprepared($sql);
    }

    public function down()
    {
        $sql = "DROP PROCEDURE IF EXISTS PopulateDimDate;";
        DB::unprepared($sql);
    }
};
