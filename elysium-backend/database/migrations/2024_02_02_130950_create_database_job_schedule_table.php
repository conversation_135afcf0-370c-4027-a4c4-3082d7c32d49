<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('database_job_schedule', function (Blueprint $table)
        {
            $table->id();
            $table->unsignedBigInteger('client_db_server_id');
            $table->foreign('client_db_server_id')->references('id')->on('client_db_server')->onDelete('cascade');
            $table->unsignedBigInteger('client_database_id');
            $table->foreign('client_database_id')->references('id')->on('client_database')->onDelete('cascade');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
		DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        Schema::dropIfExists('database_job_schedule');
		DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    }
};
