<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dim_date', function (Blueprint $table)
        {
            $table->id();
            $table->date('date');
            $table->tinyInteger('week_number');
            $table->string('week_name', 25);
            $table->string('month_number', 15);
            $table->string('month_name', 25)->nullable();
            $table->char('quarter', 2);
            $table->smallInteger('year');
            $table->tinyInteger('is_deleted')->default(0);
            $table->timestamps();
            $table->softDeletes();

            $table->unique('date', 'unq_dim_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dim_date');
    }
};
