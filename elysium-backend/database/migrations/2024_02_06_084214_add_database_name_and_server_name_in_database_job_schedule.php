<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('database_job_schedule', function (Blueprint $table)
        {
            $table->string('server_name')->after('client_db_server_id');
            $table->string('database_name')->after('server_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('database_job_schedule', function (Blueprint $table)
        {
            $table->dropColumn('database_name');
            $table->dropColumn('server_name');
        });
    }
};
