<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('table_job_schedule', function (Blueprint $table)
        {
            $table->unsignedBigInteger('database_job_schedule_id')->after('client_db_server_table_id');
            $table->foreign('database_job_schedule_id')->references('id')->on('database_job_schedule')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('table_job_schedule', function (Blueprint $table)
        {
            $table->dropForeign(['database_job_schedule_id']);
            $table->dropColumn('database_job_schedule_id');
        });
    }
};
