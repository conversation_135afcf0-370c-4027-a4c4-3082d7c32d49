<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('table_job_schedule', function (Blueprint $table) {
            // Change the foreign key reference without dropping the existing foreign key constraint
            $table->dropForeign(['client_db_server_table_id']);

            $table->foreign('client_db_server_table_id')->references('id')->on('client_db_server_table')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
