<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('company', function (Blueprint $table) {
            $table->id();
			$table->string('company_name')->unique();
			$table->text('company_address');
			$table->text('company_address2')->nullable();
			$table->string('company_city');
			$table->string('company_postalcode');
			$table->unsignedBigInteger('country_id');
			$table->foreign('country_id')->references('id')->on('country')->onDelete('cascade');
			$table->unsignedBigInteger('state_id');
			$table->foreign('state_id')->references('id')->on('state')->onDelete('cascade');
			$table->unsignedBigInteger('database_server_option_id');
			$table->foreign('database_server_option_id')->references('id')->on('database_server_option')->onDelete('cascade');
			$table->unsignedBigInteger('database_table_option_id');
			$table->foreign('database_table_option_id')->references('id')->on('database_table_option')->onDelete('cascade');
			$table->boolean('is_active')->default(0);
			$table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('company');
    }
};
