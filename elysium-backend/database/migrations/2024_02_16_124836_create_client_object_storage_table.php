<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('client_object_storage', function (Blueprint $table) {
            $table->id();
			$table->string('bucket_name', 100);
			$table->unsignedTinyInteger('object_storage_type_id');
			$table->foreign('object_storage_type_id')->references('id')->on('object_storage_type')->onDelete('cascade');
			$table->unsignedBigInteger('company_id');
			$table->foreign('company_id')->references('id')->on('company')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('client_object_storage');
    }
};
