<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('client_db_server_table', function (Blueprint $table) {

			DB::statement('SET FOREIGN_KEY_CHECKS=0;');

			// DROP COLUMNS
			if (Schema::hasColumn('client_db_server_table', 'auto_increment')) {
				$table->dropColumn('auto_increment');
			}

			if (Schema::hasColumn('client_db_server_table', 'create_time')) {
				$table->dropColumn('create_time');
			}

			if (Schema::hasColumn('client_db_server_table', 'update_time')) {
				$table->dropColumn('update_time');
			}

			// ADD NEW COLUMNS

			if (!Schema::hasColumn('client_db_server_table', 'client_database_id')) {
				$table->unsignedBigInteger('client_database_id');
				$table->foreign('client_database_id')->references('id')->on('client_database')->onDelete('cascade');
			}

			if (!Schema::hasColumn('client_db_server_table', 'client_db_server_id')) {
				$table->unsignedBigInteger('client_db_server_id');
				$table->foreign('client_db_server_id')->references('id')->on('client_db_server')->onDelete('cascade');
			}

			if (!Schema::hasColumn('client_db_server_table', 'table_process_status_id')) {
				$table->enum('table_process_status_id', ['1', '2', '3'])->default(1)->comment('1=No Action, 2=Export, 3=Archive');
			}

			if (!Schema::hasColumn('client_db_server_table', 'total_current_table_rows')) {
				$table->integer('total_current_table_rows')->unsigned()->default(0);
			}

			if (!Schema::hasColumn('client_db_server_table', 'has_reference_integrity')) {
				$table->integer('has_reference_integrity')->unsigned()->default(0)->comment('The table has referential integrity, which could affect archiving.');
			}

			DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('client_db_server_table', function (Blueprint $table) {
			if (!Schema::hasColumn('client_db_server_table', 'auto_increment')) {
				$table->integer('auto_increment')->unsigned()->nullable();
			}
			if (!Schema::hasColumn('client_db_server_table', 'create_time')) {
				$table->timestamp('create_time')->nullable()->default(null);
			}
			if (!Schema::hasColumn('client_db_server_table', 'update_time')) {
				$table->timestamp('update_time')->nullable()->default(null);
			}
        });
    }
};
