<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('table_job_schedule_agent_logs', function (Blueprint $table) {
            $table->id();
            // $table->unsignedBigInteger('table_job_schedule_id');
            // $table->foreign('table_job_schedule_id')->references('id')->on('table_job_schedule')->onDelete('cascade');
            $table->unsignedBigInteger('table_job_schedule_execution_log_id');
            $table->foreign('table_job_schedule_execution_log_id', 'fk_agent_logs_exec_log')
                ->references('id')
                ->on('table_job_schedule_execution_log')
                ->onDelete('cascade');
            $table->unsignedBigInteger('table_job_schedule_id');
            $table->foreign('table_job_schedule_id', 'fk_agent_logs_schedule')
                ->references('id')
                ->on('table_job_schedule')
                ->onDelete('cascade');
            $table->text('log_message');
            $table->string('log_type');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('table_job_schedule_agent_logs');
    }
};
