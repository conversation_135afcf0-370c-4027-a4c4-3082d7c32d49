<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id');
            $table->string('name');
            $table->string('stripe_id')->unique();
            $table->string('stripe_status');
            $table->string('stripe_price')->nullable();
            $table->integer('quantity')->nullable();
            $table->timestamp('trial_ends_at')->nullable();
            $table->timestamp('ends_at')->nullable();
			$table->softDeletes();
            $table->timestamps();

            $table->index(['user_id', 'stripe_status']);

			// $table->bigIncrements('id');
            // $table->unsignedBigInteger('seller_id');
            // $table->string('name');
            // $table->string('stripe_id');
            // $table->string('stripe_status');
            // $table->string('stripe_plan')->nullable();
            // $table->integer('quantity')->nullable();
            // $table->timestamp('trial_ends_at')->nullable();
            // $table->timestamp('ends_at')->nullable();
            // $table->timestamps();

            // $table->index(['seller_id', 'stripe_status']);

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription');
    }
};
