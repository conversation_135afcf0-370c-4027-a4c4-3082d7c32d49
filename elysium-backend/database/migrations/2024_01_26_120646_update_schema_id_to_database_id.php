<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('client_db_server_table_stat', function (Blueprint $table)
        {
            DB::statement('SET FOREIGN_KEY_CHECKS=0;');

            if (Schema::hasColumn('client_db_server_table_stat', 'client_db_schema_id'))
            {
                $table->dropForeign(['client_db_schema_id']);
                $table->dropColumn('client_db_schema_id');
            }
            if (!Schema::hasColumn('client_db_server_table_stat', 'client_database_id'))
            {
                $table->unsignedBigInteger('client_database_id');
                $table->foreign('client_database_id')->references('id')->on('client_database')->onDelete('cascade');
            }
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('client_db_server_table_stat', function (Blueprint $table)
        {
            if (Schema::hasColumn('client_db_server_table_stat', 'client_db_schema_id'))
            {
				$table->dropForeign(['client_db_schema_id']);
                $table->dropColumn('client_db_schema_id');
            }
            if (Schema::hasColumn('client_db_server_table_stat', 'client_database_id'))
            {
				$table->dropForeign(['client_database_id']);
                $table->dropColumn('client_database_id');
            }
        });
    }
};
