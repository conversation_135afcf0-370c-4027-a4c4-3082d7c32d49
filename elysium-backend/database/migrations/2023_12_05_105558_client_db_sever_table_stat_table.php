<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('client_db_server_table_stat', function (Blueprint $table)
        {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('client_db_server_id');
            $table->unsignedBigInteger('client_db_schema_id');
            $table->unsignedBigInteger('dim_date_id');
            $table->unsignedBigInteger('company_id');
            $table->string('db_name', 64)->default('');
            $table->string('table_name', 100);
            $table->date('stat_date');
            $table->unsignedBigInteger('total_table_rows')->default(0);
            $table->unsignedBigInteger('total_table_rows_archived')->default(0);
            $table->unsignedBigInteger('total_db_storage_setup')->default(0);
            $table->unsignedBigInteger('total_table_size')->default(0);
            $table->unsignedBigInteger('total_table_index')->default(0);
            $table->unsignedDecimal('total_db_storage_setup_cost', 8, 2)->default(0.0);
            $table->unsignedBigInteger('total_table_data_size_archived')->default(0);
            $table->unsignedBigInteger('total_table_object_storage_used')->default(0);
            $table->unsignedDecimal('total_table_storage_cost_saving', 6, 2)->default(0.0);
            $table->tinyInteger('is_deleted')->default(0);
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('dim_date_id')->references('id')->on('dim_date')->onDelete('cascade');
            $table->foreign('client_db_server_id')->references('id')->on('client_db_server')->onDelete('cascade');
            $table->foreign('client_db_schema_id')->references('id')->on('client_db_schema')->onDelete('cascade');
            $table->foreign('company_id')->references('id')->on('company')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('client_db_server_table_stat');
    }
};
