<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('table_job_schedule_execution_log', function (Blueprint $table) {
            $table->renameColumn('datetime', 'execution_datetime');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('table_job_schedule_execution_log', function (Blueprint $table) {
            //
        });
    }
};
