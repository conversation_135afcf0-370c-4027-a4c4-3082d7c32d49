<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('client_db_server_table_column', function (Blueprint $table) {
			if (!Schema::hasColumn('client_db_server_table_column', 'is_deleted')) {
            	$table->tinyInteger('is_deleted')->default(0);
			}

			if (!Schema::hasColumn('client_db_server_table_column', 'deleted_at')) {
				$table->softDeletes();
			}
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('client_db_server_table_column', function (Blueprint $table) {
			if (Schema::hasColumn('client_db_server_table_column', 'deleted_at')) {
				$table->dropColumn('is_deleted');
			}

			if (Schema::hasColumn('client_db_server_table_column', 'deleted_at')) {
				$table->dropSoftDeletes();
			}
        });
    }
};
