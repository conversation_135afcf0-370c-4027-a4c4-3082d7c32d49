<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('client_database', function (Blueprint $table) {
            $table->id();
			$table->unsignedBigInteger('client_db_server_id');
			$table->foreign('client_db_server_id')->references('id')->on('client_db_server')->onDelete('cascade');
			$table->string('db_name', 100);
			$table->integer('total_current_db_size')->unsigned()->default(0);
			$table->integer('total_current_db_data_size')->unsigned()->default(0);
			$table->integer('total_current_db_index_size')->unsigned()->default(0);
			$table->integer('total_table')->unsigned()->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
		DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        Schema::dropIfExists('client_database');
		DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    }
};
