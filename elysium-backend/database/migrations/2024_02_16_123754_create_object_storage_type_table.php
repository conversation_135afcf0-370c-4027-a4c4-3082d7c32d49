<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
		// Delete tables if already exists
		Schema::dropIfExists('object_storage_export_history');
		Schema::dropIfExists('object_storage');
		Schema::dropIfExists('object_storage_type');
		Schema::dropIfExists('cloud_storage_type');

		Schema::create('object_storage_type', function (Blueprint $table) {
			$table->tinyIncrements('id')->unsigned();
			$table->string('object_storage_type', 50)->default('S3 Standard');
			$table->string('cloud_provider_name', 100)->default('AWS');
			$table->tinyInteger('is_active')->default(1);
			$table->timestamps();
		});

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('object_storage_type');
    }
};
