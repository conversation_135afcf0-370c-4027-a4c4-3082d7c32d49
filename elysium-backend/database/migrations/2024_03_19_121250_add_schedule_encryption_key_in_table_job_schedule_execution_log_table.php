<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('table_job_schedule_execution_log', function (Blueprint $table) {
            if(!Schema::hasColumn('table_job_schedule_execution_log', 'schedule_enc_key')){
                $table->string('schedule_enc_key',255)->nullable()->after('day_of_week');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('table_job_schedule_execution_log', function (Blueprint $table) {
            //
        });
    }
};
