<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('table_job_schedule', function (Blueprint $table)
        {
            $table->id();
            $table->unsignedBigInteger('client_db_server_table_id');
            $table->foreign('client_db_server_table_id')->references('id')->on('client_db_server')->onDelete('cascade');
            $table->string('agent_uuid', 250)->nullable();
            $table->string('database_host', 300)->nullable();
            $table->string('database_name', 150)->nullable();
            $table->smallInteger('database_port')->nullable();
            $table->string('schema_name', 100)->nullable();
            $table->string('table_name', 250)->nullable();
            $table->unsignedSmallInteger('retention_days')->nullable();
            $table->string('object_backup_location', 250)->nullable();
            $table->tinyInteger('is_partitioned')->default(0);
            $table->integer('batch_size')->default(10000);
            $table->tinyInteger('is_active')->default(0);
            $table->unsignedTinyInteger('is_current')->default(1);
            $table->unsignedTinyInteger('archive_day_of_week')->default(0);
            $table->timestamp('archive_untill_date_utc')->nullable();
            $table->timestamp('archive_start_at_utc')->nullable();
            $table->string('local_time_zone')->nullable();
            $table->string('s3_file', 255)->nullable();
            $table->integer('total_rows')->default(0);
            $table->string('schedule_type', 64)->default('');
            $table->integer('retention_index')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('table_job_schedule');
    }
};
