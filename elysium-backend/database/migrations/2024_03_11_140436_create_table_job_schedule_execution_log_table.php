<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('table_job_schedule_execution_log', function (Blueprint $table)
        {
            $table->id();
            $table->unsignedBigInteger('table_job_schedule_id');
            $table->foreign('table_job_schedule_id')->references('id')->on('table_job_schedule')->onDelete('cascade');
            $table->string('day_of_week');
            $table->dateTime('datetime')->nullable();
            $table->enum('status', ['awaiting', 'in_progress', 'ssh_connection_failed', 'time_window_expired', 'execution_successfull']);
            $table->json('request_json')->nullable();
            $table->json('response_json')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }
    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('table_job_schedule_execution_log');
    }
};
