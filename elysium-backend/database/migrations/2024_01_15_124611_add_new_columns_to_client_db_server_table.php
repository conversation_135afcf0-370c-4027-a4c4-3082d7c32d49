<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('client_db_server', function (Blueprint $table) {
			if (!Schema::hasColumn('client_db_server', 'timezone')) {
            	$table->string('timezone', 10)->nullable();
			}
			if (!Schema::hasColumn('client_db_server', 'total_current_db_storage_setup_size')) {
				$table->integer('total_current_db_storage_setup_size')->unsigned()->default(1)->comment('Size in GB. Accessible via AWS CLI');
			}
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('client_db_server', function (Blueprint $table) {
			if (Schema::hasColumn('client_db_server', 'timezone')) {
            	$table->dropColumn('timezone');
			}
			if (Schema::hasColumn('client_db_server', 'total_current_db_storage_setup_size')) {
				$table->dropColumn('total_current_db_storage_setup_size');
			}
        });
    }
};
