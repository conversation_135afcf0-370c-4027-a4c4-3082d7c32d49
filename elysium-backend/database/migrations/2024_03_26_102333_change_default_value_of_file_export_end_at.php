<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.object_storage_export_history
     */
    public function up(): void
    {
        Schema::table('object_storage_export_history', function (Blueprint $table) {
            $table->dateTime('file_export_ended_at')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
