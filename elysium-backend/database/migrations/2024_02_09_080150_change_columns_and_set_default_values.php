<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('table_job_schedule', function (Blueprint $table)
        {
            $table->renameColumn('retention_days', 'data_retention_days');
            $table->string('local_time_zone', 35)->default('PST')->change();
            DB::statement('ALTER TABLE `table_job_schedule` CHANGE `database_port` `database_port` SMALLINT NULL AFTER `database_host`;');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('table_job_schedule', function (Blueprint $table)
        {
            $table->renameColumn('data_retention_days', 'retention_days');
            $table->string('local_time_zone')->nullable()->change();
        });
    }
};
