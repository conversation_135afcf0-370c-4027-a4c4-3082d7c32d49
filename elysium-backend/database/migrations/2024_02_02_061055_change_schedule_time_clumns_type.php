<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('table_job_schedule', function (Blueprint $table)
        {
            $table->time('archive_untill_date_utc')->change();
            $table->time('archive_start_at_utc')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('table_job_schedule', function (Blueprint $table)
        {
            $table->datetime('archive_untill_date_utc')->change();
            $table->datetime('archive_start_at_utc')->change();
        });
    }
};
