<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('client_db_server_table_column', function (Blueprint $table)
        {
			$table->id();
			$table->unsignedBigInteger('client_db_server_table_id');
			$table->foreign('client_db_server_table_id')->references('id')->on('client_db_server_table')->onDelete('cascade');
			$table->string('table_schema_name', 64);
			$table->string('table_database_name', 100);
			$table->string('table_name', 64);
			$table->string('column_name', 64);
			$table->integer('ordinal_position')->unsigned()->default(0);
			$table->string('is_nullable', 3)->nullable();
			$table->string('data_type', 64)->nullable();
			$table->integer('character_max_length')->unsigned()->default(0);
			$table->string('column_type', 100)->nullable();
			$table->string('column_key', 3)->nullable();
			$table->string('column_comment', 250)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('client_db_server_table_column');
    }
};
