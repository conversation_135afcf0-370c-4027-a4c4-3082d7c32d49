<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('client_db_server', function (Blueprint $table)
        {
			$table->id();
			$table->unsignedBigInteger('company_id');
			$table->foreign('company_id')->references('id')->on('company')->onDelete('cascade');
			$table->unsignedBigInteger('remote_server_id');
			$table->foreign('remote_server_id')->references('id')->on('remote_server')->onDelete('cascade');
			$table->unsignedBigInteger('client_db_server_type_id');
			$table->foreign('client_db_server_type_id')->references('id')->on('client_db_server_type')->onDelete('cascade');
			$table->unsignedBigInteger('remote_server_status_id');
			$table->foreign('remote_server_status_id')->references('id')->on('remote_server_status')->onDelete('cascade');
			$table->string('db_server_name');
			$table->string('db_server_alias_name')->unique();
			$table->string('client_db_server_uuid')->unique();
			$table->string('hostname');
			$table->string('username');
			$table->string('password');
			$table->string('port');
			$table->boolean('is_active')->default(0);
			$table->boolean('is_deleted')->default(0);
			$table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('client_db_server');
    }
};
