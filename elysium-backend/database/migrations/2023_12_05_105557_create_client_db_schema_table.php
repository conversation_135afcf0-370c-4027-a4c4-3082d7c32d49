<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('client_db_schema', function (Blueprint $table) {
            $table->id();
			$table->unsignedBigInteger('client_db_server_id');
			$table->foreign('client_db_server_id')->references('id')->on('client_db_server')->onDelete('cascade');
			$table->string('db_name');
			$table->integer('total_current_db_storage_setup_size')->unsigned()->default(0);
			$table->integer('total_current_db_size')->unsigned()->default(0);
			$table->integer('total_current_db_data_size')->unsigned()->default(0);
			$table->integer('total_current_db_index_size')->unsigned()->default(0);
			$table->integer('total_tables_count')->unsigned()->default(0);
			$table->string('timezone', 10)->nullable();
			$table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('client_db_schema');
    }
};
