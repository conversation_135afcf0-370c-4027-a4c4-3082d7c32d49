<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('remote_server', function (Blueprint $table) {
			if (!Schema::hasColumn('remote_server', 'is_deleted')) {
                $table->tinyInteger('is_deleted')->default(0);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('remote_server', function (Blueprint $table) {
			if (!Schema::hasColumn('remote_server', 'is_deleted')) {
            	$table->dropColumn('is_deleted');
			}
        });
    }
};
