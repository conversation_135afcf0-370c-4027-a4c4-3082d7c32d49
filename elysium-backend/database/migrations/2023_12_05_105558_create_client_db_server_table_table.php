<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('client_db_server_table', function (Blueprint $table)
        {
            $table->id();
			$table->unsignedBigInteger('client_db_schema_id');
			$table->foreign('client_db_schema_id')->references('id')->on('client_db_schema')->onDelete('cascade');
			$table->string('client_db_server_table_uuid', 36);
			$table->string('table_schema_name', 64)->nullable();
			$table->string('table_database_name', 100);
			$table->string('table_name', 100)->nullable();
			$table->string('table_type', 100)->nullable();
			$table->string('engine', 100)->nullable();
			$table->integer('version')->unsigned()->nullable();
			$table->string('row_format', 10)->nullable();
			$table->integer('total_current_table_rows')->unsigned()->default(0);
			$table->integer('total_current_data_length')->unsigned()->default(0);
			$table->integer('avg_row_length')->unsigned()->nullable();
			$table->integer('max_data_length')->unsigned()->nullable();
			$table->integer('data_free')->unsigned()->nullable();
			$table->integer('auto_increment')->unsigned()->nullable();
			$table->timestamp('create_time')->nullable()->default(null);
			$table->timestamp('update_time')->nullable()->default(null);
			$table->timestamp('check_time')->nullable()->default(null);
			$table->string('table_collation', 32)->nullable();
			$table->integer('checksum')->unsigned()->nullable();
			$table->string('create_options', 64)->nullable();
			$table->text('table_comment', 2048)->nullable();
			$table->string('timezone', 10)->nullable();
			$table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('client_db_server_table');
    }
};
