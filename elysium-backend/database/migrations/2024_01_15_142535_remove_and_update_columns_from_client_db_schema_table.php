<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('client_db_schema', function (Blueprint $table) {
			DB::statement('SET FOREIGN_KEY_CHECKS=0;');

			// DROP COLUMNS

			if (Schema::hasColumn('client_db_schema', 'client_db_server_id')) {
				$table->dropForeign(['client_db_server_id']);
            	$table->dropColumn('client_db_server_id');
			}
			if (Schema::hasColumn('client_db_schema', 'db_name')) {
            	$table->dropColumn('db_name');
			}
			if (Schema::hasColumn('client_db_schema', 'total_current_db_size')) {
				$table->dropColumn('total_current_db_size');
			}
			if (Schema::hasColumn('client_db_schema', 'total_current_db_data_size')) {
				$table->dropColumn('total_current_db_data_size');
			}
			if (Schema::hasColumn('client_db_schema', 'total_current_db_index_size')) {
				$table->dropColumn('total_current_db_index_size');
			}
			if (Schema::hasColumn('client_db_schema', 'total_tables_count')) {
				$table->dropColumn('total_tables_count');
			}
			if (Schema::hasColumn('client_db_schema', 'timezone')) {
				$table->dropColumn('timezone');
			}

			// ADD NEW COLUMNS
			if (!Schema::hasColumn('client_db_schema', 'client_database_id')) {
				$table->unsignedBigInteger('client_database_id');
				$table->foreign('client_database_id')->references('id')->on('client_database')->onDelete('cascade');
			}

			if (!Schema::hasColumn('client_db_schema', 'schema_name')) {
				$table->string('schema_name', 100);
			}

			if (!Schema::hasColumn('client_db_schema', 'is_deleted')) {
				$table->tinyInteger('is_deleted')->default(0);
			}

			DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('client_db_schema', function (Blueprint $table) {
			if (Schema::hasColumn('client_db_schema', 'client_database_id')) {
				$table->dropForeign(['client_database_id']);
				$table->dropColumn('client_database_id');
			}

			if (Schema::hasColumn('client_db_schema', 'schema_name')) {
            	$table->dropColumn('schema_name');
			}

			if (Schema::hasColumn('client_db_schema', 'is_deleted')) {
				$table->dropColumn('is_deleted');
			}
        });
    }
};
