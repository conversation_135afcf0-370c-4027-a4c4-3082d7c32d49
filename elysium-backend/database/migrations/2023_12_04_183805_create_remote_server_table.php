<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('remote_server', function (Blueprint $table) {
            $table->id();
			$table->string('name')->unique();
			$table->unsignedBigInteger('company_id');
			$table->foreign('company_id')->references('id')->on('company')->onDelete('cascade');
			$table->string('hostname')->unique();
			$table->string('username');
			$table->string('port');
			$table->string('agent_uuid')->nullable();
			$table->string('os_type')->nullable();
			$table->unsignedBigInteger('remote_server_status_id');
			$table->foreign('remote_server_status_id')->references('id')->on('remote_server_status')->onDelete('cascade');
			$table->boolean('is_tls_required')->default(0);
			$table->boolean('is_valid')->default(0);
			$table->boolean('is_active')->default(0);
			$table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('remote_server');
    }
};
