<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user', function (Blueprint $table) {
            $table->id();
			$table->string('first_name');
            $table->string('last_name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
			$table->string('otp')->nullable();
			$table->string('password');
			$table->string('image')->nullable();
			$table->boolean('is_trial_active')->default(0);
			$table->unsignedBigInteger('company_id');
			$table->foreign('company_id')->references('id')->on('company')->onDelete('cascade');
			$table->unsignedBigInteger('timezone_id')->nullable();
			$table->foreign('timezone_id')->references('id')->on('timezone')->onDelete('cascade');
			$table->string('stripe_id')->nullable()->index();
            $table->string('pm_type')->nullable();
            $table->string('pm_last_four', 4)->nullable();
            $table->timestamp('trial_ends_at')->nullable();
			$table->boolean('is_active')->default(0);
            $table->rememberToken();
			$table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user');
    }
};
