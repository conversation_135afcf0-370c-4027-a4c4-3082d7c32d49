<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('object_storage_export_history', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('client_db_server_table_id');
			$table->unsignedTinyInteger('object_storage_type_id');
            $table->string('table_name', 100);
            $table->string('file_exported_name', 150)->default('NA');
            $table->string('object_storage_dir_name', 100)->default('NA');
            $table->tinyInteger('is_file_compressed')->default(1);
            $table->unsignedBigInteger('file_export_size');
            $table->unsignedInteger('file_stored_size')->default(0);
            $table->decimal('file_compression_ratio', 5, 2)->default(0.0);
            $table->decimal('file_compression_percentage', 5, 2)->default(0.0);
            $table->unsignedInteger('total_row_exported')->default(0);
            $table->dateTime('file_export_started_at');
            $table->dateTime('file_export_ended_at');
            $table->string('export_status', 15);
            $table->tinyInteger('is_export_error')->default(0);
            $table->string('error_message', 250)->nullable();
            $table->tinyInteger('is_current')->default(1);
            $table->tinyInteger('is_deleted')->default(0);
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('client_db_server_table_id')->references('id')->on('client_db_server_table')->onDelete('cascade');
            $table->foreign('object_storage_type_id')->references('id')->on('object_storage_type')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('object_storage_export_history');
    }
};
