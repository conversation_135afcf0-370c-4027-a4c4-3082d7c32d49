<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use App\Models\RemoteServerFilter;
use Illuminate\Database\Seeder;

class RemoteServerFilterSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $remoteServerFilters = [
			['id' => 1, 'name' => 'Completed', 'is_active' => 1],
			['id' => 2, 'name' => 'Pending', 'is_active' => 1],
			['id' => 3, 'name' => 'Not Connected', 'is_active' => 1],
		];

		foreach ($remoteServerFilters as $key => $filter) {
			RemoteServerFilter::updateOrCreate(
				[
				   'id'   => $filter['id'],
				],
				[
					'name' => $filter['name'],
					'is_active' => $filter['is_active'],
				],
			);
		}
    }
}
