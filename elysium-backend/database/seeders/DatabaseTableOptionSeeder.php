<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use App\Models\DatabaseTableOption;
use Illuminate\Database\Seeder;

class DatabaseTableOptionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tableOptions = [
			['id' => 1, 'name' => '1-200', 'is_active' => 1],
			['id' => 2, 'name' => '201-400', 'is_active' => 1],
			['id' => 3, 'name' => '401-600', 'is_active' => 1],
			['id' => 4, 'name' => '601-800', 'is_active' => 1],
			['id' => 5, 'name' => '801-1000', 'is_active' => 1]
		];

		foreach ($tableOptions as $key => $option) {
			DatabaseTableOption::updateOrCreate(
				[
				   'id'   => $option['id'],
				],
				[
					'name' => $option['name'],
					'is_active' => $option['is_active']
				],
			);
		}
    }
}
