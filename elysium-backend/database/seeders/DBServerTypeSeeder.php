<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\DBServerType;

class DBServerTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $dbServers = [
			['id' => 1, 'name' => 'MySQL', 'is_active' => 1]
		];

		foreach ($dbServers as $key => $dbServer) {
			DBServerType::updateOrCreate(
				[
				   'id'   => $dbServer['id'],
				],
				[
					'name' => $dbServer['name'],
					'is_active' => $dbServer['is_active']
				],
			);
		}
    }
}
