<?php

namespace Database\Seeders;

use Carbon\Carbon;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SubscriptionPlan;

class SubscriptionPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $subscriptionPlans = [
			[
				"id" => 1,
				"plan_name" => "Trial",
				"subscription_type_id" => 1,
				"plan_price" => 0,
				"plan_price_effect_date" => Carbon::now()->format('F j, Y'),
				"plan_price_end_date" => Carbon::now()->addDays(14)->format('F j, Y'),
				"table_plan_limit" => 'Up to 3 tables',
				"price_per_table" => 0,
				'is_active' => 1,
				"stripe_plan" => null,
				"key_benefits" => null
			],
			[
				"id" => 2,
				"plan_name" => "Lite",
				"subscription_type_id" => 2,
				"plan_price" => "$75",
				"plan_price_effect_date" => Carbon::now()->format('F j, Y'),
				"plan_price_end_date" => Carbon::now()->addMonths(1)->format('F j, Y'),
				"table_plan_limit" => "Up to 10 tables",
				"price_per_table" => "$7.50 per table",
				"stripe_plan" => "price_1Nfer4ER0DocJpyMSyEDM7Ps",
				'is_active' => 1,
				"key_benefits" => [
					'Lite Dashboard' => 1,
					'Query Archive Data' => 0,
					'Storage Cost' => 0,
					'DB Storage Cost Savings' => 0,
					'Project Table Growth' => 0,
					'Table Size + Indexing' => 0
				]
			],
			[
				"id" => 3,
				"plan_name" => "Medium",
				"subscription_type_id" => 2,
				"plan_price" => "$295",
				"plan_price_effect_date" => Carbon::now()->format('F j, Y'),
				"plan_price_end_date" => Carbon::now()->addMonths(1)->format('F j, Y'),
				"table_plan_limit" => "Up to 50 tables",
				"price_per_table" => "$5.90 per table",
				"stripe_plan" => "price_1NfephER0DocJpyMd8E0Yab1",
				'is_active' => 1,
				"key_benefits" => [
					'Lite Dashboard' => 1,
					'Query Archive Data' => 1,
					'Storage Cost' => 1,
					'DB Storage Cost Savings' => 1,
					'Project Table Growth' => 1,
					'Table Size + Indexing' => 1
				]
			],
			[
				"id" => 4,
				"plan_name" => "High",
				"subscription_type_id" => 2,
				"plan_price" => "$1999",
				"plan_price_effect_date" => Carbon::now()->format('F j, Y'),
				"plan_price_end_date" => Carbon::now()->addMonths(1)->format('F j, Y'),
				"table_plan_limit" => "Up to 500 tables",
				"price_per_table" => "$4.00 per table",
				"stripe_plan" => "price_1NfemzER0DocJpyMJC8Irp8T",
				'is_active' => 1,
				"key_benefits" => [
					'Lite Dashboard' => 1,
					'Query Archive Data' => 1,
					'Storage Cost' => 1,
					'DB Storage Cost Savings' => 1,
					'Project Table Growth' => 1,
					'Table Size + Indexing' => 1
				]
			],
			[
				"id" => 5,
				"plan_name" => "Lite",
				"subscription_type_id" => 3,
				"plan_price" => "$900",
				"plan_price_effect_date" => Carbon::now()->format('F j, Y'),
				"plan_price_end_date" => Carbon::now()->addYears(1)->format('F j, Y'),
				"table_plan_limit" => "Up to 10 tables",
				"price_per_table" => "$7.50 per table",
				"stripe_plan" => "price_1NfelXER0DocJpyM7fHfVeZj",
				'is_active' => 1,
				"key_benefits" => [
					'Lite Dashboard' => 1,
					'Query Archive Data' => 0,
					'Storage Cost' => 0,
					'DB Storage Cost Savings' => 0,
					'Project Table Growth' => 0,
					'Table Size + Indexing' => 0
				]
			],
			[
				"id" => 6,
				"plan_name" => "Medium",
				"subscription_type_id" => 3,
				"plan_price" => "$3240",
				"plan_price_effect_date" => Carbon::now()->format('F j, Y'),
				"plan_price_end_date" => Carbon::now()->addYears(1)->format('F j, Y'),
				"table_plan_limit" => "Up to 50 tables",
				"price_per_table" => "$5.90 per table",
				"stripe_plan" => "price_1NfekOER0DocJpyMfFKWMm3p",
				'is_active' => 1,
				"key_benefits" => [
					'Lite Dashboard' => 1,
					'Query Archive Data' => 1,
					'Storage Cost' => 1,
					'DB Storage Cost Savings' => 1,
					'Project Table Growth' => 1,
					'Table Size + Indexing' => 1
				]
			],
			[
				"id" => 8,
				"plan_name" => "High",
				"subscription_type_id" => 3,
				"plan_price" => "$23988",
				"plan_price_effect_date" => Carbon::now()->format('F j, Y'),
				"plan_price_end_date" => Carbon::now()->addYears(1)->format('F j, Y'),
				"table_plan_limit" => "Up to 500 tables",
				"price_per_table" => "$4.00 per table",
				"stripe_plan" => "price_1Nfei0ER0DocJpyMcHcBLUvb",
				'is_active' => 1,
				"key_benefits" => [
					'Lite Dashboard' => 1,
					'Query Archive Data' => 1,
					'Storage Cost' => 1,
					'DB Storage Cost Savings' => 1,
					'Project Table Growth' => 1,
					'Table Size + Indexing' => 1
				]
			],
			[
				"id" => 9,
				"plan_name" => "Enterprise",
				"subscription_type_id" => 3,
				"plan_price" => "Custom",
				"plan_price_effect_date" => Carbon::now()->format('F j, Y'),
				"plan_price_end_date" => Carbon::now()->addYears(1)->format('F j, Y'),
				"table_plan_limit" => "Unlimited number of tables",
				"price_per_table" => null,
				"stripe_plan" => null,
				'is_active' => 1,
				"key_benefits" => [
					'Lite Dashboard' => 1,
					'Query Archive Data' => 1,
					'Storage Cost' => 1,
					'DB Storage Cost Savings' => 1,
					'Project Table Growth' => 1,
					'Table Size + Indexing' => 1
				]
			],
			[
				"id" => 10,
				"plan_name" => "Enterprise",
				"subscription_type_id" => 2,
				"plan_price" => 'Custom',
				"plan_price_effect_date" => Carbon::now()->format('F j, Y'),
				"plan_price_end_date" => Carbon::now()->addYears(1)->format('F j, Y'),
				"table_plan_limit" => "Unlimited number of tables",
				"price_per_table" => null,
				"stripe_plan" => null,
				'is_active' => 1,
				"key_benefits" => [
					'Lite Dashboard' => 1,
					'Query Archive Data' => 1,
					'Storage Cost' => 1,
					'DB Storage Cost Savings' => 1,
					'Project Table Growth' => 1,
					'Table Size + Indexing' => 1
				]
			]
		];

		foreach ($subscriptionPlans as $key => $subscriptionPlan) {
			SubscriptionPlan::updateOrCreate(
				[
				   'id'   => $subscriptionPlan['id'],
				],
				[
					'plan_name' => $subscriptionPlan['plan_name'],
					'plan_price' => $subscriptionPlan['plan_price'],
					'subscription_type_id' => $subscriptionPlan['subscription_type_id'],
					'plan_price_effect_date' => $subscriptionPlan['plan_price_effect_date'],
					'plan_price_end_date' => $subscriptionPlan['plan_price_end_date'],
					'table_plan_limit' => $subscriptionPlan['table_plan_limit'],
					'price_per_table' => $subscriptionPlan['price_per_table'],
					'stripe_plan' => $subscriptionPlan['stripe_plan'],
					'key_benefits' => $subscriptionPlan['key_benefits'],
					'is_active' => $subscriptionPlan['is_active'],
				],
			);
		}
    }
}
