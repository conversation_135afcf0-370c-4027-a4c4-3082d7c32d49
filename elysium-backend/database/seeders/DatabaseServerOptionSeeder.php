<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use App\Models\DatabaseServerOption;
use Illuminate\Database\Seeder;

class DatabaseServerOptionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $serverOptions = [
			['id' => 1, 'name' => '1-5', 'is_active' => 1],
			['id' => 2, 'name' => '6-10', 'is_active' => 1],
			['id' => 3, 'name' => '11-15', 'is_active' => 1],
			['id' => 4, 'name' => '16-20', 'is_active' => 1],
			['id' => 5, 'name' => '21-25', 'is_active' => 1]
		];

		foreach ($serverOptions as $key => $option) {
			DatabaseServerOption::updateOrCreate(
				[
				   'id'   => $option['id'],
				],
				[
					'name' => $option['name'],
					'is_active' => $option['is_active']
				],
			);
		}
    }
}
