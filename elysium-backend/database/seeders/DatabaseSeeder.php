<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
			CountrySeeder::class,
			CountryStatesSeeder::class,
			DatabaseServerOptionSeeder::class,
			DatabaseTableOptionSeeder::class,
			SubscriptionTypeSeeder::class,
			SubscriptionPlanSeeder::class,
			TimezoneTableSeeder::class,
			RemoteServerFilterSeeder::class,
			DBServerTypeSeeder ::class,
			TableProcessStatusSeeder::class,
			ObjectStorageTypeSeeder::class,
		]);
    }
}
