<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ObjectStorageType;

class ObjectStorageTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $objectStorageTypes = [
			['id' => 1, 'object_storage_type' => 'S3 Standard', 'cloud_provider_name' => 'AWS'],
			['id' => 2, 'object_storage_type' => 'S3 Intelligent-Tiering', 'cloud_provider_name' => 'AWS'],
			['id' => 3, 'object_storage_type' => 'S3 Standard-Infrequent Access', 'cloud_provider_name' => 'AWS'],
			['id' => 4, 'object_storage_type' => 'S3 One Zone-Infrequent Access', 'cloud_provider_name' => 'AWS'],
			['id' => 5, 'object_storage_type' => 'S3 Glacier', 'cloud_provider_name' => 'AWS'],
			['id' => 6, 'object_storage_type' => 'Google Standard Storage', 'cloud_provider_name' => 'GoogleCloud'],
			['id' => 7, 'object_storage_type' => 'Google Nearline Storage', 'cloud_provider_name' => 'GoogleCloud'],
			['id' => 8, 'object_storage_type' => 'Coldline Storage', 'cloud_provider_name' => 'GoogleCloud'],
			['id' => 9, 'object_storage_type' => 'Archive Storage', 'cloud_provider_name' => 'GoogleCloud'],
		];

		foreach ($objectStorageTypes as $key => $objectStorageType) {
			ObjectStorageType::updateOrCreate(
				[
				   'id'   => $objectStorageType['id'],
				],
				[
					'object_storage_type' => $objectStorageType['object_storage_type'],
					'cloud_provider_name' => $objectStorageType['cloud_provider_name'],
				],
			);
		}
    }
}
