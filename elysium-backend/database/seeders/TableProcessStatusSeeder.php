<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\TableProcessStatus;

class TableProcessStatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $statusses = [
			['id' => 1, 'table_action_name' => 'No Action'],
			['id' => 2, 'table_action_name' => 'Export'],
			['id' => 3, 'table_action_name' => 'Archive'],
			['id' => 4, 'table_action_name' => 'Analyze']
		];

		foreach ($statusses as $key => $status) {
			TableProcessStatus::updateOrCreate(
				[
				   'id'   => $status['id'],
				],
				[
					'table_action_name' => $status['table_action_name'],
				],
			);
		}
    }
}
