<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SubscriptionType;

class SubscriptionTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $subscriptionTypes = [
			['id' => 1, 'name' => 'Trial', "is_active" => 1],
			['id' => 2, 'name' => 'Monthly', "is_active" => 1],
			['id' => 3, 'name' => 'Yearly', "is_active" => 1]
		];

		foreach ($subscriptionTypes as $key => $subscriptionType) {
			SubscriptionType::updateOrCreate(
				[
				   'id'   => $subscriptionType['id'],
				],
				[
					'name' => $subscriptionType['name'],
					'is_active' => $subscriptionType['is_active']
				],
			);
		}
    }
}
