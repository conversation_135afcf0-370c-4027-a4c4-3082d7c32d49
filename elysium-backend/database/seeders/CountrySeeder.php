<?php

namespace Database\Seeders;

use App\Models\Country;
use Illuminate\Database\Seeder;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class CountrySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $countries = [
			['id' => 1, 'name' => 'United States', 'is_active' => 1]
		];

		foreach ($countries as $key => $country) {
			Country::updateOrCreate(
				[
				   'id'   => $country['id'],
				],
				[
					'country_name' => $country['name'],
					'is_active' => $country['is_active']
				],
			);
		}
    }
}
