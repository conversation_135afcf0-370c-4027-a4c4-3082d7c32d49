## Elysium Sequence Diagram of Server Connectivity

This is the sequence <PERSON><PERSON> will follow to connect with Agent server and it's DB servers.

```mermaid
sequenceDiagram
    autonumber
    participant Elysium
    actor Customer
    participant Agent
    participant Agent-DB
    
    Elysium->>Customer: Ask for Server Detail
    Customer-->>Elysium: Provided Server Detail

    Note over Customer,Agent: Save Elysium SSH-Public key in <br/>Server authorized_keys
    
    Elysium->>Agent: SSH Connect
    Note right of Agent: Save bash scripts for <br/>Agent-DB process
    Note right of Agent: Set <PERSON><PERSON>Job for bash scripts <br/>to execute priodically
    
    break When connection to the Agent fail
        Agent-->>Elysium: show error message
    end
    
    critical Establish a connection to the Agent-DB
        Agent->>Agent-DB: Connect
    option Network timeout
        Agent-->Agent: Log error
    option Credentials rejected
        Agent-->Agent: Log different error
    end

    Agent->>Agent-DB: List databases;
    Agent-DB-->>Agent: Save response in a file
    
    loop on all databases
        Agent->>Agent-DB: List tables of a database;
        Agent-DB-->>Agent: Save response in a file
    end

    Agent->>Elysium: Send response fetched from files to Elysium Webhook
```