<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Models\Cashier\Subscription;
use App\Models\Cashier\SubscriptionItem;
use App\Models\User;
use Lara<PERSON>\Cashier\Cashier;
use <PERSON><PERSON>\Cashier\CashierServiceProvider;


class AppServiceProvider extends CashierServiceProvider
{

	/**
     * The migrations that should be ignored by Cashier.
     *
     * @var array
     */
    protected $ignoreMigrations = [
        '2019_05_03_000001_create_customer_columns',
        '2019_05_03_000002_create_subscriptions_table',
        '2019_05_03_000003_create_subscription_items_table'
	];


    /**
     * Register any application services.
     */
    public function register(): void
    {
        Cashier::ignoreMigrations();
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {

    }
}
