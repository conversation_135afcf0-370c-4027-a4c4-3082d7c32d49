<?php

namespace App\Helpers;

use Carbon\Carbon;

class Helper
{
	/**
	 * Convert payment amount
	 */
    public static function convertPaymentAmount($amount = 0)
    {
        return number_format($amount/100, 2, '.', ',');
    }

	/**
	 * Find number of days left in trial period
	 */
	public static function userTrialSubscrriptionStatus($trialEndsAtDate = NULL) {

		$trialDaysLeft = 0;
		$trialEndsAtDate = Carbon::parse($trialEndsAtDate);
		$currentDate = Carbon::now();
		$diff_in_days = $currentDate->diffInDays($trialEndsAtDate);

		if($diff_in_days < 14) {
			$trialDaysLeft = $diff_in_days;
		}

		return $trialDaysLeft;
	}
}
