<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdateCompanyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'company_name' => 'required|max:255|unique:company,company_name,'.$this->company,
			'country_id' => 'required|exists:country,id',
			'state_id' => 'required|exists:state,id',
			'company_address' => 'required',
			'company_address2' => 'required',
			'company_city' => 'required',
			'company_postalcode' => 'required'
        ];
    }

	/**
	 * Custom JSON response if validation failed
	 */
	protected function failedValidation(Validator $validator) {
		throw new HttpResponseException(
			response()->json([
				'status' => 'Error',
				'messages' => $validator->errors()->all()
			], 200)
		);
	}
}
