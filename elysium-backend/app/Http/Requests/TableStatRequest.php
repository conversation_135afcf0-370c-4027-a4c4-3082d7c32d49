<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class TableStatRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'start_date' => 'required|date|date_format:Y-m-d',
            'end_date' => [
                'required',
                'date',
                'date_format:Y-m-d',
                'after_or_equal:start_date',
            ],
            'server_id' => 'required|numeric|exists:client_db_server,id',
            'database_id' => 'required|numeric|exists:client_db_schema,id',
            'table_id' => 'required|numeric|exists:client_db_server_table,id',
            'flag' => 'required|string|in:h,d,m,y,a',
        ];
    }


    /**
     * Custom JSON response if validation failed
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'Error',
                'messages' => $validator->errors()->all()
            ], 200)
        );
    }
}
