<?php

namespace App\Http\Requests;

use Illuminate\Validation\Rule;
use App\Rules\IsActiveDBSchedule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class DeleteDatabaseScheduleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'database_id' => [
                'required',
                'numeric',
                new IsActiveDBSchedule,
                Rule::exists('client_database', 'id')->whereNull('deleted_at'),
            ],
        ];
    }


    /**
     * Custom JSON response if validation failed
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'Error',
                'messages' => $validator->errors()->all()
            ], 200)
        );
    }
}
