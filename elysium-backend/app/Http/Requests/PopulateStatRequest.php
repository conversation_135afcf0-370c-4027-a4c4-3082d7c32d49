<?php

namespace App\Http\Requests;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class PopulateStatRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'id' => 'required|integer|exists:table_job_schedule_execution_log,id',
            'enc_key' => 'required|exists:table_job_schedule_execution_log,schedule_enc_key,id,' . $this->id,
            'export_status' => 'required|in:Started,Completed,Stopped',
            'is_export_error' => 'sometimes|integer',
            'total_db_storage_setup_cost' => 'sometimes|integer',
            'total_table_storage_cost_saving' => 'sometimes|integer',
            'total_db_data_size_archived' => 'sometimes|integer',
            'total_table_rows_archived' => 'sometimes|integer',
            'total_table_data_size_archived' => 'sometimes|integer',
            'file_exported_name' => 'sometimes',
            'object_storage_dir_name' => 'sometimes',
            'file_export_size' => 'sometimes|integer|min:1',
            'file_stored_size' => 'sometimes|integer|min:1',
            'file_export_started_at' => 'sometimes|date|date_format:Y-m-d H:i:s',
            'file_export_ended_at' => 'sometimes|date|date_format:Y-m-d H:i:s',
            'error_message' => 'sometimes',
            'is_current' => 'sometimes',
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'Error',
                'messages' => $validator->errors()->all()
            ], 200)
        );
    }
}
