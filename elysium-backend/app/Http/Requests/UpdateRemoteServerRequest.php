<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateRemoteServerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|unique:remote_server,name,'.$this->id,
            'hostname' => 'required|unique:remote_server,hostname,'.$this->id,
			'username' => 'required',
			'port' => 'required', // max digits length
			'agent_uuid' => 'required'
        ];
    }
}
