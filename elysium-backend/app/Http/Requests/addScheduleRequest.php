<?php

namespace App\Http\Requests;

use App\Models\ClientDatabase;
use App\Models\ClientDBServerTable;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class addScheduleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'table_ids' => 'required|array',
            'table_ids.*' => 'exists:client_db_server_table,id',
            'server_id' => 'required|numeric|exists:client_db_server,id',
            'database_id' => 'required|numeric|exists:client_database,id',
        ];
    }

    /**
     * Custom JSON response if validation failed
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'Error',
                'messages' => $validator->errors()->all()
            ], 200)
        );
    }
    /**
     * Add custom validation logic
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator)
        {
            $serverId = $this->input('server_id');
            $databaseId = $this->input('database_id');
            $tableIds = $this->input('table_ids');

            if (!$this->databaseBelongsToServer($serverId, $databaseId))
            {
                $validator->errors()->add('database_id', 'The selected database does not belong to the specified server.');
            }
            if (!$this->databaseBelongsTodatabase($tableIds, $databaseId))
            {
                $validator->errors()->add('tableId', 'The selected tableIds do not belong to the specified database.');
            }
        });
    }

    /**
     * Check if the database belongs to the specified server
     */
    protected function databaseBelongsToServer($serverId, $databaseId)
    {
        return ClientDatabase::where('id', $databaseId)
            ->where('client_db_server_id', $serverId)
            ->exists();
    }

    /**
     * Check if the tables belongs to the specified database
     */
    protected function databaseBelongsTodatabase($tableIds, $databaseId)
    {
        return ClientDBServerTable::whereIn('id', $tableIds)
            ->where('client_database_id', $databaseId)
            ->count() === count($tableIds);
    }
}
