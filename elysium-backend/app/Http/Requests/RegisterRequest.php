<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class RegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
			'company_name' => 'required|unique:company,company_name|max:255',
			'country_id' => 'required|exists:country,id',
			'state_id' => 'required|exists:state,id',
			'address' => 'required|max:1000',
 			'city' => 'required',
			'zip' => 'required',
            'first_name' => 'required|max:255',
            'last_name' => 'required|max:255',
            'email' => 'required|email|unique:user|max:255',
			'password' => 'required|min:8',
			'db_server_option' => 'required|exists:database_server_option,id',
			'db_table_option' => 'required|exists:database_table_option,id',
        ];
    }

	/**
	 * Custom JSON response if validation failed
	 */
	protected function failedValidation(Validator $validator) {
		throw new HttpResponseException(
			response()->json([
				'status' => 'Error',
				'messages' => $validator->errors()->all()
			], 200)
		);
	}
}
