<?php

namespace App\Http\Requests;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class ScheduleTimeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'schedule_id' => [
                'required',
                'numeric',
                Rule::exists('table_job_schedule', 'id')->whereNull('deleted_at')
            ],
            'batch_size' => 'required|numeric|min:10000|max:100000000',
            'schedule_type' => 'required|string',
            'retention' => 'required|numeric|min:60|max:60000',
            's3_directory' => 'required|string',
            'time_zone' => 'required|exists:timezone,name',
            'start_time' => 'required|date_format:H:i:s',
            'end_time' => 'required|after:start_time|date_format:H:i:s',
            'day_of_week' => 'required|numeric',
        ];
    }


    /**
     * Custom JSON response if validation failed
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'Error',
                'messages' => $validator->errors()->all()
            ], 200)
        );
    }
}
