<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdateDBServerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'db_server_name' => 'required|unique:client_db_server,db_server_name,'.$this->id,
            'client_db_server_uuid' => 'required|unique:client_db_server,client_db_server_uuid,'.$this->id,
            'hostname' => 'required|unique:client_db_server,hostname,'.$this->id,
			'username' => 'required',
			'password' => 'required',
			'port' => 'required',
			'remote_server_id' => 'required|exists:remote_server,id',
			'client_db_server_type_id' => 'required|exists:client_db_server_type,id',
        ];
    }

	/**
	 * Custom JSON response if validation failed
	 */
	protected function failedValidation(Validator $validator) {
		throw new HttpResponseException(
			response()->json([
				'status' => 'Error',
				'messages' => $validator->errors()->all()
			], 200)
		);
	}
}
