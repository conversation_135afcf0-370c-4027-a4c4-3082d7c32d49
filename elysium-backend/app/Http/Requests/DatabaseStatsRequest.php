<?php

namespace App\Http\Requests;

use App\Models\ClientDatabase;
use App\Models\ClientDbServer;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class DatabaseStatsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
    public function rules(): array
    {
        return [
            'start_date' => 'required|date|date_format:Y-m-d',
            'end_date' => [
                'required',
                'date',
                'date_format:Y-m-d',
                'after_or_equal:start_date',
            ],
            'id' => 'required|numeric|exists:client_db_server,id',
            'database_id' => 'required|numeric|exists:client_database,id',
            'flag' => 'required|string|in:h,d,m,y,a',
        ];
    }


    /**
     * Custom JSON response if validation failed
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'Error',
                'messages' => $validator->errors()->all()
            ], 200)
        );
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator)
        {
            $serverId = $this->input('id');
            $databseId = $this->input('database_id');

            if (!$this->serverBelongsToUser($serverId))
            {
                $validator->errors()->add('Id', 'The selected server id does not belong to the specified user.');
            }
            if (!$this->databaseBelongsToServer($serverId, $databseId))
            {
                $validator->errors()->add('database_id', 'The selected daatabase id does not belong to the specified server.');
            }
        });
    }

    /**
     * Check if the server belongs to the specified user
     */
    protected function databaseBelongsToServer($serverId, $databseId)
    {
        return ClientDatabase::where('id', $databseId)
            ->where('client_db_server_id', $serverId)
            ->exists();
    }
    protected function serverBelongsToUser($serverId)
    {
        return ClientDbServer::where('id', $serverId)
            ->where('company_id', auth()->user()->company_id)
            ->exists();
    }
}
