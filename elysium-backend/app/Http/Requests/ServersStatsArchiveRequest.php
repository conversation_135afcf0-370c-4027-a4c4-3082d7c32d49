<?php

namespace App\Http\Requests;

use App\Models\ClientDbServer;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\Rule;

class ServersStatsArchiveRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'start_date' => 'required|date|date_format:Y-m-d',
            'end_date' => [
                'required',
                'date',
                'date_format:Y-m-d',
                'after_or_equal:start_date',
            ],
            'id' => 'required|numeric|exists:client_db_server,id',
            'flag' => 'required|string|in:d,m,y,a',
        ];
    }


    /**
     * Custom JSON response if validation failed
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'Error',
                'messages' => $validator->errors()->all()
            ], 200)
        );
    }

    /**
     * Add custom validation logic
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator)
        {
            $serverId = $this->input('id');

            if (!$this->serverBelongsToUser($serverId))
            {
                $validator->errors()->add('Id', 'The selected server id does not belong to the specified user.');
            }
        });
    }

    /**
     * Check if the server belongs to the specified user
     */
    protected function serverBelongsToUser($serverId)
    {
        return ClientDbServer::where('id', $serverId)
            ->where('company_id', auth()->user()->company_id)
            ->exists();
    }
}
