<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubscriptionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
			'id' => $this->id,
			'user_id' => $this->user_id,
			'userName' => $this->user->name,
			'plan_name' => $this->name,
			'stripe_id' => $this->stripe_id,
			'stripe_status' => $this->stripe_status,
			'stripe_price' => $this->stripe_price,
			'quantity' => $this->quantity,
			'trial_ends_at' => $this->trial_ends_at,
			'ends_at' => $this->ends_at ? Carbon::parse($this->ends_at)->format('M d Y') : null,
			'created_at' => $this->created_at,
			'updated_at' => $this->updated_at,
		];
    }
}
