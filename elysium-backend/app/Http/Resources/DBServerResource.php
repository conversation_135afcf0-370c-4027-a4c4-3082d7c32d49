<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DBServerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
			'id' => $this->id,
			'db_server_name' => $this->db_server_name,
			'db_server_alias_name' => $this->db_server_alias_name,
			'client_db_server_uuid' => $this->client_db_server_uuid,
			'hostname' => $this->hostname,
			'username' => $this->username,
			'port' => $this->port,
			'remote_server_id' => $this->remote_server_id,
			'remote_server_name' => $this->remoteServer->name,
			'client_db_server_type_id' => $this->client_db_server_type_id,
			'client_db_server_type_name' => $this->type->name,
			'status_id' => $this->client_db_server_type_id,
			'statusName' => $this->remoteServerFilter->name,
			'is_active' => $this->is_active === 1 ? true : false,
			'created_at' => $this->created_at,
			'updated_at' => $this->updated_at,
			'is_deleted' => $this->is_deleted === 1 ? true : false,
			'total_current_db_storage_setup_size' => $this->total_current_db_storage_setup_size
		];
    }
}
