<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ClientObjectStorageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
			'id' => $this->id,
			'bucket_name' => $this->bucket_name,
			'object_storage_type_id' => $this->object_storage_type_id,
			'object_storage_type' => $this->objectStorageType->object_storage_type,
			'cloud_provider_name' => $this->objectStorageType->cloud_provider_name,
			'company_id' => $this->company_id,
			'company_name' => $this->company->company_name,
			'created_at' => $this->created_at,
			'updated_at' => $this->updated_at
		];
    }
}
