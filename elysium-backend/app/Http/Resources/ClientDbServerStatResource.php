<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ClientDbServerStatResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'total_archive_data'=>$this->total_archive_data,
            'total_db_archived'=>$this->total_db_archived,
            'total_table_archived'=>$this->total_table_archived,
            'total_table_storage_cost_saving'=>$this->total_table_storage_cost_saving,
        ];
    }
}
