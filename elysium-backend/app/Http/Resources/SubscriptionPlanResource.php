<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubscriptionPlanResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
			'id' => $this->id,
			'plan_name' => $this->plan_name,
			'subscription_type' => $this->subscriptionType->name,
			'subscription_type_id' => $this->subscription_type_id,
			'plan_price' => $this->plan_price,
			'plan_price_effect_date' => $this->plan_price_effect_date,
			'plan_price_end_date' => $this->subscription_type_id == 2 ? Carbon::now()->addMonths(1)->format('F j, Y') : Carbon::now()->addYears(1)->format('F j, Y'),
			'table_plan_limit' => $this->table_plan_limit,
			'price_per_table' => $this->price_per_table,
			'stripe_plan' => $this->stripe_plan,
			'status' => $this->is_active === 0 ? 'active' : 'inactive',
			'key_benefits' => $this->key_benefits,
			'created_at' => $this->created_at,
			'updated_at' => $this->updated_at
		];
    }
}
