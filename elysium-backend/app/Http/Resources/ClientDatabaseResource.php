<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ClientDatabaseResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
			'id' => $this->id,
 			'client_db_server_id' => $this->client_db_server_id,
 			'client_db_server_name' => $this->clientDBServer->db_server_name,
			'agent_name' => $this->clientDBServer->remoteServer->name,
			'hostname' => $this->clientDBServer->hostname,
 			'db_name' => $this->db_name,
 			'total_current_db_size' => $this->total_current_db_size,
 			'total_current_db_data_size' => $this->total_current_db_data_size,
 			'total_current_db_index_size' => $this->total_current_db_index_size,
			'total_tables_count' => $this->total_table,
			'created_at' => $this->created_at,
			'updated_at' => $this->updated_at,
			'client_db_schema_id' => $this->clientDBSchema ? $this->clientDBSchema->id : null,
		];
    }
}
