<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ClientDBServerTableResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // Add null check for db_server relationship
        $dbServer = $this->client_db_server ?? null;
        $dbServerName = $dbServer ? $dbServer->db_server_name : 'Unknown';
        
        return [
            'id' => $this->id,
            'client_db_server_table_uuid' => $this->client_db_server_table_uuid,
            'table_name' => $this->table_name,
            'client_db_schema_id' => $this->client_db_schema_id,
            'client_database_id' => $this->client_database_id,
            'client_db_server_id' => $this->client_db_server_id,
            'db_server_name' => $dbServerName, // Use the safe value
            // Include other fields as needed
            'table_schema_name' => $this->table_schema_name,
            'table_database_name' => $this->table_database_name,
            'table_type' => $this->table_type,
            'engine' => $this->engine,
            'version' => $this->version,
            'row_format' => $this->row_format,
            'total_current_table_rows' => $this->total_current_table_rows,
            'total_current_data_length' => $this->total_current_data_length,
            'avg_row_length' => $this->avg_row_length,
            'max_data_length' => $this->max_data_length,
            'data_free' => $this->data_free,
            'check_time' => $this->check_time,
            'table_collation' => $this->table_collation,
            'checksum' => $this->checksum,
            'create_options' => $this->create_options,
            'table_comment' => $this->table_comment,
            'timezone' => $this->timezone,
            'table_process_status_id' => $this->table_process_status_id,
            'has_reference_integrity' => $this->has_reference_integrity,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'is_dropped' => $this->is_dropped
        ];
    }
}
