<?php

namespace App\Http\Resources;

use Storage;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
		$date = Carbon::now();
        return [
            'id' => $this->id,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->email,
			'company' => $this->company->company_name,
			'company_id' => $this->company_id,
			'country' => $this->company->country->country_name,
			'country_id' => $this->company->country->id,
			'server_option' => $this->company->serverOption->name,
			'server_option_id' => $this->company->database_server_option_id,
			'table_option' => $this->company->tableOption->name,
			'table_option_id' => $this->company->database_table_option_id,
			'trial_ends_at' => $this->trial_ends_at,
			'trial_days_left' => $this->trial_ends_at != NULL ? ($date->diffInDays(Carbon::parse($this->trial_ends_at)) <= 14 ?  $date->diffInDays(Carbon::parse($this->trial_ends_at)) : 0)  : 0,
			'image_path' => $this->image ? Storage::url('users/'.$this->image) : NULL,
			'timezone_id' => $this->timezone_id,
			'timezone' => $this->timezone_id ? $this->timezone->name : NULL,
			'is_trial_active' => $this->is_trial_active ? true : false,
            'is_subscribed' => $this->subscriptions()->active()->first() ? true : false,
            'subscrition_plan_id' => $this->subscriptions()->active()->latest()->first()->stripe_price ?? null,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
