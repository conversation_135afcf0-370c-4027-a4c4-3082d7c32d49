<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ServerStatsArchiveResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'server_name' => $this->server_name,
            'total_archive_size' => $this->total_archive_size,
            'total_db_archive' => $this->total_db_archive,
            'total_table_archive' => $this->total_table_archive,
        ];
    }
}
