<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RemoteServerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
			'agentID' => $this->id,
			'name' => $this->name,
			'hostname' => $this->hostname,
			'username' => $this->username,
			'port' => $this->port,
			'osType' => $this->os_type,
			'agent_uuid' => $this->agent_uuid,
			'is_tls_required' => $this->is_tls_required === 1 ? true : false,
			'remote_servers_filter_id' => $this->remote_server_status_id,
			'status' => $this->remoteServerFilter->name,
			'is_valid' => $this->is_active === 1 ? true : false,
			'is_active' => $this->is_active === 1 ? true : false,
			'created_at' => $this->created_at,
			'updated_at' => $this->updated_at,
			'is_deleted' => $this->is_deleted === 1 ? true : false,
		];
    }
}
