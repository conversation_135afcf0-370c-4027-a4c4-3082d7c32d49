<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CompanyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
			'id' => $this->id,
			'company_name' => $this->company_name,
			'country_name' => $this->country->country_name,
			'country_id' => $this->country_id,
			'state_name' => $this->state->state_name,
			'state_id' => $this->state_id,
			'address' => $this->company_address,
			'secondary_address' => $this->company_address2,
			'city' => $this->company_city,
			'zip' => $this->company_postalcode,
			'status' => $this->is_active == 0 ? 'active' : 'inactive',
			'created_at' => $this->created_at,
			'updated_at' => $this->updated_at,
		];
    }
}
