<?php

namespace App\Http\Controllers\API;

use Carbon\Carbon;
use App\Traits\ClubData;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use App\Models\ClientDatabase;
use App\Http\Controllers\Controller;
use App\Http\Requests\TableStatRequest;
use App\Http\Resources\DashboardServerPieChartDataResource;

class TableStatsController extends Controller
{
    use ApiResponser, ClubData;


    /**
     * Table Archive data api.
     * 
     * @param  \Illuminate\Http\Request  $request
     */
    public function  tablesTotalArchiveStats(TableStatRequest $request)
    {
        try
        {
            $company_id = auth()->user()->company_id;
            $server_id = $request->server_id;
            $database_id = $request->database_id;
            $table_id = $request->table_id;
            if ($request->flag == 'a')
            {
                $startDate = null;
                $endDate = null;
            }
            else
            {
                $startDate = Carbon::createFromFormat('Y-m-d', $request->start_date)->startOfDay();
                $endDate = Carbon::createFromFormat('Y-m-d', $request->end_date)->endOfDay();
            }
            $queryData = ClientDatabase::where('id', $database_id)
                ->with([
                    'client_db_tables' => function ($query) use ($table_id)
                    {
                        $query->select('id', 'client_database_id', 'table_name')
                            ->where('id', $table_id);
                    },
                    'client_db_tables.table_archiving_stats' => function ($query) use ($startDate, $endDate)
                    {
                        $query
                            ->when(
                                $startDate && $endDate,
                                function ($query) use ($startDate, $endDate)
                                {
                                    $query->dateRange($startDate, $endDate);
                                }
                            );
                    },
                ])
                ->first();
            if ($queryData !== null)
            {
                if ($queryData['client_db_tables']->isEmpty())
                {
                    return $this->success('Table not found', []);
                }
                $result['table_name'] = $queryData->client_db_tables[0]->table_name;
                $result['total_rows'] = $queryData->client_db_tables[0]->table_archiving_stats->sum('total_table_rows_archived') ?? 0;
                $result['total_data_archived'] = $queryData->client_db_tables[0]->table_archiving_stats->sum('total_table_data_size_archived') ?? 0;

                return $this->success(
                    'Table stats retrieved successfully',
                    [
                        'Table_total_stats' =>  DashboardServerPieChartDataResource::collection([$result])
                    ]

                );
            }
            else
            {
                return $this->success('Schema for table stat not found', []);
            }
        }
        catch (\Throwable $th)
        {
            return $this->error($th, 400);
        }
    }

    /**
     * Table Archive activity data api.
     * 
     * @param  \Illuminate\Http\Request  $request
     */
    public function  tableArchivingActivities(TableStatRequest $request)
    {
        try
        {
            $company_id = auth()->user()->company_id;
            $server_id = $request->server_id;
            $database_id = $request->database_id;
            $table_id = $request->table_id;
            if ($request->flag == 'a')
            {
                $startDate = null;
                $endDate = null;
            }
            else
            {
                $startDate = Carbon::createFromFormat('Y-m-d', $request->start_date)->startOfDay();
                $endDate = Carbon::createFromFormat('Y-m-d', $request->end_date)->endOfDay();
            }
            $queryData = ClientDatabase::where('id', $database_id)
                ->with([
                    'client_db_tables' => function ($query) use ($table_id)
                    {
                        $query->select('id', 'client_database_id', 'table_name')
                            ->where('id', $table_id);
                    },
                    'client_db_tables.export_history' => function ($query) use ($startDate, $endDate)
                    {
                        $query->when($startDate && $endDate, function ($query) use ($startDate, $endDate)
                        {
                            $query->whereBetween('created_at', [$startDate, $endDate])
                                ->orderBy('created_at', 'desc');
                        });
                    },
                    'client_db_tables.table_archiving_stats' => function ($query) use ($startDate, $endDate)
                    {
                        $query
                            ->when(
                                $startDate && $endDate,
                                function ($query) use ($startDate, $endDate)
                                {
                                    $query->dateRange($startDate, $endDate);
                                }
                            );
                    },
                ])
                ->first();
            // return $queryData;
            if ($queryData !== null)
            {

                if ($queryData->client_db_tables == null)
                {
                    return $this->success('Table not found', []);
                }
                $result = [];
                foreach ($queryData->client_db_tables[0]['export_history'] as $table_data)
                {
                    $result[] = [
                        'datetime' => $table_data->created_at,
                        'total_file_size' => $table_data->file_export_size,
                        'dir_name' => $table_data->object_storage_dir_name,
                        'total_rows_archive' => $queryData->client_db_tables[0]->table_archiving_stats->where('stat_date', date('Y-m-d', strtotime($table_data->created_at)))->sum('total_table_rows_archived'),
                    ];
                }
                return $this->success(
                    'Table activity stats retrieved successfully',
                    [
                        'table_activities' => DashboardServerPieChartDataResource::collection($result)
                    ]
                );
            }
            else
            {
                return $this->success('database for table stat not found', []);
            }
        }
        catch (\Throwable $th)
        {
            return $this->error('Something went wrong', 400);
        }
    }

    /**
     * table linchart data api.
     * 
     * @param  \Illuminate\Http\Request  $request
     */
    public function  tablesLineChart(TableStatRequest $request)
    {
        try
        {
            $company_id = auth()->user()->company_id;
            $server_id = $request->server_id;
            $database_id = $request->database_id;
            $table_id = $request->table_id;
            if ($request->flag == 'a')
            {
                $startDate = null;
                $endDate = null;
            }
            else
            {
                $startDate = Carbon::createFromFormat('Y-m-d', $request->start_date)->startOfDay();
                $endDate = Carbon::createFromFormat('Y-m-d', $request->end_date)->endOfDay();
            }

            $queryData = ClientDatabase::where('id', $database_id)
                ->with([
                    'client_db_tables' => function ($query) use ($table_id)
                    {
                        $query->select('id', 'client_database_id', 'table_name')
                            ->where('id', $table_id);
                    },
                    'client_db_tables.table_archiving_stats' => function ($query) use ($startDate, $endDate)
                    {
                        $query
                            ->when(
                                $startDate && $endDate,
                                function ($query) use ($startDate, $endDate)
                                {
                                    $query->dateRange($startDate, $endDate);
                                }
                            );
                    },
                ])
                ->first();
            if ($queryData !== null)
            {
                $table_name = $queryData->client_db_tables[0]->table_name;
                $color_code = $queryData->client_db_tables[0]->color_code;
                $formattedData = [];
                $allDates = $this->getDatesBetween($startDate, $endDate);
                $formattedDates = ['dates' => $allDates];

                $formattedData[] = [
                    'name' => $table_name,
                    'color_code' => $color_code,
                    'data' => [],
                ];
                foreach ($queryData->client_db_tables[0]->table_archiving_stats as $tables_stat)
                {
                    $key = $tables_stat->stat_date;

                    if (!in_array($tables_stat->stat_date, $formattedDates['dates']))
                    {
                        $formattedDates['dates'][] = $tables_stat->stat_date;
                    }
                    $formattedData[count($formattedData) - 1]['data'][$key] = $tables_stat->total_table_rows_archived;
                }
                asort($formattedDates['dates']);
                $result = $this->clubDataWithFlag($formattedDates, $formattedData, $request->flag);
                return $this->success(
                    'Table line chart stats retrieved successfully',
                    [
                        'table_lineChart' =>   DashboardServerPieChartDataResource::collection($result)
                    ]
                );
            }
            else
            {
                return $this->success('Table data not found', []);
            }
        }
        catch (\Throwable $th)
        {
            return $this->error('Something went wrong', 400);
        }
    }
}
