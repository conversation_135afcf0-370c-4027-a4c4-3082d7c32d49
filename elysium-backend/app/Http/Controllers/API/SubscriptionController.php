<?php

namespace App\Http\Controllers\API;

use Exception;
use Carbon\Carbon;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\SubscriptionRequest;
use App\Http\Resources\SubscriptionResource;

class SubscriptionController extends Controller
{
    use ApiResponser;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
			$subscriptions = Subscription::all();

			return $this->success('All subscriptions retrieved successfully', [
				'subscriptions' => SubscriptionResource::collection($subscriptions)
			]);
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
			$subscription = Subscription::find($id);

			if(!$subscription) {
				return $this->error('No record found', 404);
			} else {
				return $this->success('Subscription retrieved successfully!',
					[
						'subscription' => new SubscriptionResource(Subscription::find($subscription->id))
					]
				);
			}
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

	/**
	 * Create package subscription
	 */
	public function createSubscription(SubscriptionRequest $request)
    {
		try {
			$plan = SubscriptionPlan::find($request->plan);

			if(!$plan) {
				return $this->error('Invalid plan id', 400);
			}

			$user = $request->user();

        	$subscription = $request->user()->newSubscription($plan->plan_name, $plan->stripe_plan)
                        ->create($request->token);

			/**
			 * End trial period
			 */
			$user->is_trial_active = 0;
			$user->save();

			// Update the subscription with the calculated end date
			$endsAt = null;
			$planType = $plan->subscription_type_id;
			if($planType == 2) {
				$endsAt = now()->addMonth(1);
			} else if($planType == 3) {
				$endsAt = now()->addMonth(12);
			}

            $subscription->update(['ends_at' => $endsAt]);

			return $this->success('Subscription created successfully!');
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

	/**
	 * Create trial subscription
	 */
	public function trialSubscription(Request $request)
	{
		try {
			$user = $request->user();
			$user->trial_ends_at = now()->addDays(14);
			$user->is_trial_active = 1;
			$user->save();

			return $this->success('Trial subscription created successfully!', []);
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
	}

	/**
	 * Get logged-in user setup intent for stripe payments
	 */
	public function getSetupIntent(Request $request) {
		try {
			$intent = auth()->user()->createSetupIntent();

			return $this->success('Intent retrieved successfully!', [
				'intent' => $intent->client_secret
			]);
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
	}

	/**
	 * Update subscription plan
	 */
	public function updateSubscriptionPlan(SubscriptionRequest $request) {
		try {
			$plan = SubscriptionPlan::find($request->plan);

			if(!$plan) {
				return $this->error('Invalid plan id', 400);
			}

			$user = $request->user();

			$subscriptions = $user->subscriptions()->active()->get();

			if(!$subscriptions) {
				return $this->success('No active subscription found!', []);
			}

			foreach($subscriptions as $subscription) {
				$user->subscription($subscription->name)->cancel();
			}

			$subscription = $request->user()->newSubscription($plan->plan_name, $plan->stripe_plan)
                        ->create($request->token);

			if($subscription) {
				$user->is_trial_active = 0;
				$user->save();
			}

			// Update the subscription with the calculated end date
			$endsAt = null;
			$planType = $plan->subscription_type_id;
			if($planType == 2) {
				$endsAt = now()->addMonth(1);
			} else if($planType == 3) {
				$endsAt = now()->addMonth(12);
			}

            $subscription->update(['ends_at' => $endsAt]);

			return $this->success('Subscription plan upgraded successfully!', []);

		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
	}

	/**
	 * Cancelling active subscription
	 */
	public function cancelSubscriptionPlan(Request $request) {
		try {
			$user = $request->user();
			$subscriptions = $user->subscriptions()->active()->get();

			if(!$subscriptions) {
				return $this->success('No active subscription found!', []);
			}

			foreach($subscriptions as $subscription) {
				$user->subscription($subscription->name)->cancel();
			}

			return $this->success('Subscription plan cancelled successfully!', []);

		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
	}
}
