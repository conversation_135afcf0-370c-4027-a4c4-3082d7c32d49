<?php

namespace App\Http\Controllers\API;

use stdClass;
use Carbon\Carbon;
use App\Traits\ClubData;
use App\Traits\ApiResponser;
use App\Models\ClientDbServer;
use App\Http\Requests\StatRequest;
use App\Http\Controllers\Controller;
use App\Http\Requests\ServersStatsArchiveRequest;
use App\Http\Resources\DashboardActivityResource;
use App\Http\Resources\ClientDbServerStatResource;
use App\Http\Resources\ServerStatsArchiveResource;
use App\Http\Resources\DashboardServersSatsResource;
use App\Http\Resources\DashboardServerPieChartDataResource;
use App\Models\DimDate;

class ClientDbServerStatController extends Controller
{
    use ApiResponser, ClubData;

    /**
     * Dashboard Archive data api.
     * 
     * @param  \Illuminate\Http\Request  $request
     */

    public function dashboardTotalArchiveStats(StatRequest $request)
    {
        try
        {
            $company = auth()->user()->company_id;

            // Get the date
            if ($request->flag == 'a')
            {
                $startDate = Carbon::createFromFormat('Y-m-d', DimDate::min('date'));
                $endDate = Carbon::createFromFormat('Y-m-d', DimDate::max('date'));
            }
            else
            {
                $startDate = Carbon::createFromFormat('Y-m-d', $request->start_date)->startOfDay();
                $endDate = Carbon::createFromFormat('Y-m-d', $request->end_date)->endOfDay();
            }

            // Retrieve stats for the specified date range
            $servers = ClientDbServer::where('company_id', $company)
                ->with(['server_stats' => function ($query) use ($startDate, $endDate)
                {
                    $query->select('id', 'client_db_server_id', 'total_db_data_size_archived', 'db_name', 'total_table_storage_cost_saving')

                        ->dateRange($startDate, $endDate);
                }])
                ->with(['server_table_stats' => function ($query) use ($startDate, $endDate)
                {
                    $query->select('client_db_server_id', 'client_db_server_table_id')
                        ->dateRange($startDate, $endDate);
                }])
                ->select('id')
                ->whereNull('deleted_at')
                ->get();
            $archivedbstats = $servers->pluck('server_stats')->flatten();
            $archivetablestats = $servers->pluck('server_table_stats')->flatten();

            $stats = new stdClass();
            $stats->total_archive_data = $archivedbstats->sum('total_db_data_size_archived')/1024;
            $stats->total_table_storage_cost_saving = $archivedbstats->sum('total_table_storage_cost_saving');
            $stats->total_db_archived = $archivedbstats->groupBy('db_name')->count('db_name');
            $stats->total_table_archived = $archivetablestats->unique('client_db_server_table_id')->count('client_db_server_table_id');
            $statsArray = [$stats];
            return $this->success('Dashboard total archive stats  retrieved successfully', [
                'dashboard_total_archive_stats' => ClientDbServerStatResource::collection($statsArray)
            ]);
        }
        catch (\Throwable $th)
        {
            return $this->error('Something went wrong', 400);
        }
    }

    /**
     * Dashboard servers Stat api.* 
     * 
     * @param  \Illuminate\Http\Request  $request
     */

    public function dashboardServersListing(StatRequest $request)
    {
        try
        {
            $company = auth()->user()->company_id;

            // Get the date
            if ($request->flag == 'a')
            {
                $startDate = Carbon::createFromFormat('Y-m-d', DimDate::min('date'));
                $endDate = Carbon::createFromFormat('Y-m-d', DimDate::max('date'));
            }
            else
            {
                $startDate = Carbon::createFromFormat('Y-m-d', $request->start_date)->startOfDay();
                $endDate = Carbon::createFromFormat('Y-m-d', $request->end_date)->endOfDay();
            }
            // Get the data of servers with stats
            $servers = ClientDbServer::where('company_id', $company)
                ->with(['server_stats' => function ($query) use ($startDate, $endDate)
                {
                    $query->select('id', 'client_db_server_id', 'total_db_size', 'total_db_data_size_archived', 'total_table_storage_cost_saving')
                        ->dateRange($startDate, $endDate);
                }])
                ->select('id', 'company_id', 'db_server_name', 'color_code', 'total_current_db_storage_setup_size')
                ->whereNull('deleted_at')
                ->get();

            $data = [];

            foreach ($servers as $server)
            {
                if ($server->server_stats->isNotEmpty())
                {
                    $total_archive_size = $server->server_stats->sum('total_db_data_size_archived');
                    $total_storage_size = $server->total_current_db_storage_setup_size * 1024;

                    $dmi['server_id'] = $server->id;
                    $dmi['server_name'] = $server->db_server_name;
                    $dmi['color_code'] = $server->color_code;
                    $dmi['total_storage_size'] = $total_storage_size;
                    $dmi['total_storage_archived'] = round((($total_archive_size / $dmi['total_storage_size']) * 100), 2);
                    $dmi['total_cost_saving'] = $server->server_stats->sum('total_table_storage_cost_saving');
                    $dmi['total_single_server_archived'] = $total_archive_size;

                    $data[] = $dmi;
                }
                else
                {
                    $data[] = [
                        'server_id' => $server->id,
                        'server_name' => $server->db_server_name,
                        'total_storage_size' => $server->total_current_db_storage_setup_size * 1024,
                        'total_storage_archived' => 0,
                        'total_cost_saving' => 0,
                        'total_single_server_archived' => 0,
                        'color_code' => '',
                    ];
                }
            }
            return $this->success('Dashboard server listing retrieved successfully', [
                'dashboard_servers_listing' => DashboardServersSatsResource::collection($data)
            ]);
        }
        catch (\Throwable $th)
        {
            return $this->error('Something went wrong', 400);
        }
    }

    /**
     * Dashboard Archive bar chart data api.
     * 
     * @param  \Illuminate\Http\Request  $request
     */

    public function dashboardServersBarChart(StatRequest $request)
    {
        try
        {
            $company = auth()->user()->company_id;
            // Get the date
            if ($request->flag == 'a')
            {
                $startDate = Carbon::createFromFormat('Y-m-d', DimDate::min('date'));
                $endDate = Carbon::createFromFormat('Y-m-d', DimDate::max('date'));
            }
            else
            {
                $startDate = Carbon::createFromFormat('Y-m-d', $request->start_date)->startOfDay();
                $endDate = Carbon::createFromFormat('Y-m-d', $request->end_date)->endOfDay();
            }

            // Get all servers archive stats and group by stat_date
            $servers = ClientDbServer::where('company_id', $company)
                ->with(['server_stats' => function ($query) use ($startDate, $endDate)
                {
                    $query->select('id', 'client_db_server_id',  'total_db_data_size_archived', 'stat_date')
                        ->dateRange($startDate, $endDate);
                }])
                ->select('id', 'company_id', 'db_server_name', 'color_code')
                ->whereNull('deleted_at')
                ->get();
            // Transform the grouped data into the desired format
            if ($servers->isNotEmpty())
            {
                $formattedData = [];
                $allDates = $this->getDatesBetween($startDate, $endDate);
                $formattedDates = ['dates' => $allDates];

                foreach ($servers as $server)
                {
                    $formattedData[] = [
                        'name' => $server->db_server_name,
                        'color_code' => $server->color_code,
                        'data' => [],
                    ];

                    $server_data = $server->server_stats->pluck('stat_date')->unique()->toArray();

                    // Merge unique stats dates into formattedDates array
                    $formattedDates['dates'] = array_merge($formattedDates['dates'], $server_data);
                    foreach ($server_data as $key)
                    {
                        $formattedData[count($formattedData) - 1]['data'][$key] = $server->server_stats
                            ->where('stat_date', $key)
                            ->sum('total_db_data_size_archived');
                    }
                }
                $formattedDates['dates'] = array_unique($formattedDates['dates']);
                asort($formattedDates['dates']);
                $result = $this->clubDataWithFlag($formattedDates, $formattedData, $request->flag);

                return $this->success(
                    'Dashboard servers bar chart stats retrieved successfully',
                    [
                        'dashboard_barChart' =>  DashboardServerPieChartDataResource::collection($result)
                    ]

                );
            }
            else
            {
                return $this->success('Dashboard servers not found', []);
            }
        }
        catch (\Throwable $th)
        {
            return $this->error('Something went wrong', 400);
        }
    }

    /**
     * Dashboard servers activity api.
     * 
     * @param  \Illuminate\Http\Request  $request
     */

    public function dashboardActivityLogs(StatRequest $request)
    {
        try
        {
            $company = auth()->user()->company_id;
            if ($request->flag == 'a')
            {
                $startDate = Carbon::createFromFormat('Y-m-d', DimDate::min('date'));
                $endDate = Carbon::createFromFormat('Y-m-d', DimDate::max('date'));
            }
            else
            {
                $startDate = Carbon::createFromFormat('Y-m-d', $request->start_date)->startOfDay();
                $endDate = Carbon::createFromFormat('Y-m-d', $request->end_date)->endOfDay();
            }

            $serverData = ClientDbServer::with([
                'client_databases_instanses.client_db_tables' => function ($query)
                {
                    $query->select('id', 'client_database_id', 'client_db_schema_id', 'table_name');
                },
                'client_databases_instanses.client_db_tables.export_history' => function ($query) use ($startDate, $endDate)
                {
                    $query->select('id', 'client_db_server_table_id', 'file_export_started_at', 'file_export_ended_at', 'export_status', 'is_export_error', 'updated_at')
                        ->when($startDate && $endDate, function ($query) use ($startDate, $endDate)
                        {
                            $query->whereBetween('created_at', [$startDate, $endDate])
                                ->orderBy('created_at', 'desc');
                        });
                }
            ])
                ->where('company_id', $company)
                ->whereNull('deleted_at')
                ->select('id', 'db_server_name')
                ->get();
            if ($serverData->isEmpty())
            {
                return $this->success('Servers data not found!', []);
            }
            $resultData = [];

            foreach ($serverData as $server)
            {
                foreach ($server->client_databases_instanses as $clientDb)
                {
                    foreach ($clientDb->client_db_tables as $table)
                    {
                        foreach ($table->export_history as $exportHistory)
                        {
                            $exportStartedAt = Carbon::parse($exportHistory->file_export_started_at);
                            $exportEndedAt = Carbon::parse($exportHistory->file_export_ended_at);
                            $status = $exportHistory->export_status;
                            $startedAt = $exportHistory->file_export_started_at;
                            $resultData[] = [
                                'table_id' => $table->id,
                                'server_id' => $server->id,
                                'server_name' => $server->db_server_name,
                                'database_id' => $clientDb->id,
                                'database_name' => $clientDb->db_name,
                                'table_name' => $table->table_name,
                                'export_history' => [
                                    'status' => $status,
                                    'started_at' => $startedAt,
                                    'ended_at' => $exportHistory->file_export_ended_at,
                                    'is_error' => $exportHistory->is_export_error,
                                    'updated_at' => $exportHistory->updated_at,
                                    'total_time' => $exportHistory->file_export_ended_at != null ? $exportStartedAt->diffInSeconds($exportEndedAt) / 60 : null,
                                ],
                            ];
                        }
                    }
                }
            }
            usort($resultData, function ($a, $b)
            {
                return strtotime($b['export_history']['started_at']) - strtotime($a['export_history']['started_at']);
            });
            return $this->success('Dashboard all activities retrieved successfully', [
                'dashboard_activities' => DashboardActivityResource::collection($resultData)
            ]);
        }
        catch (\Throwable $th)
        {
            return $this->error('Something went wrong', 400);
        }
    }


    /**
     * Dashboard single server archive data api.
     * 
     * @param  \Illuminate\Http\Request  $request
     */
    public function serversTotalArchiveStats(ServersStatsArchiveRequest $request)
    {
        try
        {
            $company = auth()->user()->company_id;
            $server_id = $request->id;

            // Get the date
            if ($request->flag == 'a')
            {
                $startDate = null;
                $endDate = null;
            }
            else
            {
                $startDate = Carbon::createFromFormat('Y-m-d', $request->start_date)->startOfDay();
                $endDate = Carbon::createFromFormat('Y-m-d', $request->end_date)->endOfDay();
            }

            // Fetch the server data, including stats and table stats
            $server_data = ClientDbServer::where('company_id', $company)
                ->where('id', $server_id)
                ->with(['server_stats' => function ($query) use ($startDate, $endDate)
                {
                    $query->select('id', 'client_db_server_id', 'total_db_data_size_archived', 'db_name', 'total_table_storage_cost_saving')
                        ->when(isset($startDate) && isset($endDate), function ($query) use ($startDate, $endDate)
                        {
                            $query->dateRange($startDate, $endDate);
                        });
                }])
                ->with(['server_table_stats' => function ($query) use ($startDate, $endDate)
                {
                    $query->select('id', 'client_db_server_id', 'client_db_server_table_id')
                        ->when(isset($startDate) && isset($endDate), function ($query) use ($startDate, $endDate)
                        {
                            $query->dateRange($startDate, $endDate);
                        });
                }])
                ->whereNull('deleted_at')
                ->first();

            // Check if the server exists
            if (!$server_data)
            {
                return $this->success('Server not found', []);
            }
            $stats = new stdClass();
            $stats->server_name = $server_data->db_server_name;
            $stats->total_archive_size = $server_data->server_stats->sum('total_db_data_size_archived')/1024;
            $stats->total_db_archive = $server_data->server_stats->groupBy('client_database_id')->count();
            $stats->total_table_archive = $server_data->server_table_stats->groupBy('client_db_server_table_id')->count();
            $statsArray = [$stats];
            return $this->success('Server total archive stats retrieved successfully', [
                'server_total_archive_stats' => ServerStatsArchiveResource::collection($statsArray)
            ]);
        }
        catch (\Throwable $th)
        {
            return $this->error('Something went wrong', 400);
        }
    }

    /**
     * Dashboard single server barchart data api.
     * 
     * @param  \Illuminate\Http\Request  $request
     */
    public function serverDatabasesBarChart(ServersStatsArchiveRequest $request)
    {
        try
        {
            $company = auth()->user()->company_id;
            $server_id = $request->id;

            if ($request->flag == 'a')
            {
                $startDate = null;
                $endDate = null;
            }
            else
            {
                $startDate = Carbon::createFromFormat('Y-m-d', $request->start_date)->startOfDay();
                $endDate = Carbon::createFromFormat('Y-m-d', $request->end_date)->endOfDay();
            }
            $server = ClientDbServer::with([
                'client_databases_instanses.db_server_tables_stats_instanses' => function ($query) use ($startDate, $endDate)
                {
                    $query->when($startDate && $endDate, function ($query) use ($startDate, $endDate)
                    {
                        $query->dateRange($startDate, $endDate)->orderBy('stat_date', 'asc');
                    });
                },
            ])
                ->where('id', $server_id)
                ->where('company_id', $company)
                ->select('id', 'db_server_name')
                ->first();
            if ($server == null)
            {
                return $this->success('Server not found.', []);
            }
            $allDates = $this->getDatesBetween($startDate, $endDate);
            $formattedDates = ['dates' => $allDates];

            $formattedData = [];
            if ($server->client_databases_instanses->isNotEmpty())
            {
                foreach ($server->client_databases_instanses as $client_db_instanse)
                {
                    $formattedData[] = [
                        'name' => $client_db_instanse->db_name,
                        'color_code' => $client_db_instanse->color_code,
                        'data' => [],
                    ];

                    $client_db_instanse_data = $client_db_instanse->db_server_tables_stats_instanses
                        ->groupBy('stat_date')
                        ->map(function ($items)
                        {
                            return $items->sum('total_table_data_size_archived');
                        });
                    foreach ($client_db_instanse_data as $key => $client_db_instanse_dat)
                    {
                        if (!in_array($key, $formattedDates['dates']))
                        {
                            $formattedDates['dates'][] = $key;
                        }
                        $formattedData[count($formattedData) - 1]['data'][$key] = $client_db_instanse_dat;
                    }
                }
                asort($formattedDates['dates']);

                $result = $this->clubDataWithFlag($formattedDates, $formattedData, $request->flag);

                return $this->success('Server bar chart stats retrieved successfully', [
                    'server_barChart' => DashboardServerPieChartDataResource::collection($result)
                ]);
            }
            else
            {
                return $this->success('Server databases not found', []);
            }
        }
        catch (\Throwable $th)
        {
            return $this->error('Something went wrong', 400);
        }
    }

    /**
     * Dashboard single server databsase listing data api.
     * 
     * @param  \Illuminate\Http\Request  $request
     */
    public function serverDatabasesListing(ServersStatsArchiveRequest $request)
    {
        try
        {
            $company = auth()->user()->company_id;
            $server_id = $request->id;

            // Get the date
            if ($request->flag == 'a')
            {
                $startDate = null;
                $endDate = null;
            }
            else
            {
                $startDate = Carbon::createFromFormat('Y-m-d', $request->start_date)->startOfDay();
                $endDate = Carbon::createFromFormat('Y-m-d', $request->end_date)->endOfDay();
            }
            // Get the data of servers with stats
            $servers = ClientDbServer::with(['client_databases_instanses.db_server_tables_stats' => function ($query) use ($startDate, $endDate)
            {
                $query->select('id', 'client_db_server_id', 'client_database_id', 'total_db_storage_setup', 'total_table_data_size_archived', 'total_table_index')
                    ->when($startDate && $endDate, function ($query) use ($startDate, $endDate)
                    {
                        $query->dateRange($startDate, $endDate);
                    });
            }])->select('id', 'company_id', 'db_server_name')
                ->where('company_id', $company)
                ->where('id', $server_id)
                ->whereNull('deleted_at')
                ->get();

            $data = $servers->flatMap(function ($server)
            {
                return $server->client_databases_instanses->map(function ($database) use ($server)
                {

                    $stats = $database->db_server_tables_stats;

                    $totalStorageArchived = $stats->sum(function ($stat) use ($stats)
                    {
                        $totalDatabaseSize = $stat->total_db_storage_setup;
                        return ($stat->total_table_data_size_archived != 0)
                            ? round((($stat->total_table_data_size_archived / $totalDatabaseSize) * 100), 2)
                            : 0;
                    });
                    return [
                        'id' => $database->id,
                        'server_id' => $database->client_db_server_id,
                        'database_name' => $database->db_name,
                        'color_code' => $database->color_code,
                        'total_storage_archived' => $stats->sum('total_table_data_size_archived'),
                        'total_storage_archived_percentage' => $totalStorageArchived,
                        'total_tables_archived' => $server->server_table_stats->where('client_db_schema_id', $database->id)->groupBy('table_name')->count('table_name'),
                    ];
                });
            });

            return $this->success('Server databases list retrieved successfully', [
                'server_databases_list' => DashboardServersSatsResource::collection($data)
            ]);
        }
        catch (\Throwable $th)
        {
            return $this->error('Something went wrong', 400);
        }
    }
}
