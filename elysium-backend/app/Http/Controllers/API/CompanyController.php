<?php

namespace App\Http\Controllers\API;

use Exception;
use App\Models\Company;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\CompanyResource;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\StoreCompanyRequest;
use App\Http\Requests\UpdateCompanyRequest;

class CompanyController extends Controller
{
    use ApiResponser;

	/**
     * Display a listing of the resource.
     */
    public function index()
    {
		try {
			$companies = Company::where('is_active', 0)->get();

			return $this->success('All companies retrieved successfully', [
				'companies' => CompanyResource::collection($companies)
			]);
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

	/**
     * Store a newly created resource in storage.
     */
    public function store(StoreCompanyRequest $request)
    {
        try {
			$company = Company::create([
				'company_name' => $request->company_name,
				'country_id' => $request->country_id,
				'state_id' => $request->state_id,
				'company_address' => $request->company_address,
				'company_address2' => $request->company_address2,
				'company_city' => $request->company_city,
				'company_postalcode' => $request->company_postalcode,
				'is_active' => $request->is_active ?? 0
			]);

			return $this->success('Company created successfully!',
				[
					'company' => new CompanyResource($company)
				]
			);
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

	/**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
			$company = Company::find($id);
			if(!$company) {
				return $this->error('No record found', 404);
			} else {
				return $this->success('Company retrieved successfully!',
					[
						'company' => new CompanyResource($company)
					]
				);
			}
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

	/**
     * Update the specified resource in storage.
     */
    public function update(UpdateCompanyRequest $request, string $id)
    {
        try {
			$company = Company::find($id);

			if(!$company) {
				return $this->error('No record found', 404);
			} else {
				$company->company_name =  $request->company_name;
				$company->country_id =  $request->country_id;
				$company->state_id =  $request->state_id;
				$company->company_address =  $request->company_address;
				$company->company_address2 =  $request->company_address2;
				$company->company_city =  $request->company_city;
				$company->company_postalcode =  $request->company_postalcode;
				$company->is_active = $request->is_active ?? 0;
				$company->save();
			}

			return $this->success('Company updated successfully!',
				[
					'company' => new CompanyResource($company)
				]
			);
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

	/**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
		try {
			$company = Company::find($id);
			if(!$company) {
				return $this->error('No record found', 404);
			}
			else {
				$company->delete();
				return $this->success('Company deleted successfully!', []);
			}
		}
		catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }
}
