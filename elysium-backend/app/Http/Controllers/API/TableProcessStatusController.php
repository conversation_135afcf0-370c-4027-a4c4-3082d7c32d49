<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Exception;
use App\Traits\ApiResponser;
use App\Models\TableProcessStatus;
use Illuminate\Support\Facades\Log;
use App\Http\Resources\TableProcessStatusResource;

class TableProcessStatusController extends Controller
{
	use ApiResponser;

    /**
	 * Get all table process statusses list
	 */
	public function getTableProcessStatusess(Request $request)
	{
		try {
			$tableProcessStatusses = TableProcessStatus::get();

			return $this->success('All table process statusess retrieved successfully', [
				'tableProcessStatusses' => TableProcessStatusResource::collection($tableProcessStatusses)
			]);
		} catch (\Throwable $th) {
			Log::info($th->getMessage());
			return $this->error('Something went wrong', 400);
		}
	}
}
