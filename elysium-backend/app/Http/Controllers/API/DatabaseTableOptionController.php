<?php

namespace App\Http\Controllers\API;

use Exception;
use App\Models\DatabaseTableOption;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\DatabaseTableOptionRequest;
use App\Http\Resources\DatabaseTableOptionResource;

class DatabaseTableOptionController extends Controller
{
	use ApiResponser;

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        try {
			$tableOptions = DatabaseTableOption::all();

			return $this->success('All database table options retrieved successfully', [
				'tableOptions' => DatabaseTableOptionResource::collection($tableOptions)
			]);
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(DatabaseTableOptionRequest $request)
    {
		try {
			$tableOption = DatabaseTableOption::create([
				'name' => $request->name
			]);

			return $this->success('Database table option created successfully!',
				[
					'tableOption' => new DatabaseTableOptionResource($tableOption)
				]
			);
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
			$tableOption = DatabaseTableOption::find($id);
			if(!$tableOption) {
				return $this->error('No record found', 404);
			} else {
				return $this->success('Database table option retrieved successfully!',
					[
						'tableOption' => new DatabaseTableOptionResource($tableOption)
					]
				);
			}
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(DatabaseTableOptionRequest $request, string $id)
    {
        try {
			$tableOption = DatabaseTableOption::find($id);

			if(!$tableOption) {
				return $this->error('No record found', 404);
			} else {
				$tableOption->name =  $request->name;
				$tableOption->save();
			}

			return $this->success('Database table option updated successfully!',
				[
					'tableOption' => new DatabaseTableOptionResource($tableOption)
				]
			);
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
			$tableOption = DatabaseTableOption::find($id);
			if(!$tableOption) {
				return $this->error('No record found', 404);
			}
			else {
				$tableOption->delete();
				return $this->success('Database table option deleted successfully!', []);
			}
		}
		catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }
}
