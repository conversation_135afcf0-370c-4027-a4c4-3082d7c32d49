<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ClientDBServerTable;
use App\Models\ClientDBSchema;
use App\Models\ClientDatabase;
use App\Traits\ApiResponser;
use Illuminate\Support\Facades\Log;
use App\Http\Resources\ClientDBServerTableResource;

class ClientDBServerTableController extends Controller
{
	use ApiResponser;

    /**
	 * Get specific remote server databases list
	 */
	public function getAllDatabasesTablesList(Request $request) {
		try {
			$startTime = microtime(true);
			$dbSchemaId = $request->client_db_schema_id ?? null;

			if(!$dbSchemaId) {
				return $this->error('Invalid schema ID!', 400);
			}

			$dbSchema = ClientDBSchema::find($dbSchemaId);
			Log::info("dbSchemaId ==>" . $dbSchemaId);

			if(!$dbSchema) {
				return $this->error('Schema not found!', 400);
			}

			$dbServersTablesList = ClientDBServerTable::where('client_db_schema_id', $dbSchemaId)->get();

			$endTime = microtime(true);
			$executionTime = $endTime - $startTime;
			Log::info("Get All databases tables Query took " . $executionTime . " seconds to execute.");

			return $this->success('All schema tables retrieved successfully', [
				'dbTables' => ClientDBServerTableResource::collection($dbServersTablesList)
			]);

		} catch (\Throwable $th) {
			Log::error($th);
			return $this->error('Something went wrong!', 400);
		}
	}
}
