<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Traits\ApiResponser;
use App\Models\RemoteServer;
use App\Http\Requests\RemoteServerRequest;
use App\Http\Resources\RemoteServerResource;
use App\Http\Requests\UpdateRemoteServerRequest;
use App\Traits\VerifyRemoteServerConnection;
use Illuminate\Support\Facades\Log;
use App\Jobs\sendRemoteServerConnectionVerificationEmail;

class RemoteServerController extends Controller
{
    use ApiResponser;

	/**
     * Display a listing of the resource.
     */
    public function getAllRemoteServer(Request $request)
    {
        try {
			$company_id = Auth::user()->company_id;

			$status_id = $request->status_id ?? null;

			$agents = RemoteServer::where('is_active', 1);

			if($status_id) {
				$agents = $agents->where('remote_server_status_id', $status_id);
			}

			$agents = $agents->where('is_deleted', 0)->where('company_id', $company_id)->get();

			return $this->success('All remote servers retrieved successfully', [
				'agents' => RemoteServerResource::collection($agents)
			]);
		} catch (\Throwable $th) {
			return $this->error('Something went wrong!', 400);
		}
    }


    /**
     * Store a newly created resource in storage.
     */
    public function createRemoteServer(RemoteServerRequest $request)
    {
        try {
			$serverName = $request->name;
			$hostname = $request->hostname;
			$username = $request->username;
			$port = $request->port ?? 22;
			$isTlsRequired = $request->has('is_tls_required') ? $request->is_tls_required : 0;
			$uuid = $request->agent_uuid;

			$remoteServerConnection = VerifyRemoteServerConnection::connect($hostname, $username, $port, $isTlsRequired);

			if($remoteServerConnection && isset($remoteServerConnection['status']) && $remoteServerConnection['status'] === true) {
				$agentFilterID = 1;
				$osType = $remoteServerConnection['osType'];
			} else {
				$agentFilterID = 3;
				$osType = NULL;
			}

			$this->sendRemoteConnectionStatusEmail($hostname, $serverName, $agentFilterID);

			$agent = new RemoteServer;
			$agent->name = $serverName;
			$agent->hostname = $hostname;
			$agent->username = $username;
			$agent->port = $port;
			$agent->is_tls_required = $isTlsRequired;
			$agent->agent_uuid = $uuid;
			$agent->os_type = $osType;
			$agent->is_active = $request->has('is_active') ? $request->is_active: 0;
			$agent->remote_server_status_id = $agentFilterID;
			$agent->company_id = Auth::user()->company_id;
			$agent->save();

			return $this->success('Agent was successfully added',
				[
					'remoteServer' => new RemoteServerResource($agent)
				]
			);

		} catch (\Throwable $th) {
			Log::info($th);
			return $this->error('Something went wrong!', 400);
		}
    }

    /**
     * Display the specified resource.
     */
    public function getRemoteServer(string $id)
    {
        try {
			$agent = RemoteServer::find($id);

			if(!$agent) {
				return $this->error('No record found', 404);
			}

			return $this->success('Agent data retrieved successfully!',
				[
					'agent' => new RemoteServerResource($agent)
				]
			);

		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

    /**
     * Update the specified resource in storage.
     */
    public function updateRemoteServer(UpdateRemoteServerRequest $request, string $id)
    {
		try {
			$agent = RemoteServer::find($id);

			if(!$agent) {
				return $this->error('Invalid agent id', 400);
			}

			$serverName = $request->name;
			$hostname = $request->hostname;
			$username = $request->username;
			$port = $request->port ?? 22;
			$isTlsRequired = $request->is_tls_required;
			$uuid = $request->agent_uuid;

			$remoteServerConnection = VerifyRemoteServerConnection::connect($hostname, $username, $port, $isTlsRequired);


			if($remoteServerConnection && isset($remoteServerConnection['status']) && $remoteServerConnection['status'] === true) {
				$agentFilterID = 1;
				$osType = $remoteServerConnection['osType'];
			} else {
				$agentFilterID = 3;
				$osType = NULL;
			}

			$this->sendRemoteConnectionStatusEmail($hostname, $serverName, $agentFilterID);

			$agent->name = $serverName;
			$agent->hostname = $hostname;
			$agent->username = $username;
			$agent->port = $port;
			$agent->is_tls_required = $isTlsRequired;
			$agent->agent_uuid = $uuid;
			$agent->os_type = $osType;
			$agent->is_active = $request->is_active;
			$agent->remote_server_status_id = $agentFilterID;
			$agent->company_id = Auth::user()->company_id;
			$agent->save();

			return $this->success('Agent #'.$id. ' has been updated',
				[
					'agent' => new RemoteServerResource($agent)
				]
			);

		} catch (\Throwable $th) {
			Log::info($th);
			return $this->error('Something went wrong!', 400);
		}
    }

    /**
     * Remove the specified resource from storage.
     */
    public function deleteRemoteServer(Request $request)
    {
		try {
			$ids = $request->ids;

			if($ids) {

				foreach($ids as $id) {
					$agent = RemoteServer::find($id);
					if(!$agent) {
						return $this->error('Invalid agent ID', 404);
					}

					$agent->is_deleted = 1;
					$agent->save();

					$agent->delete();
				}

				return $this->success('Deleted successfully!', []);

			} else {
				return $this->error('No agent ID provided', 404);
			}



		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

	/**
	 * Send Remote Connection Status Email
	 *
	 */
	public function sendRemoteConnectionStatusEmail($hostName = '', $serverName = '', $statusId = 3) {
		if($hostName && $serverName) {
			$user = Auth::user();

			if($user)  {
				$firstName = $user->first_name;
				$email = $user->email;
			}


			$data = [
				'firstName' => $firstName,
				'hostName' => $hostName,
				'serverName' => $serverName,
				'statusId' => $statusId,
				'email' => $email

			];

			dispatch(new sendRemoteServerConnectionVerificationEmail($data));
		}
	}

	/**
	 * Test connection to a remote server
	 *
	 * @param int $id
	 * @return \Illuminate\Http\JsonResponse
	 */
	public function testRemoteServerConnection($id)
	{
		try {
			$agent = RemoteServer::findOrFail($id);
			
			// Start logging connection details
			$connectionLogs = [];
			$connectionLogs[] = "Starting connection test to {$agent->hostname}...";
			$connectionLogs[] = "Using SSH port: {$agent->port}";
			$connectionLogs[] = "Using username: {$agent->username}";
			
			// Test the connection
			$remoteServerConnection = VerifyRemoteServerConnection::connect(
				$agent->hostname, 
				$agent->username, 
				$agent->port, 
				$agent->is_tls_required,
				$connectionLogs // Pass by reference to collect logs
			);

			if($remoteServerConnection && isset($remoteServerConnection['status']) && $remoteServerConnection['status'] === true) {
				$agentFilterID = 1; // Connected
				$osType = $remoteServerConnection['osType'];
				$message = "Connection to Agent #{$id} was successful.";
				$connectionLogs[] = "Connection successful!";
				$connectionLogs[] = "Detected OS: {$osType}";
			} else {
				$agentFilterID = 3; // Failed
				$osType = NULL;
				$message = "Connection to Agent #{$id} failed. Please check credentials and server availability.";
				$connectionLogs[] = "Connection failed!";
			}
			
			// Update the agent status
			$agent->remote_server_status_id = $agentFilterID;
			$agent->os_type = $osType;
			$agent->save();
			
			$connectionLogs[] = "Updated agent status in database.";
			
			// Send email notification
			$this->sendRemoteConnectionStatusEmail($agent->hostname, $agent->name, $agentFilterID);
			
			return $this->success($message, [
				'agent' => new RemoteServerResource($agent),
				'logs' => $connectionLogs
			]);
			
		} catch (\Throwable $th) {
			Log::error($th);
			return $this->error('Something went wrong while testing the connection!', 400, [
				'logs' => ["Error: {$th->getMessage()}"]
			]);
		}
	}

	/**
	 * Run network diagnostics for a remote server
	 *
	 * @param int $id
	 * @return \Illuminate\Http\JsonResponse
	 */
	public function runNetworkDiagnostics($id)
	{
		try {
			$agent = RemoteServer::findOrFail($id);
			$hostname = $agent->hostname;
			$port = $agent->port;
			
			$diagnosticLogs = [];
			$diagnosticLogs[] = "Running network diagnostics for {$hostname}:{$port}...";
			
			// Check if the hostname resolves
			$ip = gethostbyname($hostname);
			if ($ip === $hostname) {
				$diagnosticLogs[] = "WARNING: Could not resolve hostname '{$hostname}' to an IP address.";
				$diagnosticLogs[] = "This suggests a DNS resolution issue or an incorrect hostname.";
			} else {
				$diagnosticLogs[] = "Hostname '{$hostname}' resolves to IP: {$ip}";
			}
			
			// Ping the host
			$diagnosticLogs[] = "Attempting to ping {$hostname}...";
			$pingCommand = "ping -c 3 -W 5 {$hostname} 2>&1";
			exec($pingCommand, $pingOutput, $pingReturnVar);
			
			if ($pingReturnVar === 0) {
				$diagnosticLogs[] = "Ping successful. Server is reachable.";
				foreach ($pingOutput as $line) {
					if (strpos($line, 'time=') !== false) {
						$diagnosticLogs[] = $line;
					}
				}
			} else {
				$diagnosticLogs[] = "WARNING: Ping failed. Server may be unreachable or blocking ICMP packets.";
				$diagnosticLogs[] = "Ping output:";
				foreach ($pingOutput as $line) {
					$diagnosticLogs[] = $line;
				}
			}
			
			// Test TCP connection
			$diagnosticLogs[] = "Testing TCP connection to {$hostname}:{$port}...";
			$socket = @fsockopen($hostname, $port, $errno, $errstr, 5);
			
			if (!$socket) {
				$diagnosticLogs[] = "ERROR: Cannot connect to {$hostname}:{$port}. Error {$errno}. {$errstr}";
				$diagnosticLogs[] = "This suggests:";
				$diagnosticLogs[] = "1. The server may be behind a firewall blocking port {$port}";
				$diagnosticLogs[] = "2. SSH service may not be running on port {$port}";
				$diagnosticLogs[] = "3. The server may be completely unreachable";
				
				// Try to trace the route
				$diagnosticLogs[] = "Attempting traceroute to identify network path...";
				$traceCommand = "traceroute -w 2 -m 15 {$hostname} 2>&1";
				exec($traceCommand, $traceOutput, $traceReturnVar);
				
				$diagnosticLogs[] = "Traceroute results:";
				foreach ($traceOutput as $line) {
					$diagnosticLogs[] = $line;
				}
				
				$diagnosticLogs[] = "Recommendations:";
				$diagnosticLogs[] = "1. Verify the server is online and the hostname is correct";
				$diagnosticLogs[] = "2. Check if SSH is running on port {$port} (try a different port if needed)";
				$diagnosticLogs[] = "3. Verify firewall rules allow connections from this server";
				$diagnosticLogs[] = "4. Contact your network administrator to troubleshoot connectivity issues";
			} else {
				$diagnosticLogs[] = "TCP connection successful to {$hostname}:{$port}";
				$diagnosticLogs[] = "The server is reachable and accepting connections on port {$port}";
				fclose($socket);
				
				// Try a basic SSH handshake
				$diagnosticLogs[] = "Attempting SSH protocol handshake...";
				$sshCheckCommand = "nc -z -v -w5 {$hostname} {$port} 2>&1";
				exec($sshCheckCommand, $sshOutput, $sshReturnVar);
				
				foreach ($sshOutput as $line) {
					$diagnosticLogs[] = $line;
				}
				
				if ($sshReturnVar === 0) {
					$diagnosticLogs[] = "SSH service appears to be running on port {$port}";
				} else {
					$diagnosticLogs[] = "WARNING: Port {$port} is open but may not be running SSH service";
				}
			}
			
			return $this->success("Network diagnostics completed", [
				'logs' => $diagnosticLogs
			]);
			
		} catch (\Throwable $th) {
			Log::error($th);
			return $this->error('Failed to run network diagnostics: ' . $th->getMessage(), 400);
		}
	}
}
