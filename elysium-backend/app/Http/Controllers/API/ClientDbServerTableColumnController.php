<?php

namespace App\Http\Controllers\API;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\ClientDbServerTableColumn;

class ClientDbServerTableColumnController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(ClientDbServerTableColumn $clientDbServerTableColumn)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ClientDbServerTableColumn $clientDbServerTableColumn)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ClientDbServerTableColumn $clientDbServerTableColumn)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ClientDbServerTableColumn $clientDbServerTableColumn)
    {
        //
    }
}
