<?php

namespace App\Http\Controllers\API;

use App\Http\Resources\TimezoneResource;
use App\Http\Controllers\Controller;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use App\Models\Timezone;
use Exception;

class TimezoneController extends Controller
{
	use ApiResponser;

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        try {
			$timezones = Timezone::all();

			return $this->success('All timezones retrieved successfully', [
				'timezones' => TimezoneResource::collection($timezones)
			]);
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
			$timezone = Timezone::find($id);
			if(!$timezone) {
				return $this->error('No record found', 404);
			} else {
				return $this->success('Timezone retrieved successfully!',
					[
						'timezone' => new TimezoneResource($timezone)
					]
				);
			}
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }
}
