<?php

namespace App\Http\Controllers\API;

use Exception;
use App\Traits\ApiResponser;
use App\Models\RemoteServerFilter;
use App\Http\Controllers\Controller;
use App\Http\Resources\RemoteServerFilterResource;

class RemoteServerFilterController extends Controller
{
	use ApiResponser;
    /**
	 * Display a listing of the resource.
	 */
	public function getRemoteServerFilters() {
		try {
			$remoteServerFilters = RemoteServerFilter::where('is_active', 1)->get();

			return $this->success('All remote server filter options retrieved successfully', [
				'remoteServerFilters' => RemoteServerFilterResource::collection($remoteServerFilters)
			]);
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
	}
}
