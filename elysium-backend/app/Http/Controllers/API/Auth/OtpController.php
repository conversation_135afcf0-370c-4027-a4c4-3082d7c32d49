<?php

namespace App\Http\Controllers\API\Auth;

use Carbon\Carbon;
use App\Models\User;
use Illuminate\Http\Request;
use App\Traits\ApiResponser;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Mail;
use App\Http\Requests\OtpRequest;
use App\Http\Requests\VerifyOtpRequest;
use App\Mail\forgotPasswordVerificationEmail;

class OtpController extends Controller
{
	use ApiResponser;
    /*
    |--------------------------------------------------------------------------
    | Otp Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for sending otp to user email address for
	| user account verification
     */

	 /**
	  * Requesting OTP for email verification
	  *
	  * @return \Illuminate\Http\Response
	  */
	public function requestOtp(OtpRequest $request)
	{
		try {
			$otp = rand(100000,999999);
			$user = User::where('email','=',$request->email)->first();

			if($user){
				$user->otp = $otp;
				$user->save();

				$mail_details = [
					'full_name' => $user->first_name. ' '. $user->last_name,
					'subject' => 'Verify Email Address',
					'otp' => $otp
				];

				\Mail::to($request->email)->send(new forgotPasswordVerificationEmail($mail_details));

				return $this->success('OTP sent successfully!');
			} else {
				return $this->error('Email is not registered', 200);
			}
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
	}

	public function verifyOtp(VerifyOtpRequest $request){

        $user  = User::where([['email','=',$request->email],['otp','=',$request->otp]])->first();

        if($user){
			/**
			 * Validate if last updated code is more then 60 seconds old
			 * send message of invalid code due to time limit (60 seconds) exceeds
			 */
			$currentTime = Carbon::now();
			$time = $user->updated_at->diffInSeconds($currentTime);
			if($time > 65) {
				return $this->error('Invalid code, time limit of 60 seconds exceeds, please request new OTP', 200);
			}

            auth()->login($user, true);
            User::where('email','=',$request->email)->update(['otp' => null, 'is_active' => 1]);

			return $this->success('Verification completed successfully!',
				[
					'token' => auth()->user()->createToken('API Token')->plainTextToken
				]
			);
        } else {
            return $this->error('Please enter valid code', 200);
        }
    }
}
