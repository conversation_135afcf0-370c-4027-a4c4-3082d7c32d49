<?php

namespace App\Http\Controllers\API\Schedule;

use Carbon\Carbon;
use App\Traits\ApiResponser;
use App\Models\DBJobSchedule;
use App\Models\ClientDatabase;
use App\Models\ClientDbServer;
use Illuminate\Support\Facades\DB;
use App\Models\ClientDBServerTable;
use App\Models\ClientObjectStorage;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\RestoreSchedule;
use App\Models\ScheduleProcessingList;
use App\Http\Requests\addScheduleRequest;
use App\Http\Requests\GetDatabaseRequest;
use App\Http\Requests\EditScheduleRequest;
use App\Http\Requests\ScheduleTimeRequest;
use App\Http\Requests\TableListingRequest;
use App\Http\Requests\UpdateScheduleRequest;
use App\Http\Requests\RestoreDatabaseSchedule;
use App\Http\Resources\ClientDbServerResource;
use App\Http\Resources\ClientDbTablesResource;
use App\Http\Requests\DeleteSingleScheduleRequest;
use App\Http\Requests\DeleteDatabaseScheduleRequest;
use App\Http\Resources\ClientDatabaseServerResource;

class ScheduleController extends Controller
{
    use ApiResponser;

    public function serverListing()
    {
        try
        {
            $company_id = auth()->user()->company_id;
            $servers = ClientDbServer::where('company_id', $company_id)
                ->where('is_active', 1)
                ->where('is_deleted', 0)
                ->select('id', 'db_server_name')
                ->get();

            if ($servers->isNotEmpty())
            {
                return $this->success('Servers retrieved successfully', [
                    'server_listing' =>  ClientDbServerResource::collection($servers)
                ]);
            }
            else
            {
                return $this->success('Servers data not found!', []);
            }
        }
        catch (\Throwable $th)
        {
            return $this->error('Something went wrong', 400);
        }
    }
    /**
     *get Databases By server id for scheduling setup
     */
    public function databaseByServerId(GetDatabaseRequest $request)
    {
        try
        {
            $server_id = $request->server_id;
            $databases = ClientDatabase::where('client_db_server_id', $server_id)
                ->where('is_deleted', 0)
                ->select('id', 'db_name')
                ->get();
            if ($databases->isNotEmpty())
            {
                return $this->success('Servers databases retrieved successfully', [
                    'database_listing' =>  ClientDatabaseServerResource::collection($databases)
                ]);
            }
            else
            {
                return $this->success('Servers databases not found!', []);
            }
        }
        catch (\Throwable $th)
        {
            return $this->error('Something went wrong', 400);
        }
    }
    /**
     * get tables listing by Client database Id 
     */
    public function tablesListingByDbId(TableListingRequest $request)
    {
        try
        {
            $server_id = $request->server_id;
            $database_id = $request->database_id;
            $tables = ClientDBServerTable::with(['schedule_process_list'], function ($query)
            {
                $query->whereNull('deleted_at');
            })
                ->where('client_db_server_id', $server_id)
                ->where('client_database_id', $database_id)
                ->select('id', 'table_name', 'client_db_server_id', 'client_database_id', 'table_database_name', 'total_current_table_rows','is_dropped')
                ->get();
            if ($tables->isNotEmpty())
            {
                foreach ($tables as $table)
                {
                    if (isset($table->schedule_process_list->schedule_type) && $table->schedule_process_list->schedule_type == 'Archive' && isset($table->schedule_process_list->is_active) && $table->schedule_process_list->is_active == 1)
                    {
                        $export_status = "Archived";
                    }
                    else if (isset($table->schedule_process_list->schedule_type) && $table->schedule_process_list->schedule_type == 'Export' && isset($table->schedule_process_list->is_active) && $table->schedule_process_list->is_active == 1)
                    {
                        $export_status = "Exported";
                    }
                    else
                    {
                        $export_status = "";
                    }
                    $current_status = 'none';
                    if (isset($table->schedule_process_list->is_current))
                    {
                        $table->schedule_process_list->is_current == 1 ? $current_status = 'added to list' : $current_status = 'none';
                    }
                    $data[] = [
                        'server_id' =>  $table->client_db_server_id,
                        'database_id' => $table->client_database_id,
                        'table_id' =>  $table->id,
                        'table_name' =>  $table->table_name,
                        'total_rows' =>  $table->total_current_table_rows,
                        'batch_size' => $table->schedule_process_list->batchsize ?? 1000,
                        'retention' => $table->schedule_process_list->retention_days ?? 0,
                        'retention_index' => $table->schedule_process_list->retention_index ?? 0,
                        'is_current' => $current_status,
                        'is_partitioned' => $table->schedule_process_list->is_partitioned ?? 'N/A',
                        'schedule_status' => $export_status,
                        'is_dropped' => $table->is_dropped ?? 0,
                    ];
                }
                return $this->success('All database tables retrieved successfully', [
                    'db_tables' => ClientDbTablesResource::collection($data)
                ]);
            }
            else
            {
                return $this->success('Tables not Found!', []);
            }
        }
        catch (\Throwable $th)
        {
            return $this->error('Something went wrong', 400);
        }
    }

    /**
     * add Tables in schedule listing 
     */
    public function addScheduleListing(addScheduleRequest $request)
    {
        try
        {
            DB::beginTransaction();
            $ids = $request->table_ids;
            $server_id = $request->server_id;
            $database_id = $request->database_id;
            $server_data = ClientDbServer::with(['client_databases_instanses' => function ($query) use ($database_id)
            {
                $query->where('id', $database_id);
            }])
                ->where('id', $server_id)
                ->select('id', 'db_server_name', 'hostname', 'port', 'client_db_server_uuid')->first();
            $remote_server_data = ClientDbServer::with('remote_server')->where('id', $server_id)->first();
            $agent_uuid = $remote_server_data->remote_server['agent_uuid'];
            $db_schedule = DBJobSchedule::updateOrCreate(
                ['client_database_id' => $database_id, 'client_db_server_id' => $server_id],
                [
                    'client_db_server_id' => $server_id,
                    'server_name' => $server_data->db_server_name,
                    'client_database_id' => $database_id,
                    'database_name' => $server_data->client_databases_instanses->first()->db_name,
                ]
            );

            $db_schedule_id = $db_schedule->id;
            $tables = ClientDBServerTable::with(['schedule_process_list'], function ($query)
            {
                $query->whereNull('deleted_at');
            })
                ->whereIn('id', $ids)
                ->where('client_database_id', $database_id)
                ->select('id', 'table_name', 'client_db_server_id', 'client_database_id', 'table_database_name', 'total_current_table_rows')
                ->get();
            $company_id = auth()->user()->company_id;
            $storage = ClientObjectStorage::with('objectStorageType')
                ->where('company_id', $company_id)
                ->first();
            $bucket_name = $storage->bucket_name;
           
            if ($tables->isNotEmpty())
            {
                foreach ($tables as $table)
                {
                    ScheduleProcessingList::updateOrCreate(['client_db_server_table_id' => $table->id], [
                        'client_db_server_table_id' =>  $table->id,
                        'database_name' => $server_data->client_databases_instanses->first()->db_name,
                        'agent_uuid' => $agent_uuid,
                        'database_host' => $server_data->hostname,
                        'database_port' => $server_data->port,
                        'database_job_schedule_id' => $db_schedule_id,
                        'table_name' =>  $table->table_name,
                        'total_rows' =>  $table->total_current_table_rows,
                        'batch_size' => $table->schedule_process_list->batch_size ?? 10000,
                        'data_retention_days' => $table->schedule_process_list->data_retention_days ?? 60,
                        'retention_index' => $table->schedule_process_list->retention_index ?? 0,
                        'is_partitioned' => $table->schedule_process_list->is_partitioned ?? 0,
                        'is_current' => $table->schedule_process_list->is_current ?? 1,
                        'object_backup_location' => $table->table_name,
                        'schedule_type' => $table->schedule_process_list->schedule_type ?? '',
                        's3_file' => $bucket_name,
                    ]);
                }
                DB::commit();
                return $this->success('Table schedule Added successfully');
            }
        }
        catch (\Throwable $th)
        {
            DB::rollBack();
            return $this->error($th . 'Something went wrong', 400);
        }
    }

    /**
     * get all schedule listing of databases and tables
     */
    public function scheduleListing()
    {
        try
        {
            $company_id = Auth::user()->company_id;
            $getserver_data = ClientDbServer::with(['client_databases_instanses'])
                ->where('company_id', $company_id)
                ->select('id', 'db_server_name', 'company_id')->get();
            $server_ids = [];
            $database_ids = [];
            foreach ($getserver_data as $single_server)
            {
                $server_ids[] = $single_server->id;
                foreach ($single_server->client_databases_instanses as $single_database)
                {
                    $database_ids[] = $single_database->id;
                }
            }

            $schedules = DBJobSchedule::with('table_job_schedule')
                ->whereIn('client_db_server_id', $server_ids)
                ->whereIn('client_database_id', $database_ids)
                ->get();
            $data = [];
            if ($schedules->isNotEmpty())
            {
                $server_data = [];
                foreach ($schedules as $key => $single_schedule)
                {
                    $server_id = $single_schedule->client_db_server_id;

                    if (in_array($server_id, array_keys($server_data)))
                    {
                        $server_data[$server_id]['database_data'][$key]['db_schedule_id'] = $single_schedule->id;
                        $server_data[$server_id]['database_data'][$key]['database_id'] = $single_schedule->client_database_id;
                        $server_data[$server_id]['database_data'][$key]['database_name'] = $single_schedule->database_name;
                        $server_data[$server_id]['database_data'][$key]['schedule_data'] = [];
                        foreach ($single_schedule->table_job_schedule as $schedule)
                        {
                            $selected_days = [];
                            $binary_number = strrev(decbin($schedule->archive_day_of_week));
                            $days_of_Week = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
                            for ($i = 0; $i < strlen($binary_number); $i++)
                            {
                                if ($binary_number[$i] === '1')
                                {
                                    $selected_days[] = $days_of_Week[$i];
                                }
                            }
                            $start_date = $schedule->archive_start_at_utc ?  Carbon::createFromFormat('H:i:s', $schedule->archive_start_at_utc , 'UTC')->setTimezone($schedule->local_time_zone)->format('h:i A') : null;
                            $end_date = $schedule->archive_untill_date_utc ?  Carbon::createFromFormat('H:i:s', $schedule->archive_untill_date_utc , 'UTC')->setTimezone($schedule->local_time_zone)->format('h:i A') : null;
                            $table_data = [
                                'schedule_id' => $schedule->id,
                                'table_name' => $schedule->table_name,
                                'total_rows' => $schedule->total_rows,
                                'batch_size' => $schedule->batch_size ?? 1000,
                                'retention' => $schedule->data_retention_days ?? 0,
                                'retention_index' => $schedule->retention_index ?? 0,
                                'day_of_week' => $selected_days,
                                'start_time' => $start_date,
                                'end_time' => $end_date,
                                'time_zone' => $schedule->local_time_zone,
                                'is_active' => $schedule->is_active ?? 0,
                                'is_partitioned' => $schedule->is_partitioned ?? 0,
                                's3_file' => $schedule->s3_file ?? '-',
                            ];

                            $server_data[$server_id]['database_data'][$key]['schedule_data'][] = $table_data;
                        }
                    }
                    else
                    {
                        $server_data[$server_id]['server_id'] = $server_id;
                        $server_data[$server_id]['server_name'] = $single_schedule->server_name;
                        $server_data[$server_id]['database_data'][$key]['db_schedule_id'] = $single_schedule->id;
                        $server_data[$server_id]['database_data'][$key]['database_id'] = $single_schedule->client_database_id;
                        $server_data[$server_id]['database_data'][$key]['database_name'] = $single_schedule->database_name;
                        $server_data[$server_id]['database_data'][$key]['schedule_data'] = [];
                        foreach ($single_schedule->table_job_schedule as $schedule)
                        {
                            $selected_days = [];
                            $binary_number = strrev(decbin($schedule->archive_day_of_week));
                            $days_of_Week = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
                            for ($i = 0; $i < strlen($binary_number); $i++)
                            {
                                if ($binary_number[$i] === '1')
                                {
                                    $selected_days[] = $days_of_Week[$i];
                                }
                            }
                            $start_date = $schedule->archive_start_at_utc ?  Carbon::createFromFormat('H:i:s', $schedule->archive_start_at_utc , 'UTC')->setTimezone($schedule->local_time_zone)->format('h:i A') : null;
                            $end_date = $schedule->archive_untill_date_utc ?  Carbon::createFromFormat('H:i:s', $schedule->archive_untill_date_utc , 'UTC')->setTimezone($schedule->local_time_zone)->format('h:i A') : null;
                            $table_data = [
                                'schedule_id' => $schedule->id,
                                'table_name' => $schedule->table_name,
                                'total_rows' => $schedule->total_rows,
                                'batch_size' => $schedule->batch_size ?? 1000,
                                'retention' => $schedule->data_retention_days ?? 0,
                                'retention_index' => $schedule->retention_index ?? 0,
                                'day_of_week' => $selected_days,
                                'start_time' => $start_date,
                                'end_time' => $end_date,
                                'time_zone' => $schedule->local_time_zone,
                                'is_active' => $schedule->is_active ?? 0,
                                'is_partitioned' => $schedule->is_partitioned ?? 0,
                                's3_file' => $schedule->s3_file ?? '-',
                            ];

                            $server_data[$server_id]['database_data'][$key]['schedule_data'][] = $table_data;
                        }
                    }
                }
                foreach ($server_data as $serverDat)
                {
                    $data[] = $serverDat;
                }
                return $this->success('All schedule retrieved successfully', [
                    'schedule_listing' => ClientDbTablesResource::collection($data)
                ]);
            }
            else
            {
                return $this->success('No schedule Found!');
            }
        }
        catch (\Throwable $th)
        {
            return $this->error('Something went wrong', 400);
        }
    }

    /**
     * set schedule time for archiving  
     */
    public function setScheduleTime(ScheduleTimeRequest $request)
    {
        try
        {
            DB::beginTransaction();
            $carbonStartTime = Carbon::createFromFormat('H:i:s', $request->start_time, $request->time_zone);
            $carbonEndTime = Carbon::createFromFormat('H:i:s', $request->end_time, $request->time_zone);

            // Convert the time to UTC
            $start_time = $carbonStartTime->setTimezone('UTC')->format('H:i:s');
            $end_time = $carbonEndTime->setTimezone('UTC')->format('H:i:s');

            $data = [
                'data_retention_days' => $request->retention,
                'batch_size' => $request->batch_size,
                'object_backup_location' => $request->s3_directory,
                'local_time_zone' => $request->time_zone,
                'archive_start_at_utc' => $start_time,
                'archive_untill_date_utc' => $end_time,
                'archive_day_of_week' => $request->day_of_week,
                'schedule_type' => $request->schedule_type,
                'is_active' => 0,
            ];
            ScheduleProcessingList::where('id', $request->schedule_id)->update($data);
            DB::commit();
            return $this->success('Schedule added successfully');
        }
        catch (\Throwable $th)
        {
            DB::rollBack();
            return $this->error('Something went wrong', 400);
        }
    }

    /**
     * Delete single schedule from schedule listing 
     */
    public function deleteSingleSchedule(DeleteSingleScheduleRequest $request)
    {
        try
        {
            DB::beginTransaction();
            $schedul_id = $request->schedule_id;
            $schedule = ScheduleProcessingList::findOrFail($schedul_id);
            $schedule->delete();
            DB::commit();
            return $this->success('Schedule deleted successfully');
        }
        catch (\Throwable $th)
        {
            DB::rollBack();
            return $this->error('Something went wrong', 400);
        }
    }

    /**
     * Delete database's schedule from schedule listing
     */
    public function deleteDatabaseSchedule(DeleteDatabaseScheduleRequest $request)
    {
        try
        {
            DB::beginTransaction();
            $database_id = $request->database_id;
            $db_schedule = DBJobSchedule::where('client_database_id', $database_id)
                ->whereNull('deleted_at')
                ->select('id')
                ->get();
            if ($db_schedule->isNotEmpty())
            {
                $db_schedule_ids = $db_schedule->pluck('id');
                $schedules = ScheduleProcessingList::whereIn('database_job_schedule_id', $db_schedule_ids)->delete();
                DBJobSchedule::whereIn('id', $db_schedule_ids)->delete();
                DB::commit();
                return $this->success('Schedule deleted successfully');
            }
            else
            {
                return $this->success('Database schedule not found for this specific database id!');
            }
        }
        catch (\Throwable $th)
        {
            DB::rollBack();
            return $this->error('Something went wrong', 400);
        }
    }

    /**
     * get specific schedule's time with schedule id 
     */
    public function editScheduleTime(EditScheduleRequest $request)
    {
        try
        {
            $schedules = ScheduleProcessingList::where('id', $request->schedule_id)
                ->select('id', 'table_name', 'batch_size', 'archive_day_of_week', 'object_backup_location', 'data_retention_days', 'archive_untill_date_utc', 'archive_start_at_utc', 'schedule_type', 'local_time_zone', 's3_file')
                ->first();

            $selected_days = [];
            $binary_number = strrev(decbin($schedules->archive_day_of_week));
            $days_of_Week = [1, 2, 4, 8, 16, 32, 64];
            for ($i = 0; $i < strlen($binary_number); $i++)
            {
                if ($binary_number[$i] === '1')
                {
                    $selected_days[] = $days_of_Week[$i];
                }
            }
            $start_date = $schedules->archive_start_at_utc ?  Carbon::createFromFormat('H:i:s', $schedules->archive_start_at_utc , 'UTC')->setTimezone($schedules->local_time_zone)->format('H') : null;
            $end_date = $schedules->archive_untill_date_utc ?  Carbon::createFromFormat('H:i:s', $schedules->archive_untill_date_utc , 'UTC')->setTimezone($schedules->local_time_zone)->format('H') : null;
            $table_data = [
                'schedule_id' => $schedules->id,
                'batch_size' => $schedules->batch_size,
                'retention' => $schedules->data_retention_days,
                'day_of_week' => $selected_days,
                'start_time' => $start_date,
                'end_time' => $end_date,
                'schedule_type' => $schedules->schedule_type,
                'time_zone' => $schedules->local_time_zone,
                's3_directory_name' => $schedules->object_backup_location,
                's3_file' => $schedules->s3_file
            ];
            $data[] = $table_data;
            return $this->success('Schedule retrieved successfully', [
                'schedule_time' => ClientDbTablesResource::collection($data)
            ]);
        }
        catch (\Throwable $th)
        {
            DB::rollBack();
            return $this->error('Something went wrong', 400);
        }
    }

    /**
     * change specific schedule's archiving status  
     */
    public function changeArchivingState(UpdateScheduleRequest $request)
    {
        try
        {
            DB::beginTransaction();
            $data = [
                'is_active' => $request->archiving_status,
            ];
            ScheduleProcessingList::where('id', $request->schedule_id)->update($data);
            DB::commit();
            return $this->success('Archiving state changed successfully');
        }
        catch (\Throwable $th)
        {
            DB::rollBack();
            return $this->error('Something went wrong', 400);
        }
    }

    /**
     * Restore schedule with specific schedule id   
     */
    public function restoreSchedule(RestoreSchedule $request)
    {
        try
        {
            DB::beginTransaction();
            ScheduleProcessingList::onlyTrashed()->where('id', $request->schedule_id)->latest()->restore();
            DB::commit();
            return $this->success('Schedule with specific schedule id restored successfully');
        }
        catch (\Throwable $th)
        {
            DB::rollBack();
            return $this->error('Something went wrong', 400);
        }
    }

    /**
     * Restore database with specific database schedule id   
     */

    public function restoreDatabaseSchedule(RestoreDatabaseSchedule $request)
    {
        try
        {
            DB::beginTransaction();
            DBJobSchedule::onlyTrashed()
                ->where('id', $request->db_schedule_id)
                ->first()->restore();
            ScheduleProcessingList::onlyTrashed()->whereIn('id', $request->schedule_ids)->restore();
            DB::commit();
            return $this->success('Database schedule restored successfully');
        }
        catch (\Throwable $th)
        {
            DB::rollBack();
            return $this->error('Something went wrong', 400);
        }
    }
}
