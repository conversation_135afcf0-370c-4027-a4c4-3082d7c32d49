<?php

namespace App\Http\Controllers\API;

use Exception;
use App\Models\User;
use App\Models\Company;
use App\Traits\ApiResponser;
use App\Traits\FileUploader;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\UserResource;
use App\Http\Requests\StoreUserRequest;
use App\Http\Requests\UpdateUserRequest;
use App\Http\Requests\updateUserProfilePassword;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;

class UserController extends Controller
{
	use ApiResponser, FileUploader;

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        try {
			$users = User::all();

			return $this->success('All users retrieved successfully', [
				'users' => UserResource::collection($users)
			]);
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreUserRequest $request)
    {
        try {
			$user = User::create([
				'first_name' => $request->first_name,
				'last_name' => $request->last_name,
				'email' => $request->email,
				'company_id' => $request->company_id,
				'password' => $request->password,
				'server_option_id' => $request->server_option_id,
				'table_option_id' => $request->table_option_id,
				'is_active' => $request->is_active ?? 0
			]);

			return $this->success('User created successfully!',
				[
					'user' => new UserResource($user)
				]
			);
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
			$user = User::find($id);
			if(!$user) {
				return $this->error('No record found', 404);
			} else {
				return $this->success('User retrieved successfully!',
					[
						'user' => new UserResource($user)
					]
				);
			}
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateUserRequest $request, string $id)
    {
        try {
			$user = auth('sanctum')->user();

			if(!$user) {
				return $this->error('No record found', 404);
			} else {
				$user->first_name =  $request->first_name;
				$user->last_name =  $request->last_name;
				$user->email =  $request->email;

				if($request->has('image')) {
					$fileName = $this->uploadFile('users', $request->image);
					$user->image = $fileName;
				}


				if($request->has('timezone_id')) {
					$user->timezone_id = $request->timezone_id;
				}

				$user->save();

				$company = $user->company()->first();

				if($company) {
					$company->country_id = $request->country_id ?? null;
					$company->company_name = $request->company_name ?? null;
					$company->save();
				}
			}

			return $this->success('User updated successfully!',
				[
					'user' => new UserResource($user)
				]
			);
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
			$user = User::find($id);
			if(!$user) {
				return $this->error('No record found', 404);
			}
			else {
				$user->delete();
				return $this->success('User deleted successfully!', []);
			}
		}
		catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

	/**
	 * Update user profile password
	 */
	public function updateUserProfilePassword(updateUserProfilePassword $request)
	{
		try {
			if (!Hash::check($request->current_password, auth('sanctum')->user()->password)) {
				return $this->error('The old password does not match our records.', 400);
			}

			$user = auth('sanctum')->user();
			$user->password = $request->new_password;
			$user->save();

			return $this->success('Password was successfully updated', []);
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
	}

	/**
	 * Get logged in user details
	 */
	public function getAuthUser(Request $request) {
		try {
			$user = auth('sanctum')->user();

			return $this->success('User retrieved successfully!',
				[
					'user' => new UserResource($user)
				]
			);
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
	}

	/**
	 * Update user profile
	 */
	public function updateUserProfile(UpdateUserRequest $request) {
		try {
			$user = auth('sanctum')->user();

			if(!$user) {
				return $this->error('No record found', 404);
			} else {
				$user->first_name =  $request->first_name;
				$user->last_name =  $request->last_name;
				$user->email =  $request->email;

				if($request->has('image')) {
					$fileName = $this->uploadFile('users', $request->image);
					$user->image = $fileName;
				}

				if($request->has('isImageRemoved') && $request->isImageRemoved == "false") {
					if($user->image) {
						$filePath = "/users/".$user->image;
						if(Storage::disk('public')->exists($filePath)){
							Storage::disk('public')->delete($filePath);
							$user->image = NULL;
						}
					}
				}

				if($request->has('timezone_id')) {
					$user->timezone_id = $request->timezone_id;
				}

				$user->save();

				$company = $user->company()->first();

				if($company) {
					$company->country_id = $request->country_id ?? null;
					$company->company_name = $request->company_name ?? null;
					$company->save();
				}
			}

			return $this->success('Successfully updated profile!',
				[
					'user' => new UserResource($user)
				]
			);
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
	}
}
