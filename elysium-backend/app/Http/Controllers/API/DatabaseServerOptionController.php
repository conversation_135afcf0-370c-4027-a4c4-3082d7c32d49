<?php

namespace App\Http\Controllers\API;

use Exception;
use App\Models\DatabaseServerOption;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\DatabaseServerOptionRequest;
use App\Http\Resources\DatabaseServerOptionResource;

class DatabaseServerOptionController extends Controller
{
	use ApiResponser;

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        try {
			$serverOptions = DatabaseServerOption::all();

			return $this->success('All database server options retrieved successfully', [
				'serverOptionsp' => DatabaseServerOptionResource::collection($serverOptions)
			]);
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(DatabaseServerOptionRequest $request)
    {
        try {
			$serverOption = DatabaseServerOption::create([
				'name' => $request->name
			]);

			return $this->success('Database server option created successfully!',
				[
					'serverOption' => new DatabaseServerOptionResource($serverOption)
				]
			);
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
			$serverOption = DatabaseServerOption::find($id);
			if(!$serverOption) {
				return $this->error('No record found', 404);
			} else {
				return $this->success('Database server option retrieved successfully!',
					[
						'serverOption' => new DatabaseServerOptionResource($serverOption)
					]
				);
			}
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(DatabaseServerOptionRequest $request, string $id)
    {
        try {
			$serverOption = DatabaseServerOption::find($id);

			if(!$serverOption) {
				return $this->error('No record found', 404);
			} else {
				$serverOption->name =  $request->name;
				$serverOption->save();
			}

			return $this->success('Database server option updated successfully!',
				[
					'serverOption' => new DatabaseServerOptionResource($serverOption)
				]
			);
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
			$serverOption = DatabaseServerOption::find($id);
			if(!$serverOption) {
				return $this->error('No record found', 404);
			}
			else {
				$serverOption->delete();
				return $this->success('Database server option deleted successfully!', []);
			}
		}
		catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }
}
