<?php

namespace App\Http\Controllers\API;

use Exception;
use Throwable;
use Carbon\Carbon;
use App\Models\DimDate;
use phpseclib3\Net\SSH2;
use Illuminate\Support\Str;
use App\Models\RemoteServer;
use App\Traits\ApiResponser;
use App\Models\ProcessSchedule;
use App\Models\ClientDbServerStat;
use Illuminate\Support\Facades\DB;
use App\Models\ClientObjectStorage;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use phpseclib3\Crypt\PublicKeyLoader;
use App\Models\ScheduleProcessingList;
use App\Models\ClientDbServerTableStat;
use App\Http\Requests\PopulateStatRequest;
use App\Mail\agentFailureEmail;
use App\Models\ObjectStorageExportHistory;
use App\Traits\VerifyDatabaseServerConnection;
use App\Models\DBServer;
use App\Models\ClientDBServerTable;
use App\Models\table_job_schedule_agent_logs;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use Illuminate\Http\Request;

class ProcessScheduleController extends Controller
{
    use ApiResponser;
    const KEY_PATH = 'ssh_keys/id_rsa';
    const SCRIPT_PATH = '~/agent/db_archive.sh';
    const SET_TIME = 300;

    public function getSchedule()
    {
        $current_day_of_week = Carbon::now()->dayOfWeek - 1;

        // Calculate the bit value for today's day of the week

        $today_value = 2 ** $current_day_of_week;
        $current_day_of_week = (Carbon::now()->dayOfWeek + 6) % 7; // Adjust for Monday being 0
        $current_day_name = Carbon::now()->startOfWeek()->addDays($current_day_of_week)->format('l');
        $current_time = Carbon::now()->utc()->format('H:i:s');

        // log::info("Today's value: $today_value");
        // log::info("Current day of week: $current_day_of_week");
        // log::info("Current day name: $current_day_name");
        // log::info("Current time: $current_time");

        // Query schedules for today with the current time filter

        $schedules = ScheduleProcessingList::where(DB::raw("(archive_day_of_week & $today_value)"), ">", 0)
            ->where('archive_start_at_utc', '<=', $current_time)
            ->where('archive_untill_date_utc', '>=', $current_time)
            ->where('is_active', '=', 0)
            ->get();

        # print sql query for previous orm
        log::info(ScheduleProcessingList::where(DB::raw("(archive_day_of_week & $today_value)"), ">", 0)
            ->where('archive_start_at_utc', '<=', $current_time)
            ->where('archive_untill_date_utc', '>=', $current_time)
            ->where('is_active', '=', 0)->toSql());
        log::info(" Schedules -=>". $schedules);
        
        if ($schedules->isNotEmpty())
        {
            foreach ($schedules as $single_schedule)
            {
                //check shedule in added for today shedule process

                $process = ProcessSchedule::where('table_job_schedule_id', $single_schedule->id)
                    ->whereDate('created_at', '=', Carbon::now()->toDateString())->get();
                if ($process->isEmpty())
                {
                    //adding schedule for today shedule process
                    log::info("Schedule not found for today: $process");
                    $process_data = new ProcessSchedule;
                    $data = [
                        'table_job_schedule_id' =>  $single_schedule->id,
                        'day_of_week' => $current_day_name,
                        'status' => ProcessSchedule::AWAITING,
                    ];
                    $process_data->create($data);
                }
            }


            $updated_dbs=[];
            //get schedule for today shedule process 

            $process_schedules = ProcessSchedule::with('table_job_schedule.db_job_schedule')->where('status', 'awaiting')->whereDate('created_at', '=', Carbon::now()->toDateString())->get();
            if ($process_schedules->isNotEmpty())
            {
                foreach ($process_schedules as $process_schedule)
                {
                    Log::info("Schedule found: $process_schedule");
                    $current_schedule = ProcessSchedule::findOrFail($process_schedule->id);
                    $current_table_job_schedule = $process_schedule->table_job_schedule->where('id', $process_schedule->table_job_schedule->id)->first();
                    $table_job_schedule_status = [
                        'is_active' => 1
                    ];
                    $current_table_job_schedule->update($table_job_schedule_status);
                    //remote server info 
                    $response_schedule = [
                        'status' =>ProcessSchedule::IN_PROGRESS,
                    ];
                    // $current_schedule->update($response_schedule);
                    Log::info("Schedule updated: $current_schedule");
                    $remote_server = RemoteServer::whereHas('client_servers', function ($query) use ($process_schedule)
                    {
                        $query->whereHas('client_server_table', function ($query) use ($process_schedule)
                        {
                            $query->where('id', $process_schedule->table_job_schedule->client_db_server_table_id);
                        });
                    })->first();
                    Log::info("Remote Server found: $remote_server");
                    //remote server credentials 

                   


                    # get client_db_server.id through client_db_server_table which is in  table_job_schedule for the process_schedule value
                    $client_db_server_id = ClientDBServerTable::where('id', $process_schedule->table_job_schedule->client_db_server_table_id)->value('client_db_server_id');
                    Log::info("db id : ".$client_db_server_id);

                    
                    #if #client_db_server_id is not in updated_dbs then update db scheme info
                    if(!in_array($client_db_server_id, $updated_dbs)){
                        Log::info("DB Server not updated: $client_db_server_id");
                        $updated_dbs[]=$client_db_server_id;

                  
                        $dbServer = DBServer::where('id', $client_db_server_id)->first();                
                        Log::info("DB Server found: $dbServer");
                        try{
                        
                            $remoteServer =$remote_server;
                        
                            // Start logging connection details
                            $connectionLogs = [];
                            $connectionLogs[] = "Starting connection test to database server {$dbServer->hostname}...";
                            $connectionLogs[] = "Using remote server: {$remoteServer->hostname}";
                            $connectionLogs[] = "Using database port: {$dbServer->port}";
                            $connectionLogs[] = "Using username: {$dbServer->username}";
                            
                            // Test the connection
                            $databaseServerConnection = VerifyDatabaseServerConnection::updateDBSchemeInfo(
                                $remoteServer, 
                                $dbServer->hostname, 
                                $dbServer->username, 
                                $dbServer->password, 
                                $dbServer->port, 
                                $dbServer->client_db_server_uuid,
                                $connectionLogs
                            );
                        }catch(Exception $th){
                            log::info("Error: could not update DB Scheme Info ".json_encode(['error' => $th->getMessage()]));
                        }
                    }

                    try
                    {
                        $timeoutInSeconds = self::SET_TIME;
                        $ssh_host_Name = $remote_server->hostname;
                        $ssh_port = $remote_server->port;
                        $ssh = new SSH2($ssh_host_Name, $ssh_port, $timeoutInSeconds);
                        // Authenticate with the private key
                        $path = public_path('ssh_keys/id_rsa');

                        //load the file content

                        $key = PublicKeyLoader::load(file_get_contents($path));
                        // check sever login status
                        if(!$ssh->login($remote_server->username, $key))
                        {
                            log::info("Error:".ProcessSchedule::CONNECTION_FAIL." ".json_encode(['error' => 'Failed to connect to remote server using dynamic SSH tunnel.']));
                            $response_schedule = [
                                'status' =>  ProcessSchedule::CONNECTION_FAIL,
                                'execution_datetime' => Carbon::now(),
                                'response_json' => json_encode(['error' => 'Failed to connect to remote server using dynamic SSH tunnel.'])
                            ];
                            $current_schedule->update($response_schedule);
                            $table_job_schedule_status = [
                                'is_active' => 0
                            ];
                            $current_table_job_schedule->update($table_job_schedule_status);
                        }

                        // $res=$ssh->exec("ls");
                        // log::info("Response: $res");

                        

                        $encypt_key = Hash::make(Str::random(5) . Carbon::now());
                        log::info("Encypt Key: $encypt_key");
                        $params = [
                            'id' => $process_schedule->id,
                            'enc_key' =>  $encypt_key,
                            'server_name' => $process_schedule->table_job_schedule->db_job_schedule->server_name,
                            'table_name' => $process_schedule->table_job_schedule->table_name,
                            'database_name' =>  $process_schedule->table_job_schedule->database_name,
                            'schema_name' => $process_schedule->table_job_schedule->schema_name,
                            's3_bucket_name' => $process_schedule->table_job_schedule->s3_file,
                            's3_directory_name' => $process_schedule->table_job_schedule->object_backup_location,
                            'archive_type' => $process_schedule->table_job_schedule->schedule_type,
                            'batch_size' => $process_schedule->table_job_schedule->batch_size,
                        ];

                        $primary_table_column = DB::table('client_db_server_table_column')
                            ->where('client_db_server_table_id', $process_schedule->table_job_schedule->client_db_server_table_id)
                            ->where('column_key', 'PRI')
                            ->value('column_name');

                        // Fallback if primary key not found
                        if (!$primary_table_column) {
                            Log::warning("No primary key found for table {$process_schedule->table_job_schedule->table_name}");
                            $primary_table_column = 'id'; // Default fallback
                        }
                        log::info("Primary Key: $primary_table_column");
                        
                        //get Date key column
                        $table_date_column = DB::table('client_db_server_table_column')
                            ->where('client_db_server_table_id', $process_schedule->table_job_schedule->client_db_server_table_id)
                            ->where('column_key', 'MUL')
                            ->value('column_name');

                        // Fallback if primary key not found
                        if (!$table_date_column) {
                            Log::warning("No date key found for table {$process_schedule->table_job_schedule->table_name}");
                            $table_date_column = 'last_update'; // Default fallback
                        }
                        log::info($process_schedule->table_job_schedule->table_name." DAte Key: $table_date_column");
                        

                        $db_server = DB::table('client_db_server_table')
                            ->join('client_db_schema', 'client_db_server_table.client_db_schema_id', '=', 'client_db_schema.id')
                            ->join('client_database', 'client_db_schema.client_database_id', '=', 'client_database.id')
                            ->join('client_db_server', 'client_database.client_db_server_id', '=', 'client_db_server.id')
                            ->where('client_db_server_table.id', $process_schedule->table_job_schedule->client_db_server_table_id)
                            ->select('client_db_server.hostname', 'client_db_server.login_path_name')
                            ->first();

                        // Set the variables with fallbacks in case the query returns no results
                        $db_server_hostname = $db_server->hostname ?? 'localhost';
                        $db_server_login_path = $db_server->login_path_name ?? null;

                        log::info("DB Server Hostname: $db_server_hostname");
                        log::info("DB Server Login Path: $db_server_login_path");

                        // var_dump($primary_table_column);die;
                        // $db_server_hostname = $process_schedule->table_job_schedule->database_host;
                        // $db_server_login_path= $process_schedule->table_job_schedule->login_path;
                     
                        #from table object_storage_export_history get last_exported_id value for the last entry for the same table on the same db as in table_job_schedule
                        $last_exported_id = ObjectStorageExportHistory::where('client_db_server_table_id', $process_schedule->table_job_schedule->client_db_server_table_id)
                            ->where('table_name', $process_schedule->table_job_schedule->table_name)                            
                            ->max('last_exported_id');
                        log::info("Last Exported ID: $last_exported_id");

                        $params_agent = [                            
                            'h' =>  $db_server_hostname,
                            'l' =>  $db_server_login_path,
                            'd' =>  $process_schedule->table_job_schedule->database_name,
                            't' => $process_schedule->table_job_schedule->table_name,
                            'k' => $primary_table_column,                            
                            // 'schema_name' => $process_schedule->table_job_schedule->schema_name,
                            's' => $process_schedule->table_job_schedule->s3_file,
                            'v' => $process_schedule->table_job_schedule->object_backup_location,
                            // 'sp' => $process_schedule->table_job_schedule->object_backup_location,
                            // 'at' => $process_schedule->table_job_schedule->schedule_type,
                            'b' => $process_schedule->table_job_schedule->batch_size,
                            'i' => $process_schedule->table_job_schedule->is_partitioned,
                            'r' => $process_schedule->table_job_schedule->data_retention_days,
                            'e' => $process_schedule->table_job_schedule->schedule_type,
                            'c' => $table_date_column,
                            'I' => $process_schedule->id,
                            'K' =>  "'$encypt_key'",
                            'm' => $last_exported_id
                        ];

                        // log::info("Params: ".json_encode($params_agent));
                     
                        
                        Log::info("Remote Server Connection established for hostname $ssh_host_Name");
                        // Assuming $params is an array of parameters
                        
                        $params_string = json_encode($params);
                        $params_string_agent = $this->jsonToShellArgs(json_encode($params_agent));
                        try
                        {
                            DB::beginTransaction();
                            $input_response = [
                                'execution_datetime' => Carbon::now(),
                                'schedule_enc_key' => $encypt_key,
                                'request_json' => json_encode($params_agent)
                            ];
                            $current_schedule->update($input_response);
                            DB::commit();
                            log::info("Request updated with encypt key: ".$encypt_key);
                        }
                        catch (Throwable $th)
                        {
                            log::info("Error: ".json_encode(['error' => $th->getMessage()]));
                            DB::rollBack();
                            return $this->error($th, 404);
                        }
                        $script_path = self::SCRIPT_PATH;
                        $command = "$script_path $params_string_agent";
                        log::info("Command: $command");
                        $script_intail_outputs = $ssh->exec($command);
                        log::info("Script Output: $script_intail_outputs");
                        if (!$script_intail_outputs)
                        {
                            $response_schedule = [
                                'status' => ProcessSchedule::TIMEOUT,
                                'execution_datetime' => Carbon::now(),
                                'response_json' => json_encode('Maximum connection time exceed')
                            ];
                            $current_schedule->update($response_schedule);
                            $table_job_schedule_status = [
                                'is_active' => 0
                            ];
                            $current_table_job_schedule->update($table_job_schedule_status);
                        }
                        else
                        {
                            Log::info("Output of 'file exec' command: $script_intail_outputs ");
                            $json_output = json_decode($script_intail_outputs, true);
                            try
                            {
                                DB::beginTransaction();
                                $output_response = [
                                    'status' => ProcessSchedule::SUCCESS_EXEC,
                                    'response_json' => json_encode(["res"=>$script_intail_outputs])
                                ];
                                $current_schedule->update($output_response);
                                $table_job_schedule_status = [
                                    'is_active' => 0
                                ];
                                $current_table_job_schedule->update($table_job_schedule_status);
                                DB::commit();
                            }
                            catch (Throwable $th)
                            {
                                DB::rollBack();
                                return $this->error($th, 404);
                            }
                            Log::info("info updated");
                        }
                        $ssh->disconnect();
                        // }
                    }
                    catch (Exception $th)
                    {
                        log::info("Error:".ProcessSchedule::CONNECTION_FAIL." ".json_encode(['error' => $th->getMessage()]));
                        $response_schedule = [
                            'status' =>  ProcessSchedule::CONNECTION_FAIL,
                            'execution_datetime' => Carbon::now(),
                            'response_json' => json_encode(['error' => $th->getMessage()])
                        ];
                        $current_schedule->update($response_schedule);
                        $table_job_schedule_status = [
                            'is_active' => 0
                        ];
                        $current_table_job_schedule->update($table_job_schedule_status);
                    }
                }
            }
        }
        else
        {
            Log::info("No Schedule found: $schedules");
        }
    }
    //save stats using archiving response 
    public function populate_stats(PopulateStatRequest $request)
    {

        $storageRates = [
            'db_storage' => 0.115, // $/GB/month General Purpose SSD (gp3)
            's3_standard' => 0.023, // $/GB/month
            's3_glacier' => 0.004, // $/GB/month
        ];
        
        $compressionRatio = 0.3; // 70% compression typical

        log::info("Request: ".json_encode($request->all()));
        try
        {
           DB::beginTransaction();
        
            $data = ProcessSchedule::with('table_job_schedule.tables_data.clientDatabase.clientDBServer')
                ->where('id', $request->id)
                ->first();
                
            if (!$data) {
                return $this->error('Process schedule not found', 404);
            }

            $execution_datetime = $data->execution_datetime ?? null;
            $schedule_execution_id = $data->id;

            // ... rest of your code using $data directly instead of $data[0]
            
            $table_job_schedule = collect([$data->table_job_schedule]);
            $tables_data = $table_job_schedule->pluck('tables_data')->flatten();
            $client_database = $tables_data->pluck('clientDatabase')->flatten();
            $archive_server = $client_database->pluck('clientDBServer')->flatten();
            
            if ($archive_server->isEmpty()) {
                return $this->error('Server data not found', 404);
            }

            $company = $archive_server->first()->company_id;
            $execution_date = Carbon::parse($execution_datetime)->format('Y-m-d');
            $dim_date_id = DimDate::where('date', $execution_date)->pluck('id');
        // var_dump($execution_date);die;
            $storage = ClientObjectStorage::whereHas('objectStorageType', function ($query) use ($company)
            {
                $query->where('company_id', $company);
            })->pluck('id');

            $schedule = ProcessSchedule::findOrFail($schedule_execution_id);
            if ($request->export_status == 'Completed')
            {
                log::info("Export Status: ".$request->export_status);
                $schedule_data = [
                    'schedule_enc_key' => null,
                ];
                $schedule->update($schedule_data);

                $total_archived_size_in_gb=(($request->total_table_data_size_archived/1024) );
                $tableMetrics = [
                    'size_gb' => $total_archived_size_in_gb,
                ];
                $dbStorageCost = ($tableMetrics['size_gb'] * $storageRates['db_storage']);
                $s3StorageCost = ($tableMetrics['size_gb'] * $compressionRatio * $storageRates['s3_standard']);
                
                $costSaving=$dbStorageCost - $s3StorageCost;

                //insert table stats
                $record = ClientDbServerTableStat::updateOrCreate(
                    ['client_db_server_table_id' => $tables_data[0]['id'], 'stat_date' => $execution_date],
                    [
                        'client_db_server_id' => $archive_server[0]['id'],
                        'dim_date_id' => $dim_date_id[0],
                        'company_id' => $company,
                        'client_db_server_table_id' => $tables_data[0]['id'],
                        'stat_date' => $execution_date,
                        'total_table_rows' => $tables_data[0]['total_current_table_rows'],
                        'total_table_rows_archived' => $request->total_table_rows_archived,
                        'total_db_storage_setup' => $client_database[0]['total_current_db_data_size'],
                        'total_table_size' => $tables_data[0]['total_current_data_length'],
                        'total_table_data_size_archived' => $request->total_table_data_size_archived,
                        'total_table_storage_cost_saving' => $costSaving,
                        'total_db_storage_setup_cost' => $dbStorageCost,
                        'is_deleted' => 0,
                        'client_database_id' => $client_database[0]['id']
                    ],
                );
                if ($record->wasRecentlyCreated)
                {
                    //insert servers stats
                    ClientDbServerStat::Create(
                        [
                            'dim_date_id' => $dim_date_id[0],
                            'client_db_server_id' => $archive_server[0]['id'],
                            'company_id' => $company,
                            'db_name' => $client_database[0]['db_name'],
                            'stat_date' => $execution_date,
                            'total_db_size' => $client_database[0]['total_current_db_size'],
                            'total_db_storage_setup' => $client_database[0]['total_current_db_data_size'],
                            'total_db_index' => $client_database[0]['total_current_db_index_size'],
                            'total_db_data_size_archived' => $request->total_db_data_size_archived,
                            'total_table_data_size_archived' => $request->total_table_data_size_archived,
                            'total_table_storage_cost_saving' => $costSaving,
                            'is_deleted' => 0,
                        ]
                    );
                }

                ObjectStorageExportHistory::updateOrCreate(
                    ['client_db_server_table_id' => $tables_data[0]['id'], 'created_at' => $execution_date],
                    [
                        'file_export_size' => $request->file_export_size,
                        'file_stored_size' => $request->file_stored_size,
                        'file_compression_ratio' => $request->file_export_size / $request->file_stored_size,
                        'file_compression_percentage' => (1 - ($request->file_stored_size / $request->file_export_size)) * 100,
                        'file_export_ended_at' => $request->file_export_ended_at ?? date('Y-m-d H:i:s'),
                        'export_status' => $request->export_status,
                        'last_exported_id' => $request->last_exported_id,
                        'is_export_error' => $request->is_export_error,
                        'error_message' => $request->error_message,
                        'is_current' => $request->is_current,
                        'created_at' => $execution_date
                    ],
                );
            }
            if ($request->export_status === 'Started')
            {
                log::info("Export Status: ".$request->export_status);
                ObjectStorageExportHistory::updateOrCreate(
                    ['client_db_server_table_id' => $tables_data[0]['id'], 'created_at' => $execution_date],
                    [
                        'client_db_server_table_id' => $tables_data[0]['id'],
                        'object_storage_type_id' => $storage[0],
                        'table_name' => $tables_data[0]['table_name'],
                        'file_exported_name' => $request->file_exported_name,
                        'object_storage_dir_name' => $request->object_storage_dir_name,
                        'file_export_size' => $request->file_export_size,
                        'file_export_started_at' => $request->file_export_started_at ?? date('Y-m-d H:i:s'),
                        'export_status' => $request->export_status,
                        'created_at' => $execution_date
                    ],
                );
            }
            if ($request->export_status === 'Stopped')
            {
                $schedule_data = [
                    'schedule_enc_key' => null,
                ];
                $schedule->update($schedule_data);
                $record = ClientDbServerTableStat::updateOrCreate(
                    ['client_db_server_table_id' => $tables_data[0]['id'], 'stat_date' => $execution_date],
                    [
                        'client_database_id' => $client_database[0]['id'],
                        'dim_date_id' => $dim_date_id[0],
                        'client_db_server_id' => $archive_server[0]['id'],
                        'company_id' => $company,
                        'db_name' => $client_database[0]['db_name'],
                        'stat_date' => $execution_date,
                        'total_db_size' => $client_database[0]['total_current_db_size'],
                        'total_db_storage_setup' => $client_database[0]['total_current_db_data_size'],
                        'total_db_index' => $client_database[0]['total_current_db_index_size'],
                        'total_table_rows_archived' => $request->total_table_rows_archived ?? 0,
                        // 'total_table_storage_cost_saving' => $request->total_table_storage_cost_saving ?? 0,
                        // 'total_db_storage_setup_cost' => $request->total_db_storage_setup_cost ?? 0
                    ],

                );
                if ($record->wasRecentlyCreated)
                {
                    //insert servers stats
                    ClientDbServerStat::Create(
                        [
                            'dim_date_id' => $dim_date_id[0],
                            'client_db_server_id' => $archive_server[0]['id'],
                            'company_id' => $company,
                            'db_name' => $client_database[0]['db_name'],
                            'stat_date' => $execution_date,
                            'total_db_size' => $client_database[0]['total_current_db_size'],
                            'total_db_storage_setup' => $client_database[0]['total_current_db_data_size'],
                            'total_db_index' => $client_database[0]['total_current_db_index_size'],
                            'total_db_data_size_archived' => $request->total_db_data_size_archived ?? 0,
                            // 'total_table_storage_cost_saving' => $request->total_table_storage_cost_saving ?? 0,
                            // 'total_db_storage_setup_cost' => $request->total_db_storage_setup_cost ?? 0,
                            'is_deleted' => 0,
                        ]
                    );
                }
                ObjectStorageExportHistory::updateOrCreate(
                    ['client_db_server_table_id' => $tables_data[0]['id'], 'created_at' => $execution_date],
                    [
                        'export_status' => $request->export_status,
                        'is_export_error' => $request->is_export_error,
                        'error_message' => $request->error_message,
                        'file_export_ended_at' => $request->file_export_ended_at ?? date('Y-m-d H:i:s'),
                    ],
                );
            }

            DB::commit();

            //insert export history 
            return $this->success('Dashboard archiving stats created successfully', []);
        }
        catch (\Throwable $th)
        {
            Log::info('Process Error =>'. $th);

            // DB::rollBack();
            return $this->error($th . 'Something went wrong', 400);
        }
    }

    private function jsonToShellArgs(string $jsonString): string
    {
        $data = json_decode($jsonString, true); // Decode JSON into an associative array

        if (json_last_error() !== JSON_ERROR_NONE) {
            // Handle JSON decoding errors
            error_log("JSON decoding error: " . json_last_error_msg());
            return '';
        }

        $args = [];
        foreach ($data as $key => $value) {
            // We'll assume keys are safe for direct use as short flags (e.g., single character or simple alphanumeric)
            // If keys could contain spaces or special characters and still need to be short flags,
            // you'd need a more complex sanitization or a different flag naming convention.

            if (is_bool($value)) {
                if ($value) {
                    // For boolean true, just add the flag (e.g., -verbose, though -v is more common for short flags)
                    $args[] = "-" . $key;
                }
                // For boolean false, we omit it
            } elseif (is_array($value)) {
                // For array values, join them with spaces.
                // **WARNING**: This is generally not safe without escaping if array elements can contain spaces.
                // For strict "no quotes" requirement, this is a common compromise but can be problematic.
                $args[] = "-" . $key . " " . implode(" ", $value);
            } elseif ($value !== null && $value !== '') {
                // For other non-empty values, add -key value
                // **WARNING**: Direct inclusion of $value without escapeshellarg() is UNSAFE
                // if $value can contain spaces or shell metacharacters.
                // Only use this if you are absolutely certain of the input's safety (e.g., internal-only, controlled data).
                $args[] = "-" . $key . " " . $value;
            }
        }

        return implode(" ", $args);
    }

    //API for agent failure to receive from webhool using post method and save to table_job_schedule_agent_logs
    public function agentFailure(Request $request)
    {
        try
        {
            $data = $request->all();
            Log::info('Agent Failure =>'. json_encode($data));
            $log_message = $data['log_message'];
            $log_type = $data['log_type'];
           
            $table_job_schedule_execution_log_id = $data['table_job_schedule_execution_log_id'];
            $table_job_schedule_id = ProcessSchedule::where('id', $table_job_schedule_execution_log_id)->value('table_job_schedule_id');
            

            $log = table_job_schedule_agent_logs::create([
                'table_job_schedule_id' => $table_job_schedule_id,
                'table_job_schedule_execution_log_id' => $table_job_schedule_execution_log_id,
                'log_message' => $log_message,
                'log_type' => $log_type
            ]);

            //parse log_message JSON    to get table_id and log_type, and log message

            //send a notification email to user with log message
            // $subject = 'Agent Failure';
            // $message = 'Agent failed to execute schedule';
            // $email = '<EMAIL>';
            // $data = [
            //     'subject' => $subject,
            //     'message' => $message,
            //     'email' => $email
            // ];
            
            // Get user email using table_job_schedule_id through the relationship chain:
            // table_job_schedule -> client_db_server_table -> client_db_server -> company_id -> users

            // First get the table_job_schedule record
            $table_job_schedule = ScheduleProcessingList::find($table_job_schedule_id);
            
            if ($table_job_schedule) {
                // Get the company_id through the relationship chain
                $company_id = $table_job_schedule->tables_data->clientDBServer->company_id;

                // Get all users for this company
                $users = User::where('company_id', $company_id)->get();
                Log::info('Users =>'. json_encode($users));
                // Send email to all users in the company
                foreach ($users as $user) {
                    $mail_details = [
                        'full_name' => $user->first_name . ' ' . $user->last_name,
                        'subject' => 'Agent Failure Notification',
                        'log_message' => $log_message,
                        'log_type' => $log_type,
                        'table_name' => $table_job_schedule->table_name,
                        'database_name' => $table_job_schedule->database_name
                    ];

                    Mail::to($user->email)->send(new agentFailureEmail($mail_details));
                }
            }

            // dispatch(new sendAgentFailureEmailJob($data));



            return $this->success('Log created successfully', $log);
        }
        catch (\Throwable $th)
        {
            Log::error('Agent failure processing error: ' . $th->getMessage());
            return $this->error('Something went wrong', 400);
        }
    }

}
