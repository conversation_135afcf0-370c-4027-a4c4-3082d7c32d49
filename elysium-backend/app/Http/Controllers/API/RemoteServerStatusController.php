<?php

namespace App\Http\Controllers\API;

use Illuminate\Http\Request;
use App\Models\RemoteServerStatus;
use App\Http\Controllers\Controller;

class RemoteServerStatusController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(RemoteServerStatus $remoteServerStatus)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(RemoteServerStatus $remoteServerStatus)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, RemoteServerStatus $remoteServerStatus)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(RemoteServerStatus $remoteServerStatus)
    {
        //
    }
}
