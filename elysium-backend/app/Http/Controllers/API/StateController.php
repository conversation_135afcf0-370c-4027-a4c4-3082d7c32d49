<?php

namespace App\Http\Controllers\API;

use App\Models\CountryState;
use Illuminate\Http\Request;
use App\Traits\ApiResponser;
use App\Http\Controllers\Controller;
use App\Http\Resources\StateResource;

class StateController extends Controller
{
    use ApiResponser;

	/**
	 * Get all states list
	 */
	public function getAll(Request $request)
	{
		try {
			$states = CountryState::all();

			if($states) {
				return $this->success('All states retrieved successfully', [
					'states' => StateResource::collection($states)
				]);
			} else {
				return $this->success('No state found', []);
			}

		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
	}
}
