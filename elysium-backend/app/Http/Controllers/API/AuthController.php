<?php

namespace App\Http\Controllers\API;

use Exception;
use App\Models\User;
use App\Models\Company;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use App\Http\Requests\LoginRequest;
use App\Http\Controllers\Controller;
use App\Http\Requests\RegisterRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Mail\accountVerificationEmail;
use Illuminate\Support\Facades\Mail;

class AuthController extends Controller
{
    use ApiResponser;

	/**
	 * Signup API
	 * @param Request $request
	 * @return Json
	 */
	public function register(RegisterRequest $request)
    {
		DB::beginTransaction();
		try {
			$company = Company::create([
				'company_name' => $request->company_name,
				'country_id' => $request->country_id,
				'state_id' => $request->state_id,
				'company_address' => $request->address,
				'company_address2' => $request->secondary_address ?? null,
				'company_city' => $request->city,
				'company_postalcode' => $request->zip,
				'database_server_option_id' => $request->db_server_option,
				'database_table_option_id' => $request->db_table_option,
				'is_active' => 1
			]);

			$user = User::create([
				'first_name' => $request->first_name,
				'last_name' => $request->last_name,
				'email' => $request->email,
				'company_id' => $company->id,
				'password' => $request->password,
				'is_active' => 1
			]);

			$otp = rand(100000,999999);
			$user = User::where('email','=',$request->email)->first();
			$user->otp = $otp;
			$user->save();

			if($user){
				$mail_details = [
					'full_name' => $user->first_name. ' '. $user->last_name,
					'subject' => 'Verify Email Address',
					'otp' => $otp
				];

				\Mail::to($request->email)->send(new accountVerificationEmail($mail_details));
			}

			DB::commit();

			return $this->success('User created successfully!', []);
		} catch (\Throwable $th) {
			DB::rollback();
			return $this->error('Something went wrong', 400);
		}
    }

	/**
	 * Sign-in API
	 *
	 * @return Json
	 */
	public function login(LoginRequest $request)
    {
		try {

			if(!Auth::attempt($request->only(['email', 'password']))){
                return $this->error('Email or password is incorrect', 200);
            }

            $user = User::where('email', $request->email)->first();

			return $this->success('User logged-in successfully', [
				'token' => $user->createToken('API Token')->plainTextToken
			]);
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

	/**
	 * Logout API
	 *
	 * @return json
	 */
	public function logout(Request $request)
    {
		try {
			if($request->user()) {
				// Revoke the token that was used to authenticate the current request...
				$request->user()->currentAccessToken()->delete();
			} else {
				return $this->error('Invalid token', 200);
			}

			return $this->success('User logged-out successfully', []);
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }
}
