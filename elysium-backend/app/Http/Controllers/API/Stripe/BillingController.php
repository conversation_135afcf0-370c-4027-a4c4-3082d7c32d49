<?php

namespace App\Http\Controllers\API\Stripe;

use App\Http\Controllers\Controller;
use App\Models\SubscriptionPlan;
use Illuminate\Http\Request;
use App\Traits\ApiResponser;
use App\Models\User;
use Carbon\Carbon;
use Helper;

class BillingController extends Controller
{
	use ApiResponser;

    /**
	 * Fetch customer invoices
	 */
	public function getInvoices(Request $request)
	{
		try {
			$invoicesList = [];
			$user = auth('sanctum')->user();
			$invoices = $user->invoices();

			if($invoices) {
				foreach($invoices as $invoice) {

					$planId = $invoice->lines->data[0]->plan->id ?? NULL;

					if($planId) {
						$planName = SubscriptionPlan::where('stripe_plan', $planId)->pluck('plan_name');
						$planName = $planName ? $planName[0] : NULL;

						$invoicesList[] = [
							'date' => Carbon::createFromTimestamp($invoice->created)->format('d/m/Y'),
							'plan' => $planName,
							'amount' => Helper::convertPaymentAmount($invoice->amount_paid),
							'status' => $invoice->paid ? 'Paid' : 'Pending',
							'url' => 'downloadInvoice/'. $user->id.'/'.$invoice->id,
						];
					}
				}

				return $this->success('All invoices retrieved successfully', [
					'invoices' => $invoicesList
				]);
			} else {
				return $this->success('No invoice found', [
					'invoices' => $invoicesList
				]);
			}
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
	}

	/**
	 * Download Invoice in PDF Format
	 */
	public function downloadInvoice(Request $request, int $userId) {
		try {
			$user = User::find($userId);
			$invoiceId = $request->invoiceId;

			if(!$user || !$invoiceId) {
				return $this->error('Unable to download pdf', 400);
			}

			return $user->downloadInvoice($invoiceId);

		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
	}


	/**
	 * Get customer payment methods (cards) details
	 */
	public function getPaymentMethods(Request $request) {
		try {
			$cards = [];
			$user = auth('sanctum')->user();

			if (!$user->defaultPaymentMethod()) {
				return $this->success('No payment method found', [
					'cards' => null
				]);
			}

			$paymentMethod = $user->defaultPaymentMethod('card');

			if($paymentMethod) {
				$cards = [
					'type' => $paymentMethod->card->brand,
					'exp_month' => $paymentMethod->card->exp_month,
					'exp_year' => $paymentMethod->card->exp_year,
					'last4' => $paymentMethod->card->last4,
					'icon' => 'FaCc'.$paymentMethod->card->brand
				];
			}

			return $this->success('All payment methods retrieved successfully', [
				'cards' => $cards
			]);

		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
	}

	/**
	 * Update payment method
	 */
	public function updatePaymentMethod(Request $request) {
		try {

			$user = auth('sanctum')->user();

			if ($user->hasPaymentMethod()) {
				// Update the payment method using Laravel Cashier
				$user->updateDefaultPaymentMethod($request->token);
				return $this->success('Payment methods updated successfully', []);
			}

			return $this->success('No payment method found', []);

		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
	}

	/**
	 * Get active subscription
	 */
	public function getActiveSubscription(Request $request) {
		try {
			$user = $request->user();
			$subscription = $user->subscriptions()->active()->latest()->first();

			/**
			 * Check if user is in trial period or do not have any active subsctiption
			 */
			if(($user && $user->trial_ends_at) && !$subscription) {
				$trialEndsAtDate = $user->trial_ends_at;
				$trialEndsAtDate = Carbon::parse($trialEndsAtDate);
				$trialDaysLeft = Helper::userTrialSubscrriptionStatus($trialEndsAtDate);

				$plan = SubscriptionPlan::where('plan_name', 'Trial')->first();

				$plan = [
					'name' => 'Free '. $plan->plan_name,
					'days_left' => $trialDaysLeft,
					'table_plan_limit' => $plan->table_plan_limit,
					'status' => 'trial'
				];

				return $this->success('Subscriptions details retrieved successfully', [
					'plan' => $plan
				]);
			}

			if(!$subscription || ($subscription && !$subscription->ends_at)) {
				$plan = [
					'status' => 'non-subscribed',
					'message' => 'No subscription found'
				];

				return $this->success('No subscription found', [
					'plan' => $plan
				]);
			}

			$subscriptionEndDate = $subscription->ends_at;

			$plan = SubscriptionPlan::where('stripe_plan', $subscription->stripe_price)->first();

			$plan = [
				'plan_id' => $plan->stripe_plan,
				'name' => $plan->plan_name,
				'price' => $plan->plan_price,
				'duration' => $plan->subscriptionType->name,
				'ends_at' => Carbon::parse($subscription->ends_at)->format('M d Y'),
				'table_plan_limit' => $plan->table_plan_limit,
				'status' => 'subscribed'
			];

			return $this->success('Subscriptions details retrieved successfully', [
				'plan' => $plan
			]);

		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
	}
}