<?php

namespace App\Http\Controllers\API;

use App\Models\User;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\ResetPasswordRequest;

class ResetPasswordController extends Controller
{
	use ApiResponser;

	/**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest');
    }

	/**
     * Reset the given user's password.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function resetPassword(ResetPasswordRequest $request)
    {
		try {
			$user = User::where('email', $request->email)->first();
			if($user) {
				$user->update($request->only('password'));

				return $this->success('password has been successfully reset');
			} else {
				return $this->error('Email not found', 200);
			}
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }
}
