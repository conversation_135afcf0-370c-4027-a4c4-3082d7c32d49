<?php

namespace App\Http\Controllers\API;

use Exception;
use App\Models\SubscriptionPlan;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\SubscriptionPlanResource;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\StoreSubscriptionPlanRequest;
use App\Http\Requests\UpdateSubscriptionPlanRequest;

class SubscriptionPlanController extends Controller
{
	use ApiResponser;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
		try {
			$subscriptionPlans = SubscriptionPlan::where('is_active', 1)->get();

			return $this->success('All subscription plans retrieved successfully', [
				'subscriptionPlans' => SubscriptionPlanResource::collection($subscriptionPlans)
			]);
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSubscriptionPlanRequest $request)
    {
        try {
			$subscriptionPlan = SubscriptionPlan::create([
				'plan_name' => $request->plan_name,
				'subscription_type' => $request->subscription_type,
				'plan_price' => $request->plan_price ?? 0,
				'plan_price_effect_date' => $request->plan_price_effect_date,
				'plan_price_end_date' => $request->plan_price_end_date,
				'table_plan_limit' => $request->table_plan_limit,
				'price_per_table' => $request->price_per_table,
				'key_benefits' => $request->key_benefits,
				'is_active' => $request->is_active ?? 0,
				'stripe_plan' => $request->stripe_plan ?? null,
				'subscription_type_id' => $request->subscription_type_id
			]);

			return $this->success('Subscription plan created successfully!',
				[
					'subscriptionPlan' => new SubscriptionPlanResource(SubscriptionPlan::find($subscriptionPlan->id)),
				]
			);
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
			// return auth()->user();
			$subscriptionPlan = SubscriptionPlan::find($id);

			if(!$subscriptionPlan) {
				return $this->error('No record found', 404);
			} else {
				return $this->success('Subscription plan retrieved successfully!',
					[
						'subscriptionPlan' => new SubscriptionPlanResource(SubscriptionPlan::find($subscriptionPlan->id))
					]
				);
			}
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateSubscriptionPlanRequest $request, string $id)
    {
        try {
			$subscriptionPlan = SubscriptionPlan::find($id);

			if(!$subscriptionPlan) {
				return $this->error('No record found', 404);
			} else {
				$subscriptionPlan->plan_name =  $request->plan_name;
				$subscriptionPlan->subscription_type =  $request->subscription_type;
				$subscriptionPlan->plan_price =  $request->plan_price;
				$subscriptionPlan->plan_price_effect_date =  $request->plan_price_effect_date;
				$subscriptionPlan->plan_price_end_date =  $request->plan_price_end_date;
				$subscriptionPlan->table_plan_limit =  $request->table_plan_limit;
				$subscriptionPlan->price_per_table = $request->price_per_table;
				$subscriptionPlan->key_benefits = $request->key_benefits;
				$subscriptionPlan->is_active = $request->is_active ?? 0;
				$subscriptionPlan->stripe_plan = $request->stripe_plan ?? null;
				$subscriptionPlan->save();
			}

			return $this->success('Subscription plan updated successfully!',
				[
					'subscriptionPlan' => new SubscriptionPlanResource(SubscriptionPlan::find($subscriptionPlan->id))
				]
			);
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
			$subscriptionPlan = SubscriptionPlan::find($id);
			if(!$subscriptionPlan) {
				return $this->error('No record found', 404);
			}
			else {
				$subscriptionPlan->delete();
				return $this->success('Subscription plan deleted successfully!', []);
			}
		}
		catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
    }

	/**
	 * Filter subscription plans
	 *
	 * @return Json
	 */
	public function filterSubscriptionPlans(Request $request) {

		$validated = $request->validate([
			'subscription_type_id' => 'required|exists:subscription_type,id',
		]);

		try {
			$subscriptionPlans = SubscriptionPlan::where('subscription_type_id', $request->subscription_type_id)
									->where('is_active', 1)
									->get();

			$intent = auth()->user()->createSetupIntent();

			if($subscriptionPlans) {
				return $this->success('All subscription plans retrieved successfully', [
					'subscriptionPlans' => SubscriptionPlanResource::collection($subscriptionPlans),
					'intent' => $intent->client_secret
				]);
			} else {
				return $this->success('No subscription plans found', [
					'subscriptionPlans' => [],
				]);
			}
		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
	}
}
