<?php

namespace App\Http\Controllers\API;

use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use App\Models\ClientDatabase;
use App\Models\ClientDbServer;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Http\Resources\ClientDatabaseResource;

class ClientDatabaseController extends Controller
{
	use ApiResponser;


	/**
	 * Get specific remote server databases list
	 */
	public function getAllDatabasesList(Request $request)
	{
		try
		{
			$startTime = microtime(true);
			$dbServerId = $request->db_server_id ?? null;

			if (!$dbServerId)
			{
				return $this->error('Invalid database server ID!', 400);
			}

			$dbServer = ClientDbServer::find($dbServerId);

			if (!$dbServer)
			{
				return $this->error('Database server not found!', 400);
			}

			$clientDatabasesList = ClientDatabase::with(['clientDBSchema' => function($query) {
				$query->select('id', 'client_database_id');
			}])
			->where('client_db_server_id', $dbServerId)
			->where('is_deleted', 0)
			->get();

			$endTime = microtime(true);
			$executionTime = $endTime - $startTime;
			Log::info("Get All Databases list Query took " . $executionTime . " seconds to execute.");

			return $this->success('All database servers retrieved successfully', [
				'dbServers' => ClientDatabaseResource::collection($clientDatabasesList)
			]);
		}
		catch (\Throwable $th)
		{
			Log::error($th);
			return $this->error('Something went wrong!', 400);
		}
	}

	/**
	 * Delete specific database schema
	 */
	public function deleteClientDatabase(Request $request)
	{
		try
		{
			$ids = $request->ids;

			if (!$ids)
			{

				return $this->error('No client DB schema ID provided', 404);
			}
			else
			{

				foreach ($ids as $id)
				{
					$clientDatabase = ClientDatabase::find($id);
					if (!$clientDatabase)
					{
						return $this->error('Invalid client DB schema ID', 404);
					}

					$clientDatabase->is_deleted = 1;
					$clientDatabase->save();

					$clientDatabase->delete();

					// Delete client DB Schemas, tables and columns
				}

				return $this->success('Client database deleted successfully!', []);
			}
		}
		catch (\Throwable $th)
		{
			Log::error($th);
			return $this->error('Something went wrong', 400);
		}
	}
}
