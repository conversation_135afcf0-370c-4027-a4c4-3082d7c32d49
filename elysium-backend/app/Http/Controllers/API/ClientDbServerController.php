<?php

namespace App\Http\Controllers\API;

use Illuminate\Http\Request;
use App\Models\ClientDbServer;
use App\Http\Controllers\Controller;

class ClientDbServerController extends Controller
{

    /**
     * get Server listing for scheduling setup.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(ClientDbServer $clientDbServer)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ClientDbServer $clientDbServer)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ClientDbServer $clientDbServer)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ClientDbServer $clientDbServer)
    {
        //
    }
}
