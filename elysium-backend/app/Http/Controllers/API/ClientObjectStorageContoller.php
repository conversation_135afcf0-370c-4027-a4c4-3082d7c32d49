<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ClientObjectStorage;
use App\Models\ObjectStorageType;
use App\Traits\ApiResponser;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Http\Resources\ClientObjectStorageResource;
use App\Http\Requests\ClientObjectStorageRequest;

class ClientObjectStorageContoller extends Controller
{
	use ApiResponser;

	/**
	 * Get client object storage information
	 */
	public function getClientObjectStorage()
	{
		try {
			$companyId = Auth::user()->company_id;

			$clientObjectStorage = ClientObjectStorage::where('company_id', $companyId)->first();

			if($clientObjectStorage) {
				return $this->success('Client object storage information retrieved successfully',
					[
						'clientObjectStorage' => new ClientObjectStorageResource($clientObjectStorage)
					]
				);
			} else {
				return $this->success('No client object storage found',
					[
						'clientObjectStorage' => []
					]
				);
			}
		} catch (\Throwable $th) {
			Log::info($th->getMessage());
			return $this->error('Something went wrong', 400);
		}
	}

	/**
	 * Create or update client object storage information
	 */
	public function storeClientObjectStorage(ClientObjectStorageRequest $request)
	{
		try {
			$companyId = Auth::user()->company_id;
			$cloudProviderName = $request->cloud_provider_name;
			$objectStorageTypeName = $request->object_storage_type_name;

			$objectStorageTypeId = ObjectStorageType::where('cloud_provider_name', $cloudProviderName)->where('object_storage_type', $objectStorageTypeName)->select('id')->first();

			if(!$objectStorageTypeId) {
				return $this->error('Invalid cloud provider name or object storage type provided', 404);
			}

			$clientObjectStorage = ClientObjectStorage::where('company_id', $companyId)->first();

			if($clientObjectStorage) {
				$message = 'Object storage updated successfully';
			} else {
				$message = 'Object storage created successfully';
			}

			$result = ClientObjectStorage::updateOrCreate(
				[
					'company_id'   => $companyId
				],
				[
					'bucket_name' => $request->bucket_name,
					'object_storage_type_id' => $objectStorageTypeId['id'],
				],
			);

			if($result) {

				$clientObjectStorage = ClientObjectStorage::where('company_id', $companyId)->first();

				return $this->success($message,
					[
						'company' => new ClientObjectStorageResource($clientObjectStorage)
					]
				);
			} else {
				return $this->error('Something went wrong', 400);
			}
		} catch (\Throwable $th) {
			return $th;
			Log::info($th->getMessage());
			return $this->error('Something went wrong', 400);
		}
	}
}
