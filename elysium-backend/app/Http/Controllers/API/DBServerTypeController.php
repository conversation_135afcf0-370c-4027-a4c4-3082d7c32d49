<?php

namespace App\Http\Controllers\API;

use Illuminate\Http\Request;
use App\Models\DBServerType;
use App\Traits\ApiResponser;
use App\Http\Controllers\Controller;
use App\Http\Resources\DBServerTypeResource;

class DBServerTypeController extends Controller
{
	use ApiResponser;
    /**
	 * Get all database server types list
	 *
	 * @return json
	 */
	public function getAllDBServerTypes(Request $request) {
		try {
			$dbServerTypes = DBServerType::where('is_active', 1)->get();

			if($dbServerTypes) {
				return $this->success('All database server types retrieved successfully', [
					'dbServerTypes' => DBServerTypeResource::collection($dbServerTypes)
				]);
			} else {
				return $this->success('No database server type found', []);
			}

		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
	}
}
