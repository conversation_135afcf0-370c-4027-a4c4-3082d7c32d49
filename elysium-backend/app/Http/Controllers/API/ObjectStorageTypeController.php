<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ObjectStorageType;
use App\Traits\ApiResponser;
use Illuminate\Support\Facades\Log;
use App\Http\Requests\CloudProviderNameRequest;

class ObjectStorageTypeController extends Controller
{
	use ApiResponser;

	/**
     * Display a listing of the resource.
     */
    public function getAllCloudProviderNames()
    {
		try {
			$cloudProviderNames = ObjectStorageType::distinct()->select('cloud_provider_name')->get();

			return $this->success('All cloud storage types retrieved successfully', [
				'cloudProviderNames' => $cloudProviderNames
			]);
		} catch (\Throwable $th) {
			Log::info($th->getMessage());
			return $this->error('Something went wrong', 400);
		}
    }

	/**
	 * Get cloud provider names according to object storage type
	 */
	public function getObjectStorageTypes(CloudProviderNameRequest $request)
	{
		try {
			$cloudProviderName = $request->cloud_provider_name;

			$objectStorageTypes = ObjectStorageType::distinct()->where('cloud_provider_name', $cloudProviderName)->select('object_storage_type')->get();

			if(isset($objectStorageTypes) && !count($objectStorageTypes)) {
				return $this->error('Invalid cloud provider name provided', 404);
			}

			return $this->success('All object storage types retrieved successfully', [
				'objectStorageTypes' => $objectStorageTypes
			]);
		} catch (\Throwable $th) {
			Log::info($th->getMessage());
			return $this->error('Something went wrong', 400);
		}
	}
}
