<?php

namespace App\Http\Controllers\API;

use Carbon\Carbon;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use App\Models\ClientDbServer;
use App\Http\Controllers\Controller;
use App\Http\Requests\DatabaseStatsRequest;
use App\Http\Resources\DashboardServersSatsResource;
use App\Http\Resources\DashboardServerPieChartDataResource;
use App\Traits\ClubData;

class DatabaseStatController extends Controller
{
    use ApiResponser, ClubData;

    /**
     * Database Archive data api.
     * 
     * @param  \Illuminate\Http\Request  $request
     */
    public function databasesTotalArchiveStats(DatabaseStatsRequest $request)
    {
        try
        {
            $company = auth()->user()->company_id;
            $server_id = $request->id;
            $database_id = $request->database_id;
            if ($request->flag == 'a')
            {
                $startDate = null;
                $endDate = null;
            }
            else
            {
                $startDate = Carbon::createFromFormat('Y-m-d', $request->start_date)->startOfDay();
                $endDate = Carbon::createFromFormat('Y-m-d', $request->end_date)->endOfDay();
            }

            $databases = ClientDbServer::with([
                'client_databases_instanses' => function ($query) use ($database_id)
                {
                    $query->where('id', $database_id);
                },
                'client_databases_instanses.client_db_tables_instanses.table_archiving_stats' => function ($query) use ($startDate, $endDate)
                {
                    $query->select('id', 'client_db_server_table_id', 'total_table_data_size_archived', 'stat_date')

                        ->when($startDate && $endDate, function ($query) use ($startDate, $endDate)
                        {
                            $query->dateRange($startDate, $endDate);
                        });
                },
            ])->select('id', 'company_id', 'db_server_name')
                ->where('company_id', $company)
                ->where('id', $server_id)
                ->whereNull('deleted_at')
                ->get();
            $data = $databases->flatMap(function ($server)
            {

                return $server->client_databases_instanses->map(function ($databaseInstance)
                {
                    $totalTableDataArchived = $databaseInstance->client_db_tables_instanses->flatMap->table_archiving_stats->sum('total_table_data_size_archived');
                    $totalTablesArchived = $databaseInstance->client_db_tables_instanses->flatMap->table_archiving_stats->groupBy('client_db_server_table_id')->count('client_db_server_table_id');
                    if ($databaseInstance->client_db_tables_instanses->isNotEmpty())
                    {
                        return [
                            'server_id' => $databaseInstance->client_db_server_id,
                            'client_database_id' => $databaseInstance->id,
                            'database_name' => $databaseInstance->db_name,
                            'total_table_data_archived' => $totalTableDataArchived /1024,
                            'total_tables_archived' => $totalTablesArchived,
                        ];
                    }
                    else
                    {
                        return [
                            'server_id' => $databaseInstance->client_db_server_id,
                            'client_database_id' => $databaseInstance->id,
                            'database_name' => $databaseInstance->db_name,
                            'total_table_data_archived' => 0,
                            'total_tables_archived' => 0,
                        ];
                    }
                });
            });

            return $this->success('Database archived data retrieved successfully', [
                'database_archived_data' => DashboardServersSatsResource::collection($data)
            ]);
        }
        catch (\Throwable $th)
        {
            return $this->error('Something went wrong', 400);
        }
    }

    /**
     * Database Archive tables listing data api.
     * 
     * @param  \Illuminate\Http\Request  $request
     */
    public function databaseTablesListing(DatabaseStatsRequest $request)
    {
        try
        {
            $company = auth()->user()->company_id;
            $server_id = $request->id;
            $database_id = $request->database_id;
            if ($request->flag == 'a')
            {
                $startDate = null;
                $endDate = null;
            }
            else
            {
                $startDate = Carbon::createFromFormat('Y-m-d', $request->start_date)->startOfDay();
                $endDate = Carbon::createFromFormat('Y-m-d', $request->end_date)->endOfDay();
            }
            $server = ClientDbServer::with([
                'client_databases_instanses' => function ($query) use ($database_id) {
                    $query->where('id', $database_id);
                },
                'client_databases_instanses.client_db_tables_instanses' => function ($query) use ($database_id, $server_id) {
                    $query->where('client_database_id', $database_id)->where('client_db_server_id', $server_id);
                },
                'client_databases_instanses.client_db_tables_instanses.table_archiving_stats' => function ($query) use ($startDate, $endDate) {
                    $query->select('id', 'client_db_server_table_id', 'total_table_rows_archived', 'total_table_data_size_archived', 'stat_date')
                        ->when($startDate && $endDate, function ($query) use ($startDate, $endDate) {
                            $query->whereBetween('stat_date', [$startDate, $endDate]);
                        });
                },
            ])->select('id', 'company_id', 'db_server_name')
                ->where('company_id', $company)
                ->where('id', $server_id)
                ->whereNull('deleted_at')
                ->first();
            $data = [];
            foreach ($server->client_databases_instanses as $databaseInstance)
            {
                foreach ($databaseInstance->client_db_tables_instanses as $tableInstance)
                {
                    $total_current_db_size = $databaseInstance->total_current_db_size;
                    $table_data = [
                        'table_id' => $tableInstance->id,
                        'database_id' => $tableInstance->client_database_id,
                        'server_id' => $databaseInstance->client_db_server_id,
                        'total_current_db_size' => $databaseInstance->total_current_db_size,
                        'color_code' => $tableInstance->color_code,
                        'table_name' => $tableInstance->table_name,
                    ];
                    $total_rows_archived = 0;
                    $total_tablesize_archived = 0;
                    $total_table_archive_percentage = 0;

                    foreach ($tableInstance->table_archiving_stats as $tableStats)
                    {
                        $total_rows_archived += ($tableStats->total_table_rows_archived ?? 0);
                        $total_tablesize_archived += ($tableStats->total_table_data_size_archived ?? 0);

                        if ($total_tablesize_archived > 0)
                        {
                            $total_table_archive_percentage = round(($total_tablesize_archived / $total_current_db_size) * 100, 2);
                        }
                    }
                    $table_data['total_table_rows_archived'] = $total_rows_archived;
                    $table_data['total_table_size_archived'] = $total_tablesize_archived;
                    $table_data['total_table_archive_percentage'] = $total_table_archive_percentage;

                    $data[] = $table_data;
                }
            }



            return $this->success('Database archived tables lsiting retrieved successfully', [
                'database_tables_list' => DashboardServersSatsResource::collection($data)
            ]);
        }
        catch (\Throwable $th)
        {
            return $this->error('Something went wrong', 400);
        }
    }

    /**
     * Database Archive barchart data api.
     * 
     * @param  \Illuminate\Http\Request  $request
     */
    public function databaseTablesBarChart(DatabaseStatsRequest $request)
    {
        try
        {
            $company = auth()->user()->company_id;
            $server_id = $request->id;
            $database_id = $request->database_id;
            if ($request->flag == 'a')
            {
                $startDate = null;
                $endDate = null;
            }
            else
            {
                $startDate = Carbon::createFromFormat('Y-m-d', $request->start_date)->startOfDay();
                $endDate = Carbon::createFromFormat('Y-m-d', $request->end_date)->endOfDay();
            }
            $server = ClientDbServer::with([
                'client_databases_instanses' => function ($query) use ($database_id) {
                    $query->where('id', $database_id);
                },
                'client_databases_instanses.client_db_tables_instanses' => function ($query) use ($database_id, $server_id) {
                    $query->where('client_database_id', $database_id)->where('client_db_server_id', $server_id);
                },
                'client_databases_instanses.client_db_tables_instanses.table_archiving_stats' => function ($query) use ($startDate, $endDate) {
                    $query->select('id', 'client_db_server_table_id', 'total_table_rows_archived', 'total_table_data_size_archived', 'stat_date')
                        ->when($startDate && $endDate, function ($query) use ($startDate, $endDate) {
                            $query->whereBetween('stat_date', [$startDate, $endDate]);
                        });
                },
            ])->select('id', 'company_id', 'db_server_name')
                ->where('company_id', $company)
                ->where('id', $server_id)
                ->whereNull('deleted_at')
                ->first();
            $formattedData = [];
            $allDates = $this->getDatesBetween($startDate, $endDate);
            $formattedDates = ['dates' => $allDates];

            if (empty($server->client_databases_instanses))
            {
                return $this->success('Database not found', []);
            }
            if (empty($server->client_databases_instanses[0]->client_db_tables_instanses))
            {
                return $this->success('Tables not found', []);
            }
            foreach ($server->client_databases_instanses[0]->client_db_tables_instanses as $db_instanse)
            {
                $formattedData[] = [
                    'name' => $db_instanse->table_name,
                    'color_code' => $db_instanse->color_code,
                    'data' => [],
                ];
                $client_db_instanse_data = $db_instanse->table_archiving_stats->toArray();
                foreach ($client_db_instanse_data as  $client_db_instanse_dat)
                {
                    $key_value = $client_db_instanse_dat['stat_date'];
                    if (!in_array($key_value, $formattedDates['dates']))
                    {
                        $formattedDates['dates'][] = $key_value;
                    }
                    $formattedData[count($formattedData) - 1]['data'][$key_value] = $client_db_instanse_dat['total_table_data_size_archived'];
                }
            }
            asort($formattedDates['dates']);

            $result = $this->clubDataWithFlag($formattedDates, $formattedData, $request->flag);

            return $this->success('Database bar chart stats retrieved successfully', [
                'database_barChart' => DashboardServerPieChartDataResource::collection($result)
            ]);
        }
        catch (\Throwable $th)
        {
            return $this->error('Something went wrong', 400);
        }
    }
}
