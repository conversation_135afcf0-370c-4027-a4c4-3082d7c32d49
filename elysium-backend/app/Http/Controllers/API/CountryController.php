<?php

namespace App\Http\Controllers\API;

use App\Models\Country;
use Illuminate\Http\Request;
use App\Traits\ApiResponser;
use App\Http\Controllers\Controller;
use App\Http\Resources\CountryResource;

class CountryController extends Controller
{
	use ApiResponser;

	/**
	 * Get all countries list
	 */
	public function getAll(Request $request)
	{
		try {
			$countries = Country::where('is_active', 1)->get();

			if($countries) {
				return $this->success('All countries retrieved successfully', [
					'countries' => CountryResource::collection($countries)
				]);
			} else {
				return $this->success('No country found', []);
			}

		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
	}
}
