<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ClientDBSchema;
use App\Models\ClientDbServer;
use App\Traits\ApiResponser;
use Illuminate\Support\Facades\Log;
use App\Http\Resources\ClientDBSchemaResource;

class ClientDBSchemaController extends Controller
{
	use ApiResponser;

    /**
	 * Get specific remote server databases list
	 */
	public function getAllDatabasesList(Request $request) {
		try {
			$startTime = microtime(true);
			$dbServerId = $request->db_server_id ?? null;

			if(!$dbServerId) {
				return $this->error('Invalid database server ID!', 400);
			}

			$dbServer = ClientDbServer::find($dbServerId);

			if(!$dbServer) {
				return $this->error('Database server not found!', 400);
			}

			$dbServersList = ClientDBSchema::where('client_db_server_id', $dbServerId)->get();

			$endTime = microtime(true);
			$executionTime = $endTime - $startTime;
			Log::info("Get All Databases list Query took " . $executionTime . " seconds to execute.");

			if($dbServersList) {
				return $this->success('All database servers retrieved successfully', [
					'dbServers' => ClientDBSchemaResource::collection($dbServersList)
				]);
			} else {
				return $this->success('All database servers retrieved successfully', [
					'dbServers' => []
				]);
			}
		} catch (\Throwable $th) {
			Log::error($th);
			return $this->error('Something went wrong!', 400);
		}
	}

	/**
	 * Delete specific database schema
	 */
	public function deleteDBSchema(Request $request) {
		try {
			$ids = $request->ids;

			if(!$ids) {

				return $this->error('No client DB schema ID provided', 404);

			} else {

				foreach($ids as $id) {
					$clientDBSchema = ClientDBSchema::find($id);
					if(!$clientDBSchema) {
						return $this->error('Invalid client DB schema ID', 404);
					}

					$clientDBSchema->delete();
				}

				return $this->success('Deleted successfully!', []);
			}

		} catch (\Throwable $th) {
			return $this->error('Something went wrong', 400);
		}
	}
}
