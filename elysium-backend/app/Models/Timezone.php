<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Timezone extends Model
{
    use HasFactory;

	/**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

	/**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'timezone';


	/**
	 * The attributes that are mass assignable
	 *
	 * @var Array
	 */
	protected $fillable = [
		'name'
	];
}
