<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ClientObjectStorage extends Model
{
    use HasFactory;

	/**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'client_object_storage';

	/**
	 * The attributes that are mass-assignable
	 *
	 * @var array
	 */
	protected $fillable = [
		'bucket_name',
		'object_storage_type_id',
		'company_id'
	];

	/**
	 * Relationships
	 */

	/**
     * Get the client object storage instances associated with the object storage type.
     */
    public function objectStorageType()
    {
        return $this->belongsTo(ObjectStorageType::class, 'object_storage_type_id', 'id');
    }

	/**
	 * Get the company which owns the user
	*/
	public function company()
	{
		return $this->belongsTo(Company::class, 'company_id');
	}
}
