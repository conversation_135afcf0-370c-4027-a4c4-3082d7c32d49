<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class Country extends Model
{
    use HasFactory, SoftDeletes;

	/**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'country';

	/**
	 * The attributes that are mass assignable
	 *
	 * @var array
	 */
	protected $fillable = [
		'country_name',
		'is_active'
	];

	/**
	 * Relationships
	 */

	 /**
	 * Get the states for the country
	 */
	public function states()
	{
		return $this->hasMany(CountryState::class);
	}
}
