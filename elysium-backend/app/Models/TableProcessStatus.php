<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TableProcessStatus extends Model
{
    use HasFactory;

	/**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'table_process_status';

	/**
	 * The attributes that are mass assignable
	 *
	 * @var array
	 */
	protected $fillable = [
		'table_action_name'
	];

	/**
	 * Relationships
	 */

	/**
	 * Get the tables associated with the table process status
	 */
	public function client_db_server_table()
    {
        return $this->hasMany(ClientDBServerTable::class);
    }
}
