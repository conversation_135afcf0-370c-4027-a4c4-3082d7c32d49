<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class DBJobSchedule extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'database_job_schedule';

    protected $fillable = [
        'client_db_server_id',
        'client_database_id',
        'server_name',
        'database_name'
    ];
    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function table_job_schedule()
    {
        return $this->hasMany(ScheduleProcessingList::class, 'database_job_schedule_id', 'id');
    }
}
