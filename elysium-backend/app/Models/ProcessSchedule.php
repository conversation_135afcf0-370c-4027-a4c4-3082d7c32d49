<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProcessSchedule extends Model
{
    use HasFactory, SoftDeletes;

    const IN_PROGRESS = 'in_progress';
    const AWAITING = 'awaiting';
    const TIMEOUT = 'time_window_expired';
    const SUCCESS_EXEC = 'execution_successfull';
    const CONNECTION_FAIL = 'ssh_connection_failed';

    protected $table = 'table_job_schedule_execution_log';

    protected $fillable = [
        'id',
        'table_job_schedule_id',
        'day_of_week',
        'execution_datetime',
        'status',
        'request_json',
        'schedule_enc_key',
        'response_json'
    ];


    public function table_job_schedule()
    {
        return $this->belongsTo(ScheduleProcessingList::class, 'table_job_schedule_id');
    }
}
