<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ClientDatabase extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'client_database';

    /**
     * The attrivutes that are mass assignable
     */
    protected $fillable = [
        'client_db_server_id',
        'db_name',
        'total_current_db_size',
        'total_current_db_data_size',
        'total_current_db_index_size',
        'total_table'
    ];

    /**
     * Relationships
     */
    public function clientDBServer()
    {
        return $this->belongsTo(DBServer::class, 'client_db_server_id', 'id');
    }

    public function client_db_tables()
    {
        return $this->hasMany(ClientDBServerTable::class, 'client_database_id', 'id');
    }
    public function client_db_tables_instanses()
    {
        return $this->hasMany(ClientDBServerTable::class, 'client_database_id', 'id')->select('id', 'client_database_id', 'client_db_server_id', 'table_name',);
    }
    public function db_server_tables_stats()
    {
        return $this->hasMany(ClientDbServerTableStat::class, 'client_database_id', 'id');
    }
    public function db_server_tables_stats_instanses()
    {
        return $this->hasMany(ClientDbServerTableStat::class, 'client_database_id', 'id')->select('id', 'client_database_id',  'stat_date', 'total_table_data_size_archived');
    }
    public function database_schedule()
    {
        return $this->hasMany(DBJobSchedule::class, 'client_database_id', 'id');
    }

    /**
     * Get the associated client DB schema
     */
    public function clientDBSchema()
    {
        return $this->hasOne(ClientDBSchema::class, 'client_database_id', 'id');
    }
}
