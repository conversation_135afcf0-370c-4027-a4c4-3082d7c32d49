<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ClientDBServerTableColumn extends Model
{
    use HasFactory;

	/**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'client_db_server_table_column';

	/**
	 * The attributes that are mass assignable
	 *
	 * @var array
	 */
	protected $fillable = [
		'client_db_server_table_id',
		'table_schema_name',
		'table_database_name',
		'table_name',
		'column_name',
		'ordinal_position',
		'is_nullable',
		'data_type',
		'character_max_length',
		'column_type',
		'column_key',
		'column_comment'
	];

	/**
	 * Relationships
	 */

	/**
	 * Get the table associated with the client db table column
	*/
	public function client_db_server_table()
	{
		return $this->belongsTo(ClientDBServerTable::class, 'client_db_server_table_id', 'id');
	}
}
