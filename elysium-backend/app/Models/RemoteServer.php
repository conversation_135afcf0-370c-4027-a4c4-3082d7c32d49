<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class RemoteServer extends Model
{
    use HasFactory, SoftDeletes;

	/**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'remote_server';

	/**
	 * The attributes that are mass assignable
	 *
	 * @var array
	 */
	protected $fillable = [
		'name',
		'company_id',
		'hostname',
		'username',
		'port',
		'agent_uuid',
		'os_type',
		'remote_server_status_id',
		'is_tls_required',
		'is_valid',
		'is_active'
	];

	/**
	 * Relationships
	 */

	/**
     * Get the remote server filter that owns the remote server connection status.
     */
    public function remoteServerFilter()
    {
        return $this->belongsTo(RemoteServerFilter::class, 'remote_server_status_id', 'id');
    }

	/**
	 * Get all client db servers associated with the remote server
	 */
	public function clientDBServers()
	{
		return $this->belongsTo(DBServer::class, 'remote_server_id', 'id');
	}
	public function client_servers()
	{
		return $this->hasMany(ClientDbServer::class, 'remote_server_id', 'id');
	}
}
