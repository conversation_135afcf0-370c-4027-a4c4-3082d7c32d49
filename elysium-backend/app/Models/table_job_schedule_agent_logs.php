<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\table_job_schedule;
use App\Models\table_job_schedule_execution_log;


class table_job_schedule_agent_logs extends Model
{
    use HasFactory, SoftDeletes;
    protected $table = 'table_job_schedule_agent_logs';
    protected $fillable = [
        'table_job_schedule_id',
        'table_job_scheduel_execution_log_id',
        'log_message',
        'log_type'
    ];
    public function table_job_schedule()
    {
        return $this->belongsTo(table_job_schedule::class, 'table_job_schedule_id', 'id');
    }
    public function table_job_schedule_execution_log()
    {
        return $this->belongsTo(table_job_schedule_execution_log::class, 'table_job_scheduel_execution_log_id', 'id');
    }
}
