<?php

namespace App\Models;

use App\Models\ClientDatabase;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ClientDbServer extends Model
{

    use HasFactory;
    use SoftDeletes;

    protected $table = 'client_db_server';


    public function server_stats()
    {
        return $this->hasMany(ClientDbServerStat::class);
    }
    public function server_table_stats()
    {
        return $this->hasMany(ClientDbServerTableStat::class, 'client_db_server_id', 'id');
    }
    // public function server_db_server_tables()
    // {
    //     return $this->hasMany(ClientDbServerTable::class, 'client_db_server_table_uuid', 'client_db_server_uuid');
    // }
    public function client_databases()
    {
        return $this->hasMany(ClientDatabase::class, 'client_db_server_id', 'id');
    }
    public function client_databases_instanses()
    {
        return $this->hasMany(ClientDatabase::class, 'client_db_server_id', 'id')->select('id', 'client_db_server_id', 'db_name', 'color_code','total_current_db_size');
    }


    public function remote_server()
    {
        return $this->belongsTo(RemoteServer::class,'remote_server_id', 'id');
    }
    public function client_server_table()
    {
        return $this->hasMany(ClientDBServerTable::class,'client_db_server_id', 'id');
    }
}
