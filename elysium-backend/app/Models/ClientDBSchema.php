<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class ClientDBSchema extends Model
{
	use HasFactory, SoftDeletes;

	protected $table = 'client_db_schema';

	/**
	 * The attributes that are mass-assignable
	 */
	protected $fillable = [
		'client_database_id',
		'schema_name'
	];

	/**
	 * Accessors
	 */
	public function getCreatedAtAttribute($value)
	{
		return Carbon::parse($value)->format('Y-m-d H:i:s');
	}

	public function getUpdatedAtAttribute($value)
	{
		return Carbon::parse($value)->format('Y-m-d H:i:s');
	}

	/**
	 * Relationships
	 */

	/**
	 * Get the client db server associated with the client database
	 */
	public function client_db_server()
	{
		return $this->belongsTo(DBServer::class, 'client_db_server_id', 'id');
	}

	/**
	 * Get the tables information associated with the schema
	 */
	public function client_db_server_tables()
	{
		return $this->belongsTo(ClientDBServerTable::class, 'client_db_schema_id', 'id');
	}
	public function client_db_tables()
	{
		return $this->hasMany(ClientDBServerTable::class, 'client_db_schema_id', 'id');
	}
	public function client_db_tables_instanses()
	{
		return $this->hasMany(ClientDBServerTable::class, 'client_db_schema_id', 'id')->select('id', 'client_db_schema_id', 'client_db_server_id', 'table_name');
	}
	public function db_server_tables_stats()
	{
		return $this->hasMany(ClientDbServerTableStat::class, 'client_db_schema_id', 'id');
	}
	public function db_server_tables_stats_instanses()
	{
		return $this->hasMany(ClientDbServerTableStat::class, 'client_db_schema_id', 'id')->select('id', 'client_db_schema_id',  'db_name', 'stat_date', 'total_table_data_size_archived');
	}
}
