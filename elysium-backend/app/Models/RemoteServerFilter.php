<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class RemoteServerFilter extends Model
{
    use HasFactory, SoftDeletes;

	/**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'remote_server_status';

	/**
	 * The attributes that are mass assignable
	 */
	protected $fillable = [
		'name'
	];

	/**
	 * Relationships
	 */

	/**
     * Get the remote servers for the remote server filter status.
     */
    public function agents()
    {
        return $this->hasMany(RemoteServer::class);
    }

}
