<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class DimDate extends Model
{
    use HasFactory;
    use SoftDeletes;


    protected $table = 'dim_date';




    public function client_db_server_stat_data()
    {
        return $this->hasMany(ClientDbServerStat::class, 'dim_date_id', 'id');
    }
    public function client_db_table_stat_data()
    {
        return $this->hasMany(ClientDbServerTableStat::class, 'dim_date_id', 'id');
    }
}
