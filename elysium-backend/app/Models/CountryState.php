<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class CountryState extends Model
{
    use HasFactory, SoftDeletes;

	/**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'state';


	/**
	 * The attributes that are mass assignable
	 *
	 * @var array
	 */
	protected $fillable = [
		'state_name',
		'country_id',
	];

	/**
	 * Relationships
	 */

	/**
	 * Get the country that owns the state
	 */
	public function country()
	{
		return $this->belongsTo(Country::class);
	}
}
