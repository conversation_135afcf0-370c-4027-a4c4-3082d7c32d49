<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class SubscriptionPlan extends Model
{
    use HasFactory, SoftDeletes;

	/**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'subscription_plan';

	/**
	 * The attributes that should be cast
	 *
	 * @var array
	 */
	protected $casts = [
        'key_benefits' => 'array'
    ];

	/**
	 * The attributes that are mass assignable
	 *
	 * @var array
	 */
	protected $fillable = [
		'plan_name',
		'subscription_type_id',
		'plan_price',
		'plan_price_effect_date',
		'plan_price_end_date',
		'table_plan_limit',
		'price_per_table',
		'key_benefits',
		'stripe_plan',
		'is_active'
	];

	/**
	 * Relationships
	 */

	/**
	 * Get the subscription type that owns the subscription plan
	 */
	public function subscriptionType()
    {
        return $this->belongsTo(SubscriptionType::class);
    }
}
