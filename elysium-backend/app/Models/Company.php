<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Company extends Model
{
    use HasFactory, SoftDeletes;

	/**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'company';

	/**
	 * The attributes that are mass assignable
	 *
	 * @var array
	 */
	protected $fillable = [
		'company_name',
		'country_id',
		'state_id',
		'company_address',
		'company_address2',
		'company_city',
		'company_postalcode',
		'database_server_option_id',
		'database_table_option_id'
	];

	/**
	 * Relationships
	 */

	/**
	 * Get the country that owns the company
	 */
	public function country()
	{
		return $this->belongsTo(Country::class);
	}

	/**
	 * Get the state that owns the company
	 */
	public function state()
	{
		return $this->belongsTo(CountryState::class);
	}

	/**
	 * Get the company which owns the user
	*/
	public function serverOption()
	{
		return $this->belongsTo(DatabaseServerOption::class, 'database_server_option_id');
	}

	/**
	 * Get the company which owns the user
	*/
	public function tableOption()
	{
		return $this->belongsTo(DatabaseTableOption::class, 'database_table_option_id');
	}
}
