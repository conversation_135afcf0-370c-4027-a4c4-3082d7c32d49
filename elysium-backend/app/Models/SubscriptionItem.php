<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Laravel\Cashier\SubscriptionItem as CashierSubscriptionItem;

class SubscriptionItem extends CashierSubscriptionItem
{
    use HasFactory;

	protected $table = 'subscription_item';

	protected $fillable  = [
		'stripe_id',
		'stripe_product'
	];

	/**
	 * Relationships
	 */

	/**
	 * Get the subscription belongs to the subscription item
	 */
	public function subscription()
    {
        return $this->belongsTo(Subscription::class);
    }
}
