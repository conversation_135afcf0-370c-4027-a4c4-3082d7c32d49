<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Model;

class DBServer extends Model
{
    use HasFactory;

		/**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'client_db_server';

	/**
	 * The attributes that are mass assignable
	 *
	 * @var array
	 */
	protected $fillable = [
		'company_id',
		'db_server_name',
		'db_server_alias_name',
		'client_db_server_uuid',
		'hostname',
		'username',
		'password',
		'port',
		'remote_server_id',
		'client_db_server_type_id',
		'remote_server_status_id',
		'is_active',
		'timezone',
		'total_current_db_storage_setup_size'
	];

	/**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'is_admin' => 'boolean',
    ];

	/**
	 * Relationships
	 */

	/**
     * Get the client database server type for the database server.
     */
    public function type(): BelongsTo
    {
        return $this->belongsTo(DBServerType::class, 'client_db_server_type_id', 'id');
    }

	/**
     * Get the remote server for the database server.
     */
	public function remoteServer(): BelongsTo
    {
        return $this->belongsTo(RemoteServer::class);
    }

	/**
     * Get the remote server for the database server.
     */
	public function remoteServerFilter(): BelongsTo
    {
        return $this->belongsTo(RemoteServerFilter::class, 'remote_server_status_id', 'id');
    }

	/**
	 * Get all databases list associated with the client db server
	 */
	public function clientDBSchemas()
	{
		return $this->belongsTo(ClientDBSchema::class, 'client_db_server_id', 'id');
	}
}
