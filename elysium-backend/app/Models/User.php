<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use <PERSON><PERSON>\Cashier\Billable;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes, Billable;

	/**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'password',
		'company_id'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

	/**
	 * Always encrypt the password when it is updated.
	 *
	 * @param $value
	 * @return string
	*/
	public function setPasswordAttribute($value)
	{
		$this->attributes['password'] = bcrypt($value);
	}

	/**
	 * Relationships
	 */

	/**
	 * Get the company which owns the user
	*/
	public function company()
	{
		return $this->belongsTo(Company::class);
	}

	/**
	 * Get the timezone assicated with the user
	 */
	public function timezone()
	{
		return $this->belongsTo(Timezone::class, 'timezone_id');
	}

	/**
	 * Get all of the subscriptions for the Stripe model.
	 *
	 * @return \Illuminate\Database\Eloquent\Collection
	 */
	public function subscriptions()
	{
		return $this->hasMany(Subscription::class, $this->getForeignKey())->orderBy('created_at', 'desc');
	}
}
