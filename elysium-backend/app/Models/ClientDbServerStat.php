<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ClientDbServerStat extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'client_db_server_stat';
    protected $fillable = [
        'dim_date_id',
        'client_db_server_id',
        'company_id',
        'db_name',
        'stat_date',
        'total_db_size',
        'total_db_storage_setup',
        'total_db_rows_archived',
        'total_db_data_size_archived',
        'total_db_rows',
        'total_db_index',
        'total_db_storage_setup_cost',
        'total_table_storage_cost_saving',
        'is_deleted',
    ];

    public function client_db_server_data()
    {
        return $this->belongsTo(ClientDbServer::class, 'client_db_server_id', 'id');
    }
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereHas('dim_date', function ($query) use ($startDate, $endDate) {
            $query->whereBetween('date', [$startDate, $endDate]);
        });
    }
    public function dim_date()
    {
        return $this->belongsTo(DimDate::class,'dim_date_id','id');
    }
}
