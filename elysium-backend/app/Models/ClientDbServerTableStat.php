<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ClientDbServerTableStat extends Model
{
    use HasFactory, SoftDeletes;
    protected $table = 'client_db_server_table_stat';
    protected $fillable = [
        'client_db_server_id',
        'dim_date_id',
        'company_id',
        'client_db_server_table_id',
        'stat_date',
        'total_table_rows',
        'total_table_rows_archived',
        'total_db_storage_setup',
        'total_table_size',
        'total_table_index',
        'total_db_storage_setup_cost',
        'total_table_data_size_archived',
        'total_table_object_storage_used',
        'total_table_storage_cost_saving',
        'is_deleted',
        'client_database_id'
    ];
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereHas('dim_date', function ($query) use ($startDate, $endDate)
        {
            $query->whereBetween('date', [$startDate, $endDate]);
        });
    }
    public function dim_date()
    {
        return $this->belongsTo(DimDate::class, 'dim_date_id', 'id');
    }
}
