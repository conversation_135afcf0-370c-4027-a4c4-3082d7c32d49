<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ObjectStorageExportHistory extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'object_storage_export_history';

    protected $fillable = [
        'client_db_server_table_id',
        'object_storage_type_id',
        'table_name',
        'file_exported_name',
        'is_file_compressed',
        'file_export_size',
        'file_stored_size',
        'file_compression_ratio',
        'file_compression_percentage',
        'total_row_exported',
        'file_export_started_at',
        'file_export_ended_at',
        'export_status',
        'last_exported_id',
        'is_export_error',
        'error_message',
        'is_current',
        'is_deleted',
        'created_at'
    ];
    public function activities()
    {
        return $this->hasMany(ObjectStorageExportHistoryActivity::class);
    }
}
