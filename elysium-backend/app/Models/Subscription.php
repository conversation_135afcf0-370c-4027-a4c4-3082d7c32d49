<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Laravel\Cashier\Subscription as CashierSubscription;
use Illuminate\Database\Eloquent\Model;

class Subscription extends CashierSubscription
{
    use HasFactory;

	/**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'subscription';

	/**
	 * Relationships
	 */

	/**
	 * Get the company that owns subscription
	 */
	public function user()
	{
		return $this->belongsTo(User::class);
	}

	/**
	 * Get the subscription items related to the subscription
	 */
	public function items()
    {
        return $this->hasMany(SubscriptionItem::class);
    }
}
