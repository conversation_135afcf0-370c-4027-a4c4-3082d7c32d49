<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ScheduleProcessingList extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'table_job_schedule';
    protected $fillable = [
        'client_db_server_table_id',
        'agent_uuid',
        'database_host',
        'database_name',
        'database_port',
        'schema_name',
        'table_name',
        'retention_days',
        'object_backup_location',
        'is_partitioned',
        'batch_size',
        'is_active',
        'is_current',
        'archive_day_of_week',
        'archive_untill_date_utc',
        'archive_start_at_utc',
        'local_time_zone',
        's3_file',
        'total_rows',
        'schedule_type',
        'retention_index',
        'database_job_schedule_id'
    ];

    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function process_schedule(){
        return $this->hasMany(ProcessSchedule::class ,'table_job_schedule_id','id');
    }
    public function tables_data(){
        return $this->belongsTo(ClientDBServerTable::class ,'client_db_server_table_id','id');
    }
    public function db_job_schedule(){
        return $this->belongsTo(DBJobSchedule::class ,'database_job_schedule_id');
    }
}
