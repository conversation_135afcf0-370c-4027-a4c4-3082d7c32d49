<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Model;

class ObjectStorageType extends Model
{
    use HasFactory;

	/**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'object_storage_type';

	/**
	 * The attributes that are mass assignable
	 *
	 * @var array
	 */
	protected $fillable = [
		'object_storage_type',
		'cloud_provider_name'
	];

	/**
	 * Relationships
	 */

	/**
     * Get the client object storage instances associated with the object storage type.
     */
    public function clientObjectStorage(): BelongsTo
    {
        return $this->belongsTo(ClientObjectStorage::class, 'object_storage_type_id', 'id');
    }
}
