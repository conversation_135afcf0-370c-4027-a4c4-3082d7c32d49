<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class ClientDBServerTable extends Model
{
	use HasFactory, SoftDeletes;

	protected $table = 'client_db_server_table';


	/**
	 * The attributes that are mass-assignable
	 * @var array
	 */
	protected $fillable = [
		'client_db_schema_id',
		'client_db_server_table_uuid',
		'table_schema_name',
		'table_database_name',
		'table_name',
		'table_type',
		'engine',
		'version',
		'row_format',
		'total_current_table_rows',
		'total_current_data_length',
		'avg_row_length',
		'max_data_length',
		'data_free',
		'check_time',
		'table_collation',
		'checksum',
		'create_options',
		'table_comment',
		'timezone',
		'client_database_id',
		'client_db_server_id',
		'table_process_status_id',
		'has_reference_integrity',
		'is_dropped'
	];

	/**
	 * Relationships
	 */

	/**
	 * Get the database information associated with the database table
	 */
	public function client_db_schema()
	{
		return $this->belongsTo(ClientDBSchema::class, 'client_db_schema_id', 'id');
	}

	/**
	 * Get the client db table columns associated with the client db table
	 */
	public function columns()
	{
		return $this->hasMany(ObjectStorageExportHistory::class, 'client_db_server_table_id', 'id');
	}

	public function export_history()
	{
		return $this->hasMany(ObjectStorageExportHistory::class, 'client_db_server_table_id', 'id');
	}

	/**
	 * Get the database information associated with the db schema table
	 */
	public function clientDatabase()
	{
		return $this->belongsTo(ClientDatabase::class, 'client_database_id', 'id');
	}

	/**
	 * Get the client db server information associated with the db schema table
	 */
	public function clientDBServer()
	{
		return $this->belongsTo(DBServer::class, 'client_db_server_id', 'id');
	}

	/**
	 * Get the client db server information associated with the db schema table
	 */
	public function schedule_process_list()
	{
		return $this->hasOne(ScheduleProcessingList::class, 'client_db_server_table_id', 'id');
	}

	/**
	 * Get the table process status to which tables are associated
	 */
	public function tableProcessStatus()
	{
		return $this->belongsTo(TableProcessStatus::class, 'table_process_status_id', 'id');
	}

	public function table_archiving_stats()
	{
		return $this->hasMany(ClientDbServerTableStat::class, 'client_db_server_table_id', 'id');
	}
}
