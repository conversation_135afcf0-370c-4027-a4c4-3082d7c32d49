<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class SubscriptionType extends Model
{
    use HasFactory, SoftDeletes;

	/**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'subscription_type';


	/**
	 * The attributes that are assignable
	 *
	 * @var array
	 */
	protected $fillable = ['name'];

	/**
	 * Relationships
	 */

	/**
	 * Get subscription plans for the subscription type
	 */
	public function plans(): HasMany
    {
        return $this->hasMany(SubscriptionPlan::class);
    }
}
