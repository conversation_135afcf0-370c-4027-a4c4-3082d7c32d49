<?php

namespace App\Traits;
use Illuminate\Support\Facades\Storage;

trait FileUploader
{
	/**
	 * Upload images or files
	 */
	public function uploadFile($directoryName = 'files', $file)
    {
        try {
			$fileName = NULL;

            if ($file) {
				$fileName = time(). '.' . $file->extension();
                Storage::putFileAs('public/'.$directoryName, $file, $fileName);
            }

            return $fileName;
        } catch (\Throwable $th) {
            return $th->getMessage();
        }
    }

    /**
	 * Delete files or images
	 */
    public function deleteFile($directoryName = 'files', $fileName)
    {
        try {
            if ($fileName) {
                Storage::delete('public/'. $directoryName .'/'. $fileName);
            }

            return NULL;
        } catch (\Throwable $th) {
            return $th->getMessage();
        }
    }
}