<?php

namespace App\Traits;

use phpseclib3\Net\SSH2;
use Illuminate\Support\Facades\Log;
use phpseclib3\Crypt\PublicKeyLoader;
use Illuminate\Support\Facades\Storage;

trait VerifyRemoteServerConnection
{
	/**
	 * Verify remote server credentials and return response
	 * @param string $hostname
	 * @param string $username
	 * @param int $port
	 * @param bool $isTlsRequired
	 * @param array &$logs Optional array to collect connection logs
	 * @return array
	 */
	public static function connect($hostname, $username, $port, $isTlsRequired, &$logs = [])
	{
		try {
			if (is_array($logs)) {
				$logs[] = "Creating SSH2 instance for {$hostname}:{$port}...";
				
				// Add network diagnostics
				$logs[] = "Running network diagnostics...";
				
				// Check if the hostname resolves
				$ip = gethostbyname($hostname);
				if ($ip === $hostname) {
					$logs[] = "WARNING: Could not resolve hostname '{$hostname}' to an IP address.";
				} else {
					$logs[] = "Hostname '{$hostname}' resolves to IP: {$ip}";
				}
				
				// Test if the port is reachable with a timeout
				$logs[] = "Testing TCP connection to {$hostname}:{$port}...";
				$socket = @fsockopen($hostname, $port, $errno, $errstr, 5);
				if (!$socket) {
					$logs[] = "ERROR: Cannot connect to {$hostname}:{$port}. Error {$errno}. {$errstr}";
					$logs[] = "Possible causes:";
					$logs[] = "1. The server is not reachable (network issue or server is down)";
					$logs[] = "2. A firewall is blocking the connection";
					$logs[] = "3. The SSH service is not running on the specified port";
					$logs[] = "4. The hostname might be incorrect";
					
					// Suggest troubleshooting steps
					$logs[] = "Troubleshooting steps:";
					$logs[] = "1. Verify the server is online and reachable";
					$logs[] = "2. Check if SSH is running on port {$port}";
					$logs[] = "3. Verify firewall rules allow connections from this server";
					$logs[] = "4. Try connecting with 'ssh -v {$username}@{$hostname} -p {$port}' from command line";
					
					return ['status' => false, 'osType' => null, 'error' => "Connection timed out"];
				} else {
					$logs[] = "TCP connection successful to {$hostname}:{$port}";
					fclose($socket);
				}
			}
			
			// Create an SSH2 instance with a shorter timeout
			$ssh = new SSH2($hostname, $port, 10); // 10 second timeout
			
			if (is_array($logs)) {
				$logs[] = "Loading private key from public/ssh_keys/id_rsa...";
			}

			// Authenticate with the private key
			$path = public_path('ssh_keys/id_rsa');
			$key = PublicKeyLoader::load(file_get_contents($path));

			if (is_array($logs)) {
				$logs[] = "Attempting login with username: {$username}...";
			}

			if (!$ssh->login($username, $key)) {
				if (is_array($logs)) {
					$logs[] = "Login failed! Authentication rejected.";
				}
				
				Log::info("Login failed for the hostname $hostname");
				return $response = [ 'status' => false, 'osType' => null];
			} else {
				if (is_array($logs)) {
					$logs[] = "Login successful! Authenticated to {$hostname}.";
					$logs[] = "Executing command: lsb_release -i -s";
				}
				
				Log::info("Login Success for the hostname $hostname");
				$osType = $ssh->exec('lsb_release -i -s');
				
				if (is_array($logs)) {
					$logs[] = "OS detected: " . (trim($osType) ?: "Unknown");
					$logs[] = "Closing SSH connection...";
				}

				// Close the SSH connection
				$ssh->disconnect();
				return $response = [ 'status' => true, 'osType' => trim($osType)];
			}
		} catch (\Throwable $th) {
			if (is_array($logs)) {
				$logs[] = "Error occurred: {$th->getMessage()}";
			}
			
			Log::error($th->getMessage());
			if (isset($ssh)) {
				$ssh->disconnect();
			}
			Log::error("An error occurred during SSH login. Please try again later for the hostname $hostname");
			return $response = [ 'status' => false, 'osType' => null];
		}
	}
}
