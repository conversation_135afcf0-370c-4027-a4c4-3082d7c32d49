<?php

namespace App\Traits;

use Carbon\Carbon;

trait ClubData
{
    /**
     *  Barchart data formation
     */
    public function clubDataWithFlag($formattedDates, $formattedData, $flag)
    {
        try
        {
            $uniqueDates = [];
            foreach ($formattedDates['dates'] as $date)
            {

                if ($flag == 'h')
                {
                    $dateTime = date('Y-m-d H', strtotime($date));
                }
                elseif ($flag == 'd')
                {
                    $dateTime = date('Y-m-d', strtotime($date));
                }
                elseif ($flag == 'm')
                {
                    $dateTime = date('Y-m', strtotime($date));
                }
                elseif ($flag == 'y')
                {
                    $dateTime = date('Y', strtotime($date));
                }
                elseif ($flag == 'a')
                {
                    $dateTime = date('Y', strtotime($date));
                }

                if (!in_array($dateTime, $uniqueDates))
                {
                    $uniqueDates[] = $dateTime;
                }
            }
            $formattedDates['dates'] = $uniqueDates;

            $uniqueFormattedData = [];
            foreach ($formattedData as $data)
            {
                $uniqueFormattedData[] = [
                    'name' => $data['name'],
                    'color_code' => $data['color_code']
                ];

                $sum = 0;
                foreach ($uniqueDates as $uniqueDate)
                {
                    foreach ($data['data'] as $key => $data_d)
                    {
                        if ($flag == 'h' && $uniqueDate == date('Y-m-d H', strtotime($key)))
                        {
                            $sum += $data_d;
                        }
                        elseif ($flag == 'd' && $uniqueDate == date('Y-m-d', strtotime($key)))
                        {
                            $sum += $data_d;
                        }
                        elseif ($flag == 'm' && $uniqueDate == date('Y-m', strtotime($key)))
                        {
                            $sum += $data_d;
                        }
                        elseif ($flag == 'y' && $uniqueDate == date('Y', strtotime($key)))
                        {
                            $sum += $data_d;
                        }
                        elseif ($flag == 'a' && $uniqueDate == date('Y', strtotime($key)))
                        {
                            $sum += $data_d;
                        }
                    }
                    $uniqueFormattedData[count($uniqueFormattedData) - 1]['data'][$uniqueDate] = $sum;
                    $sum = 0;
                }
            }

            $formattedData = $uniqueFormattedData;

            return [$formattedDates, $formattedData];
        }
        catch (\Throwable $th)
        {
            return $th->getMessage();
        }
    }

    /**
     *  Get all dates between startDate and endDate
     */
    public function getDatesBetween($startDate, $endDate)
    {
        $dates = [];

        $currentDate = Carbon::parse($startDate);

        while ($currentDate->lte(Carbon::parse($endDate)))
        {
            $dates[] = date('Y-m-d H:m:i', strtotime($currentDate));
            $currentDate->addDay();
        }

        return $dates;
    }
}
