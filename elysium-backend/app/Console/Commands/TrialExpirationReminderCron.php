<?php

namespace App\Console\Commands;

use App\Mail\trialExpirationEmail;
use Illuminate\Support\Facades\Log;
use Illuminate\Console\Command;
use App\Models\Subscription;
use App\Models\User;
use Carbon\Carbon;


class TrialExpirationReminderCron extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'trial-expiration-reminder-email:cron';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send trial expiration emails on daily basis';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {

			$trialUsers = User::select('first_name', 'last_name', 'email', 'trial_ends_at')
							->where('is_active', 0)->whereNotNull('trial_ends_at')
							->whereDate('trial_ends_at', '>', Carbon::now())
							->get();

			if($trialUsers) {
				foreach($trialUsers as $user) {
					if($user) {
						$trialUsers[] = [
							'fullName' => $user->first_name. ' ' .$user->last_name,
							'subscriptionEndDate' => $user->trial_ends_at,
							'email' => $user->email,
						];
					}
				}
			}

			/**
			 * Send email notification to each individual user with trial subscription
			 */
			if($trialUsers) {
				foreach($trialUsers as $trialUser) {
					\Mail::to($trialUser['email'])->send(new trialExpirationEmail($trialUser));
				}
			}

			Log::info('Trial expiration emails sent successfully');
		} catch (\Throwable $th) {

			Log::error('Something went wrong while sending trial expiration email notifications '.$th);
		}
    }
}
