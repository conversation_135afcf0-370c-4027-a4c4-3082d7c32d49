<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Routing\Route;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\API\ProcessScheduleController;

class ScheduleProcessing extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'schedule-processing';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To process the schedule for archiving/export';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try
        {
            $start_time=Carbon::now();
            $shedules = new ProcessScheduleController();
            $shedules->getSchedule();
            $end_time=Carbon::now();
            Log::info("schedule-processing function call time:$start_time");
            Log::info("schedule-processing function end time: $end_time");
            
        }
        catch (\Throwable $th)
        {
            
        \Illuminate\Support\Facades\Log::info("schedule-processing function error: $th");
        }
    }
}
