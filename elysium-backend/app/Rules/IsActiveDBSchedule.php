<?php

namespace App\Rules;

use App\Models\DBJobSchedule;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class IsActiveDBSchedule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $dbSchedule = DBJobSchedule::with(['table_job_schedule'])
            ->where('client_database_id', $value)
            ->whereNull('deleted_at')
            ->first();
        if ($dbSchedule)
        {
            foreach ($dbSchedule->table_job_schedule as $schedule)
            {
                if ($schedule->is_active == 1)
                {
                    $fail('The selected database must be all inactive schedules.');
                }
            }
        }
    }
}
