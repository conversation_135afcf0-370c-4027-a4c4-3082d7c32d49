<?php

namespace App\Rules;

use Closure;
use App\Models\ScheduleProcessingList;
use Illuminate\Contracts\Validation\ValidationRule;

class IsActiveSchedule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $schedule = ScheduleProcessingList::where('id', $value)
            ->whereNull('deleted_at')
            ->where('is_active', 1)
            ->first();
        if ($schedule)
        {
            $fail('The selected schedule must be inactive.');
        }
    }
}
