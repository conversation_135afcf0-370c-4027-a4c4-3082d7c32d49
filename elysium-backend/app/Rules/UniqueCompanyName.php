<?php

namespace App\Rules;

use Auth;
use Closure;
use App\Models\Company;
use Illuminate\Contracts\Validation\ValidationRule;

class UniqueCompanyName implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
		$userCompanyId = Auth::user()->company_id;

		$company = Company::where('company_name', $value)->where('id', '!=', $userCompanyId)->first();

        if ($company) {
            $fail('The :attribute is already registered.');
        }
    }
}
