@if($data)
	Dear {{ $data['full_name'] }},
	<br><br>

	We are writing to inform you that an agent failure has occurred in your Elysium database archiving system.
	<br><br>

	<strong>Failure Details:</strong>
	<br>
	<ul>
		<li><strong>Log Type:</strong> {{ $data['log_type'] ?? 'N/A' }}</li>
		<li><strong>Database:</strong> {{ $data['database_name'] ?? 'N/A' }}</li>
		<li><strong>Table:</strong> {{ $data['table_name'] ?? 'N/A' }}</li>
		<li><strong>Timestamp:</strong> {{ now()->format('Y-m-d H:i:s') }} UTC</li>
	</ul>
	<br>

	@if(isset($data['log_message']))
		<strong>Error Message:</strong>
		<br>
		<div style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #dc3545; margin: 10px 0;">
			{{ $data['log_message'] }}
		</div>
		<br>
	@endif

	<strong>Recommended Actions:</strong>
	<br>
	<ul>
		<li>Check your database server connectivity</li>
		<li>Verify your archiving agent is running properly</li>
		<li>Review the system logs for additional details</li>
		<li>Contact support if the issue persists</li>
	</ul>
	<br>

	Please log into your Elysium dashboard to review the full details and take appropriate action to resolve this issue.
	<br><br>

	If you need assistance, please contact our support team at: {{ config('app.support_email', '<EMAIL>') }}.
	<br><br>

	Best regards,
	<br><br>
	The Elysium Team
@else
	Dear User,
	<br><br>

	An agent failure notification was triggered, but the failure details are not available.
	<br><br>

	Please log into your Elysium dashboard to check the system status and contact support if needed.
	<br><br>

	Support contact: {{ config('app.support_email', '<EMAIL>') }}
	<br><br>

	Best regards,
	<br><br>
	The Elysium Team
@endif
