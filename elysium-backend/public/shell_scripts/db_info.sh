#!/bin/bash

# Source the file with environment variables
source ./mysql_config.txt

# REMOTE_DB_HOST="localhost"
# REMOTE_DB_USER="root"
# REMOTE_DB_PASSWORD="root"

#WEBHOOK_URL="https://elysium.au.ngrok.io/api/getDBResponse"

# Function to send POST request to webhook URL
send_post_request() {
    start_time=$(date +"%Y-%m-%d %H:%M:%S")
  local url="$1"
  local data="$2"

  echo "Curl command to send data to webhook URL:"
  echo "-v -X POST -H "Content-Type: application/json" -H "Authorization: Bearer $DB_SERVER_UUID" --data-binary "@json_payload.json" "$url"" 2>&1 | while read line; do echo "$(date '+%Y-%m-%d %H:%M:%S') $line" >> webhook.log; done
  curl -v -X POST -H "Content-Type: application/json" -H "Authorization: Bearer $DB_SERVER_UUID" --data-binary "@json_payload.json" "$url" 2>&1 | while read line; do echo "$(date '+%Y-%m-%d %H:%M:%S') $line" >> webhook.log; done
  
  echo "Response from Laravel"

  end_time=$(date +"%Y-%m-%d %H:%M:%S")
    start_seconds=$(date -d "$start_time" +%s)
    end_seconds=$(date -d "$end_time" +%s)
    execution_time=$((end_seconds - start_seconds))
    echo "Total send_post_request execution time: $execution_time seconds"

}

start_time=$(date +"%Y-%m-%d %H:%M:%S")
# Connect to remote server and fetch list of all databases
database_data=$(mysql -h $REMOTE_DB_HOST -u $REMOTE_DB_USER -p"$REMOTE_DB_PASSWORD" -e "SELECT TABLE_SCHEMA AS \"database\",
                    Engine, Sum(table_rows) AS \"Total Rows\",
                    Sum(data_length + index_length) AS \"Total DB\",
                    Sum(data_length) AS \"Data\",
                    Sum(index_length) AS \"Index\"
                    FROM information_schema.TABLES
                    WHERE table_schema NOT IN ('sys', 'mysql', 'information_schema', 'performance_schema') AND engine IS NOT NULL
                    GROUP BY 1,2;" | tail -n +2)

echo $database_data > databases.txt

# Initialize the final JSON array
final_json="["

# Loop through each database
while read -r line; do
    IFS=$'\t' read -r -a database_data <<< "$line"
    TARGET_DATABASE="${database_data[0]}"
    engine="${database_data[1]}"
    total_rows="${database_data[2]}"
    total_db="${database_data[3]}"
    data="${database_data[4]}"
    index="${database_data[5]}"

    # Fetch table data for the current database
    table_data=$(mysql -h $REMOTE_DB_HOST -u $REMOTE_DB_USER -p"$REMOTE_DB_PASSWORD" $TARGET_DATABASE -e "SELECT TABLE_NAME AS \`Table\`,
                TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT,
                TABLE_ROWS, DATA_LENGTH, INDEX_LENGTH, AVG_ROW_LENGTH, MAX_DATA_LENGTH, DATA_FREE, AUTO_INCREMENT,
                CREATE_TIME, UPDATE_TIME, CHECK_TIME,
                TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT,
                TABLE_SCHEMA
                FROM information_schema.TABLES
                WHERE TABLE_SCHEMA = '$TARGET_DATABASE';" | tail -n +2)
    echo $table_data > ${TARGET_DATABASE}-tables.txt

    # Convert table data to JSON for the current database
    db_json="{\"database\": \"$TARGET_DATABASE\",
              \"engine\": \"$engine\",
              \"total_rows\": \"$total_rows\",
              \"total_db\": \"$total_db\",
              \"data\": \"$data\",
              \"index\": \"$index\",
              \"table_data\": ["
    while read -r line; do
        IFS=$'\t' read -r -a table_data <<< "$line"
        table_name="${table_data[0]}"

        # Fetch column data for the current table
        column_data=$(mysql -h $REMOTE_DB_HOST -u $REMOTE_DB_USER -p"$REMOTE_DB_PASSWORD" $TARGET_DATABASE -e "SELECT COLUMN_NAME, ORDINAL_POSITION, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, COLUMN_TYPE, COLUMN_KEY, COLUMN_COMMENT
                    FROM information_schema.COLUMNS
                    WHERE TABLE_SCHEMA = '$TARGET_DATABASE' AND TABLE_NAME = '$table_name';" | tail -n +2)

        # Convert column data to JSON for the current table
        columns_json="["
        while read -r column_line; do
            IFS=$'\t' read -r -a column_data <<< "$column_line"
            column_name="${column_data[0]}"
            ordinal_position="${column_data[1]}"
            is_nullable="${column_data[2]}"
            data_type="${column_data[3]}"
            character_max_length="${column_data[4]}"
            column_type="${column_data[5]}"
            column_key="${column_data[6]}"
            column_comment="${column_data[7]}"

            column_json="{\"column_name\": \"$column_name\",
                        \"ordinal_position\": \"$ordinal_position\",
                        \"is_nullable\": \"$is_nullable\",
                        \"data_type\": \"$data_type\",
                        \"character_max_length\": \"$character_max_length\",
                        \"column_type\": \"$column_type\",
                        \"column_key\": \"$column_key\",
                        \"column_comment\": \"$column_comment\"}"

            columns_json="$columns_json$column_json,"
        done <<< "$column_data"
        columns_json="${columns_json%,}"  # Remove the trailing comma
        columns_json="$columns_json]"

        table_json="{\"table_name\": \"$table_name\",
                    \"table_type\": \"${table_data[1]}\",
                    \"engine\": \"${table_data[2]}\",
                    \"version\": \"${table_data[3]}\",
                    \"row_format\": \"${table_data[4]}\",
                    \"total_current_table_rows\": \"${table_data[5]}\",
                    \"total_current_data_length\": \"${table_data[6]}\",
                    \"total_current_index_length\": \"${table_data[7]}\",
                    \"avg_row_length\": \"${table_data[8]}\",
                    \"max_data_length\": \"${table_data[9]}\",
                    \"data_free\": \"${table_data[10]}\",
                    \"auto_increment\": \"${table_data[11]}\",
                    \"create_time\": \"${table_data[12]}\",
                    \"update_time\": \"${table_data[13]}\",
                    \"check_time\": \"${table_data[14]}\",
                    \"table_collation\": \"${table_data[15]}\",
                    \"checksum\": \"${table_data[16]}\",
                    \"create_options\": \"NULL\",
                    \"table_comment\": \"NULL\",
                    \"columns\": $columns_json}"
        db_json="$db_json$table_json,"
    done <<< "$table_data"
    db_json="${db_json%,}"  # Remove the trailing comma
    db_json="$db_json]},"

    # remove trailing comma and save json output into json file
    echo "${db_json%,}" > ${TARGET_DATABASE}-tables.json

    # Append the current database JSON to the final JSON array
    final_json="$final_json$db_json"
done <<< "$database_data"

# Remove the trailing comma and close the final JSON array
final_json="${final_json%,}]"

# Save the final JSON data to a file (e.g., output.json)
echo "$final_json" > json_payload.json

end_time=$(date +"%Y-%m-%d %H:%M:%S")
start_seconds=$(date -d "$start_time" +%s)
end_seconds=$(date -d "$end_time" +%s)
execution_time=$((end_seconds - start_seconds))
echo "Total final_json execution time: $execution_time seconds"
# Display the final JSON data
#echo "$final_json"

# Send the response back to the webhook URL
send_post_request "$WEBHOOK_URL" "$final_json"