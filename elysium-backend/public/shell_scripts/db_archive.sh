#!/bin/bash
# --------------------------------------------------------------------------------
# Copyright (c) 2023 Elysium-IO, Inc
#
# This script is proprietary software and is protected under copyright law.
# Redistribution or modification of this software without explicit permission
# from Elysium-IO, Inc. is strictly prohibited.
#
# Title: Database Archiving Script
# Description: This script automates the process of archiving databases.
# Version: 1.1
# Author: <PERSON><PERSON>
# Created on: 11/23/2023
# Modified on: Current Date
# --------------------------------------------------------------------------------
# Overview
# Archive a particular table with direct parameters:
# ./db_archive.sh -h [host] -d [database] -t [table] -u [username] -p [password] -r [retention_days] -b [batch_size] -s [s3_location] -i [is_partitioned]

# Script run conditions
## -e is fast fail on first error
## -u is error on unset variables
## -E is to pass trap handling to functions
set -Eeu

# Constants
source /home/<USER>/agent/agent_config.txt

# Variables to be set from command line
database_host=""
database_schema=""
login_path=""
table_name=""
db_username=""
db_password=""
retention_in_days=60
batch_size=10000
s3_bucket=""
s3_subdir=""
s3_location=""
is_partitioned=0
is_active=1
attr_arg="created_at"
unique_key="id"
nodelete=false
last_max_id=""
export_type="export"
id=""
enckey=""
child_tables=""
parent_column_name=""

# Functions
log() {
    # Print string passed with a timestamp prefix
    local level="${1}"
    local message="${2}"

    # Argument to date
    ## %F is full date: 2022-08-04
    ## %T is time:     12:03:43
    ## %Z is timezone: UTC
    local timestamp=$(date +"%F %T %Z")

    if [[ $# != 2 ]]; then
        log ERROR "log function requires 2 arguments, got: $#"| tee -a agent.log
        exit 1
    fi

    # printf is a bash built in
    if [[ -n "${table_id:-}" ]]; then
        printf '%s: %s: %s: %s\n' "${timestamp}" "${level}" "${table_id}" "${message}" | tee -a agent.log
    else
        printf '%s: %s: %s\n' "${timestamp}" "${level}" "${message}" | tee -a agent.log
    fi
}

usage() {
    printf '%s' "Usage: $0 -l [login_path] -h [host] -d [database] -t [table] -r [retention_days] -b [batch_size] -s [s3_bucket] -sd [s3_subdir] -i [is_partitioned] -k [unique_key] "  1>&2
    exit 1
}

mysql_cmd() {
    local host="${1}"
    local query="${2}"

    if [[ $# != 2 ]]; then
        log ERROR "mysql_cmd function requires 2 arguments, got: $#"
        exit 1
    fi

    # log INFO " QUERY __>> --login-path="${login_path}" --skip-column-names --batch <<<${query} "
    mysql \
        --login-path="${login_path}" \
        --skip-column-names \
        --batch <<<"${query}"
}

fail_mail() {
    local body="${1}"
    local subject="Purge Job: FAILURE: Host='${database_host:-}' Schema='${database_schema:-}' Table='${table_name:-}'"

    if [[ $# != 1 ]]; then
		log ERROR "fail_mail function requires 1 arguments, got: $#"
		exit 1
	fi

    printf '%s\n\nLog File: %s\nTable ID: %s' "${body}" "${log_file:-}" "${table_id:-}" \

    #post to /api/agentFailure 
    response=$(curl \
        --silent \
        -X POST \
        -H 'Content-type: application/json' \
        --data '{"table_job_schedule_execution_log_id": "'"${id}"'", "log_message": "'"${body}"'", "log_type": "ERROR", "text": "*'"${subject}*"'\n```\n'"${body}"'\n```\nLog File: `'"${log_file:-}"'`\nTable ID: `'"${table_id:-}"'`"}' \
        "${WEBHOOK_URL}/api/agentfailure" 2>&1)  

    log INFO "Reponse: ${response}"
  
        
}

unhandled_err() {
    if [[ $? != 0 ]]; then
        printf "%s\n" "$*" >&2
        fail_mail "Unhandled error in: ${0}
    Line:  ${BASH_COMMAND}"
    fi
}

cap_threads() {
    local dir=${1}
    local limit=${2}

    local threads=$(ls "${dir}" | wc -l | tr -d ' ')

    [[ ${threads} -ge ${limit} ]]
}

send_post_request() {
  local start_time=$(date +"%Y-%m-%d %H:%M:%S")
  local url="$1"
  local json_data="$2"

  log INFO "Posting request to Laravel API at ${url} ${json_data}"

  # Validate required parameters
  if [ -z "$url" ] || [ -z "$json_data" ]; then
    log ERROR "Missing URL or JSON data parameters"
    return 1
  fi

#   log INFO "curl --silent --show-error --fail \
#     --location --request POST $url/api/schedule_response \
#     --header 'Content-Type: application/json' \
#     --header 'Accept: application/json' \
#     --data-raw $$json_data"

  # Make the API request with better error handling
  response=$(curl --silent --show-error --fail \
    --location --request POST "${url}/api/schedule_response" \
    --header 'Content-Type: application/json' \
    --header 'Accept: application/json' \
    --data-raw "$json_data" 2>&1)

  log INFO "Reponse: ${response}"
  
  local curl_exit_code=$?

  if [ $curl_exit_code -ne 0 ]; then
    log ERROR "API request failed with exit code $curl_exit_code"
    log ERROR "Response: $response"
    return 1
  fi

  log INFO "API request completed successfully"
  log DEBUG "API data: $json_data"
  log DEBUG "API Response: $response"

  # Return the response for further processing if needed
  echo "$response"
}
# Traps
trap unhandled_err EXIT

# Script arguments
while getopts "h:d:t:u:p:l:r:b:s:v:i:c:na:k:m:e:I:K:C:P" opt; do
    case $opt in
    h) database_host=$OPTARG ;;
    d) database_schema=$OPTARG ;;
    l) login_path=$OPTARG ;;
    t) table_name=$OPTARG ;;
    # u) db_username=$OPTARG ;;
    # p) db_password=$OPTARG ;;
    r) retention_in_days=$OPTARG ;;
    b) batch_size=$OPTARG ;;
    s) s3_bucket=$OPTARG ;;
    v) s3_subdir=$OPTARG ;;
    i) is_partitioned=$OPTARG ;;
    c) attr_arg=$OPTARG ;;
    n) nodelete=true ;;
    a) is_active=$OPTARG ;;
    k) unique_key=$OPTARG ;;  # New option for unique key
    m) last_max_id=$OPTARG ;;  
    e) export_type=$OPTARG ;;  
    I) id=$OPTARG ;;
    K) enckey=$OPTARG ;;
    C) child_tables=$OPTARG ;;
    P) parent_column_name=$OPTARG ;;    
    ?) usage ;;
    esac
done

log INFO "Starting Archive Run"

log INFO "Threads: ${ARCHIVE_THREAD_LIMIT}"
# Detect if output redirection/logging is used
if [[ ! -t 1 ]]; then
    log_file=$(lsof -p$$ -a -d 1 -w -Fn | tail -n1 | sed 's/^n//')
    log INFO "Log File:        ${log_file}"
fi

# Verification
if [[ -z "${database_host}" ]]; then
    log ERROR "Database Host is not set"
    usage
fi

if [[ -z "${database_schema}" ]]; then
    log ERROR "Database Schema is not set"
    usage
fi

if [[ -z "${table_name}" ]]; then
    log ERROR "Table Name is not set"
    usage
fi


if [[ -z "${s3_bucket}" ]]; then
    log ERROR "s3_bucket not set"
    usage
fi


if [[ -z "${id}" ]]; then
    log ERROR "ID is not set"
    usage
fi

if [[ -z "${enckey}" ]]; then
    log ERROR "Encryption key is not set"
    usage
fi

# if [[ -z "${db_username}" ]]; then
#     log ERROR "Database Username is not set"
#     usage
# fi

# if [[ -z "${db_password}" ]]; then
#     log ERROR "Database Password is not set"
#     usage
# fi

log INFO "id : ${id}"
log INFO "enc_key : ${enckey}"
log INFO "Database Host:   ${database_host}"
log INFO "Database Schema: ${database_schema}"
log INFO "Database Table:  ${table_name}"
log INFO "ARCHIVE MODE: Table"
archive_mode="table"


# set s3_location to be s3_bucket and if s3_subdir exist append to s3_bucket path
if [[ -n "${s3_subdir}" ]]; then
    s3_location="${s3_bucket}/${s3_subdir}"
else
    s3_location="${s3_bucket}"
fi

# Method
log INFO "Using provided archive information"
log INFO "Processing table information:
    Host:        ${database_host}
    Schema:      ${database_schema}
    Table Name:  ${table_name}
    Retention:   ${retention_in_days} days
    Active:      ${is_active}
    Partitioned: ${is_partitioned}
    Batch size:  ${batch_size} rows
    S3 Location: ${s3_location}"

# Create a single-row dataset with the provided parameters
mysql_results_archival_info="${database_host}	${database_schema}	${table_name}	${retention_in_days}	${is_active}	${batch_size}	${s3_location}	${is_partitioned}"

archival_info_row_count=1
archival_threads_dir=$(mktemp -d)

current_max_id=""


export_and_sync() {

    
}

# if child_tables is provided, decode into array to loop through
if [[ -n "${child_tables}" ]]; then
    child IFS=',' read -ra child_tables_array <<< "$child_tables"
fi


log INFO "Number of tables for processing: ${archival_info_row_count}"
while IFS=$'\t' read database_host database_schema table_name retention_in_days is_active batch_size s3_location is_partitioned; do
    while cap_threads "${archival_threads_dir}" ${ARCHIVE_THREAD_LIMIT}; do
        sleep 2
    done
    row_count=$((${row_count:=0} + 1))
    (
        table_id=$(md5sum <<<"${database_host}.${database_schema}.${table_name}" | fold -w8 | head -n1)
        archive_thread_file="${archival_threads_dir}/${table_id}.thread"
        touch "${archive_thread_file}"
        log INFO "Processing row information:
        Row:         ${row_count}/${archival_info_row_count}
        Table ID:    ${table_id} 
        Host:        ${database_host}
        Schema:      ${database_schema}
        Table Name:  ${table_name}
        Retention:   ${retention_in_days} days
        Active:      ${is_active}
        Partitioned: ${is_partitioned}
        Batch size:  ${batch_size} rows
        S3 Location: ${s3_location}"

        

        max_date=$(date -u +"%Y-%m-%d" -d "-${retention_in_days} days")

        #if no child tables, then process parent table
        if [[ -z "${child_tables_array[*]}" ]]; then
            archive_sql_query="
            SELECT COUNT(1), ${last_max_id:-min(${unique_key})}, max(${unique_key})
            FROM   ${database_schema}.${table_name}
            WHERE  ${attr_arg} > '${max_date}'
            ${last_max_id:+"AND ${unique_key} >= ${last_max_id}"};
            "

            # export_and_sync calling for parent table
        fi

        #for child table override the archive_sql_query to be the parent column name and get the max of that column
        if [[ -n "${child_tables_array[*]}" ]]; then
            for child_table in "${child_tables_array[@]}"; do
                log INFO "Processing child table: ${child_table}"
                # parent_column_name=$(exec_query "SELECT parent_column_name FROM archive_table WHERE table_name='$child_table'")
                archive_sql_query="SELECT COUNT(1), MIN(${parent_column_name}), MAX(${parent_column_name}) 
                FROM ${database_schema}.${child_table} WHERE ${parent_column_name} > '${max_date}' ${last_max_id:+"AND ${parent_column_name} >= ${last_max_id}"};"

                # export_and_sync calling for child tables
            done           
        fi


        
        ## ARCHIVE LOGIC START ##

        ### TO BE ADDED
        #After first archive where id > max(id) from previous run of last archive

        log INFO "SQL Query for row count and ID range
                ${archive_sql_query}"

        log INFO "Executing SQL query for row count and ID range"
        log INFO "mysql_cmd '${database_host}' '${archive_sql_query}'"
        mysql_results=$(mysql_cmd "${database_host}" "${archive_sql_query}")
        log INFO "SQL Query executed successfully  ${mysql_results}"
        # A single line is returned and the mysql delimiter is `\t` so we can just cast these in via position
        total_db_rows=$(cut -d$'\t' -f1 <<<"${mysql_results}")
        start_id=$(cut -d$'\t' -f2 <<<"${mysql_results}")
        end_id=$(cut -d$'\t' -f3 <<<"${mysql_results}")

        current_max_id=${end_id}
        log INFO "Archive information:
                Total Rows: ${total_db_rows}
                Start ID:   ${start_id}
                End ID:     ${end_id}
                current_max_id: ${current_max_id}"

        # If the returned row count is 0 then we want to skip this database server as there is nothing to do
        if [[ "${total_db_rows}" = 0 ]]; then
            log INFO "No rows to archive. SKIPPING!"
            if ! $nodelete; then
                rm -f "${archive_thread_file}"
            fi
            exit
        fi

        # Checking if export is needed. Skipping if it already exists in s3
        s3_file_name="${table_name}_${max_date}_${start_id}_${end_id}.tsv"
        s3_file="s3://${s3_location}/${database_schema}/${table_name}/${s3_file_name}"
        s3_part_file_name="${s3_file_name}.part"
        s3_part_file="s3://${s3_location}/${database_schema}/${table_name}/${s3_part_file_name}"
        log INFO "Checking for TSV in s3"

        tsv_file="${ARCHIVE_DIR}/${database_host}.${database_schema}.${table_name}_${max_date}_${start_id}_${end_id}.tsv"

        if aws s3 ls "${s3_file}" | grep -qE "${s3_file_name}$" >/dev/null; then
            message="TSV has been already uploaded. SKIPPING! TSV: ${s3_file}"

            log WARN "${message}"            
            fail_mail "${message}"
            if ! $nodelete; then
                rm -f "${archive_thread_file}"
            fi

            # json_payload=$(jq -n \
            #     --arg id "$id" \
            #     --arg enc_key "$enckey" \
            #     --arg error_message "$message" \
            #     '{
            #         id: $id,
            #         enc_key: $enc_key,
            #         "export_status": "Stopped",
            #         "is_export_error": 1,
            #         "error_message":  $error_message            
            #     }')

            # send_post_request "$WEBHOOK_URL" "$json_payload"


            exit
        fi


        
        json_payload=$(jq -n \
        --arg id "$id" \
        --arg enc_key "$enckey" \
        --arg s3_file_name "$s3_file_name" \
        --arg s3_file "$s3_file" \
        '{
            id: $id,
            enc_key: $enc_key,
            export_status: "Started",
            is_export_error: 0,
            file_exported_name: $s3_file_name,
            object_storage_dir_name: $s3_file,
            file_export_size: 1
        }')
        
        
        send_post_request "$WEBHOOK_URL" "$json_payload"


        # Check if TSV exists and if it does then skip
        if [[ -f "${tsv_file}" ]]; then
            message="TSV already exists on disk, removing file: ${tsv_file}"

              if ! rm -f "${tsv_file}"; then
                message="Failed to remove local tsv file. SKIPPING! File: ${tsv_file}"            
                log WARN "${message}"
                fail_mail "${message}"
                if ! $nodelete; then
                    rm -f "${archive_thread_file}"
                fi

                json_payload=$(jq -n \
                    --arg id "$id" \
                    --arg enc_key "$enckey" \
                    --arg error_message "$message" \
                    '{
                        id: $id,
                        enc_key: $enc_key,
                        "export_status": "Stopped",
                        "is_export_error": 1,
                        "error_message":  $error_message            
                    }')

                send_post_request "$WEBHOOK_URL" "$json_payload"

                exit
            fi
        fi

        log INFO "Archiving data into: ${tsv_file}"
        current_batch_id_start=${start_id}
        current_batch_id_end=0
        archive_export_sql_displayed=false
        total_db_table_size_kb=0
        
        # Get average row length from database to estimate size
        avg_row_length_query="
        SELECT AVG_ROW_LENGTH 
        FROM information_schema.tables 
        WHERE TABLE_SCHEMA='${database_schema}' AND TABLE_NAME='${table_name}';"
        avg_row_length=$(mysql_cmd "${database_host}" "${avg_row_length_query}")
        log INFO "Average row length: ${avg_row_length} bytes"
        
        while true; do
            # Iterate collection range by `batch_size`
            current_batch_id_end=$((${current_batch_id_start} + ${batch_size}))
            # if `current_batch_id_end` is greater then `end_id` then set it to `end_id` as we do not want to go past this range for _this_ run
            if [[ ${current_batch_id_end} -gt ${end_id} ]]; then
                current_batch_id_end=${end_id}
            fi

            # Calculate batch size in rows
            batch_rows_count=$((${current_batch_id_end} - ${current_batch_id_start} + 1))
            # Estimate batch size in KB
            batch_size_kb=$(( (${batch_rows_count} * ${avg_row_length:-100}) / 1024 ))
            total_db_table_size_kb=$((${total_db_table_size_kb} + ${batch_size_kb}))
            
            archive_export_sql="
            SELECT *
            FROM   ${database_schema}.${table_name}
            WHERE  ${unique_key} BETWEEN ${current_batch_id_start} AND ${current_batch_id_end}
            AND    ${attr_arg} > '${max_date}';
            "
            # Display SQL for row export only once to not be noisy
            if [[ "${archive_export_sql_displayed}" = false ]]; then
                log INFO "SQL Query for row export
                        THIS IS ONLY DISPLAYED ONCE
                        ${archive_export_sql}"
                archive_export_sql_displayed=true
            fi
            log INFO "${archive_export_sql}"

            log INFO "Exporting rows between '${current_batch_id_start}' and '${current_batch_id_end}'"
            mysql_cmd "${database_host}" "${archive_export_sql}" >>"${tsv_file}"
            
            local_tsv_size_bytes=$(ls -l "${tsv_file}" | cut -d' ' -f5)

            # Calculate current TSV file size in KB after each batch
            current_tsv_size_kb=$(du -k "${tsv_file}" | cut -f1)
            log INFO "Current TSV file size: ${current_tsv_size_kb} KB"
            
            if [[ ${current_batch_id_end} -ge ${end_id} ]]; then
                break
            fi

            # As SQL BETWEEN is INCLUSIVE we need to add `1` or we will get duplicate rows
            current_batch_id_start=$((${current_batch_id_end} + 1))
        done
        
        # Get final sizes
        final_tsv_size_kb=$(du -k "${tsv_file}" | cut -f1)
        log INFO "Total data size in database: ${total_db_table_size_kb} KB"
        log INFO "Final TSV file size: ${final_tsv_size_kb} KB"
        
        # Store sizes for later use
        db_data_size_kb=${total_db_table_size_kb}
        tsv_file_size_kb=${final_tsv_size_kb}

        # Validate that local and database row count matches
        log INFO "Comparing local tsv row count to returned total of database rows"
        total_local_rows=$(wc -l "${tsv_file}" | cut -d" " -f1)

        # if the local rows and database rows do not match then we do not want to upload to s3 or delete rows
        if [[ "${total_local_rows}" != "${total_db_rows}" ]]; then
            message="Local and database row count doesn't match:
                    Local:    ${total_local_rows} rows
                    Database: ${total_db_rows} rows"
            log WARN "${message}"
            fail_mail "${message}"
            if ! $nodelete; then
                rm -f "${archive_thread_file}"
            fi
            json_payload=$(jq -n \
                --arg id "$id" \
                --arg enc_key "$enckey" \
                --arg error_message "$message" \
                '{
                    id: $id,
                    enc_key: $enc_key,
                    "export_status": "Stopped",
                    "is_export_error": 1,
                    "error_message":  $error_message            
                }')

            send_post_request "$WEBHOOK_URL" "$json_payload"
            exit
        fi

        # Compress the TSV file
        log INFO "Compressing TSV file to gzip format"
        gz_file="${tsv_file}.gz"
        if ! gzip -c "${tsv_file}" > "${gz_file}"; then
            message="Failed to compress TSV file to gzip format"
            log WARN "${message}"
            fail_mail "${message}"
            if ! $nodelete; then
                rm -f "${archive_thread_file}"
            fi
            json_payload=$(jq -n \
                --arg id "$id" \
                --arg enc_key "$enckey" \
                --arg error_message "$message" \
                '{
                    id: $id,
                    enc_key: $enc_key,
                    "export_status": "Stopped",
                    "is_export_error": 1,
                    "error_message": $error_message
                }')
            send_post_request "$WEBHOOK_URL" "$json_payload"
            exit
        fi

        # Get compressed file size
        gz_file_size_bytes=$(ls -l "${gz_file}" | cut -d' ' -f5)
        gz_file_size_kb=$(du -k "${gz_file}" | cut -f1)
        log INFO "Compressed file size: ${gz_file_size_kb} KB (${gz_file_size_bytes} bytes)"
        log INFO "Compression ratio: $(echo "scale=2; ${local_tsv_size_bytes}/${gz_file_size_bytes}" | bc)"

        # Create S3 paths for compressed file
        s3_gz_file_name="${s3_file_name}.gz"
        s3_gz_file="s3://${s3_location}/${database_schema}/${table_name}/${s3_gz_file_name}"
        s3_gz_part_file_name="${s3_part_file_name}.gz"
        s3_gz_part_file="s3://${s3_location}/${database_schema}/${table_name}/${s3_gz_part_file_name}"

        # Upload original TSV file to S3
        log INFO "Uploading original TSV to: ${s3_part_file}"
        if ! aws s3 cp "${tsv_file}" "${s3_part_file}" >/dev/null; then
            message="Failed to upload TSV to S3 bucket
                    TSV: ${tsv_file}
                    S3 Bucket: ${s3_location}"
            log WARN "${message}"
            fail_mail "${message}"
            aws s3 rm "${s3_part_file}" || true
            if ! $nodelete; then
                rm -f "${archive_thread_file}"
            fi
            json_payload=$(jq -n \
                --arg id "$id" \
                --arg enc_key "$enckey" \
                --arg error_message "$message" \
                '{
                    id: $id,
                    enc_key: $enc_key,
                    "export_status": "Stopped",
                    "is_export_error": 1,
                    "error_message":  $error_message            
                }')
            send_post_request "$WEBHOOK_URL" "$json_payload"
            exit
        fi

        # Upload compressed file to S3
        log INFO "Uploading compressed TSV to: ${s3_gz_part_file}"
        if ! aws s3 cp "${gz_file}" "${s3_gz_part_file}" >/dev/null; then
            message="Failed to upload compressed TSV to S3 bucket
                    TSV: ${gz_file}
                    S3 Bucket: ${s3_location}"
            log WARN "${message}"
            fail_mail "${message}"
            aws s3 rm "${s3_part_file}" || true
            aws s3 rm "${s3_gz_part_file}" || true
            if ! $nodelete; then
                rm -f "${archive_thread_file}"
            fi
            json_payload=$(jq -n \
                --arg id "$id" \
                --arg enc_key "$enckey" \
                --arg error_message "$message" \
                '{
                    id: $id,
                    enc_key: $enc_key,
                    "export_status": "Stopped",
                    "is_export_error": 1,
                    "error_message":  $error_message            
                }')
            send_post_request "$WEBHOOK_URL" "$json_payload"
            exit
        fi

        # Verify both files on S3 (original TSV)
        # Keep existing verification logic for original file
        log INFO "Getting tsv size from s3"
        try=0
        s3_tsv_size_bytes=""
        while [[ -z "${s3_tsv_size_bytes}" ]]; do
            try=$((${try} + 1))
            # Once we reach the MAX break the inner loop
            if [[ ${try} -gt ${S3_TRY_MAX} ]]; then
                log WARN "Maximum tries (${S3_TRY_MAX}) reached while getting S3 file size"
                break
            fi
            
            log INFO "Try: ${try}/${S3_TRY_MAX}"
            # Add more robust error handling and debugging
            s3_ls_output=$(aws s3 ls "${s3_part_file}" 2>&1)
            log INFO "S3 ls output: ${s3_ls_output}"
            
            # Check if the command succeeded
            if [[ $? -ne 0 ]]; then
                log WARN "AWS S3 ls command failed: ${s3_ls_output}"
                sleep 30
                continue
            fi
            
            # Extract the file size more reliably
            s3_tsv_size_bytes=$(echo "${s3_ls_output}" | grep -E "${s3_part_file_name}$" | awk '{ print $3 }')
            
            if [[ -z "${s3_tsv_size_bytes}" ]]; then
                log WARN "Failed to extract file size from S3 ls output"
                # Add a longer sleep to allow S3 to propagate changes
                sleep 45
            else
                log INFO "Successfully retrieved S3 file size: ${s3_tsv_size_bytes} bytes"
            fi
        done
        # If try is greater then MAX then remove remote s3 file and skip
        if [[ ${try} -gt ${S3_TRY_MAX} ]]; then
            message="Could not get s3 tsv size"
            log WARN "${message}"
            fail_mail "${message}"
            aws s3 rm "${s3_part_file}" || true
            if ! $nodelete; then
                rm -f "${archive_thread_file}"
            fi
            json_payload=$(jq -n \
                --arg id "$id" \
                --arg enc_key "$enckey" \
                --arg error_message "$message" \
                '{
                    id: $id,
                    enc_key: $enc_key,
                    "export_status": "Stopped",
                    "is_export_error": 1,
                    "error_message":  $error_message            
                }')

            send_post_request "$WEBHOOK_URL" "$json_payload"
            exit
        fi
        # Compare the size of the local file and the s3 file as a basic _checksum_
        log INFO "Comparing local and s3 file size for '${S3_TRY_MAX}' tries"
        log INFO "Current s3 tsv size: ${s3_tsv_size_bytes} bytes"
        local_tsv_size_bytes=$(ls -l "${tsv_file}" | cut -d' ' -f5)
        log INFO "Local tsv size: ${local_tsv_size_bytes} bytes"
        try=0

        
        while [[ "${local_tsv_size_bytes}" != "${s3_tsv_size_bytes}" ]]; do
            try=$((${try} + 1))
            # Once we reach the MAX break the inner loop
            if [[ ${try} -gt ${S3_TRY_MAX} ]]; then
                break
            fi
            s3_tsv_size_bytes=$(aws s3 ls "${s3_part_file}" | grep -E "${s3_part_file_name}$" | awk '{ print $3 }')
            log INFO "Try: ${try}/${S3_TRY_MAX}
            local: ${local_tsv_size_bytes} bytes
            s3:    ${s3_tsv_size_bytes} bytes"
            sleep 20
        done
        if [[ ${try} -gt ${S3_TRY_MAX} ]]; then
            message="Mismatch between local and s3 files"
            log WARN "${message}"
            fail_mail "${message}"
            aws s3 rm "${s3_part_file}" || true
            if ! $nodelete; then
                rm -f "${archive_thread_file}"
            fi
            json_payload=$(jq -n \
                --arg id "$id" \
                --arg enc_key "$enckey" \
                --arg error_message "$message" \
                '{
                    id: $id,
                    enc_key: $enc_key,
                    "export_status": "Stopped",
                    "is_export_error": 1,
                    "error_message":  $error_message            
                }')

            send_post_request "$WEBHOOK_URL" "$json_payload"
            exit
        fi

        # Verify compressed file on S3
        log INFO "Getting compressed tsv size from s3"
        try=0
        s3_gz_size_bytes=""
        while [[ -z "${s3_gz_size_bytes}" ]]; do
            try=$((${try} + 1))
            if [[ ${try} -gt ${S3_TRY_MAX} ]]; then
                log WARN "Maximum tries (${S3_TRY_MAX}) reached while getting compressed S3 file size"
                break
            fi
            
            log INFO "Try: ${try}/${S3_TRY_MAX}"
            # Add more robust error handling and debugging
            s3_ls_output=$(aws s3 ls "${s3_gz_part_file}" 2>&1)
            log INFO "S3 ls output for compressed file: ${s3_ls_output}"
            
            # Check if the command succeeded
            if [[ $? -ne 0 ]]; then
                log WARN "AWS S3 ls command failed for compressed file: ${s3_ls_output}"
                sleep 30
                continue
            fi
            
            # Extract the file size more reliably
            s3_gz_size_bytes=$(echo "${s3_ls_output}" | grep -E "${s3_gz_part_file_name}$" | awk '{ print $3 }')
            
            if [[ -z "${s3_gz_size_bytes}" ]]; then
                log WARN "Failed to extract compressed file size from S3 ls output"
                # Add a longer sleep to allow S3 to propagate changes
                sleep 45
            else
                log INFO "Successfully retrieved compressed S3 file size: ${s3_gz_size_bytes} bytes"
            fi
        done

        if [[ ${try} -gt ${S3_TRY_MAX} ]]; then
            message="Could not get s3 compressed tsv size"
            log WARN "${message}"
            fail_mail "${message}"
            aws s3 rm "${s3_part_file}" || true
            aws s3 rm "${s3_gz_part_file}" || true
            if ! $nodelete; then
                rm -f "${archive_thread_file}"
            fi
            json_payload=$(jq -n \
                --arg id "$id" \
                --arg enc_key "$enckey" \
                --arg error_message "$message" \
                '{
                    id: $id,
                    enc_key: $enc_key,
                    "export_status": "Stopped",
                    "is_export_error": 1,
                    "error_message":  $error_message            
                }')
            send_post_request "$WEBHOOK_URL" "$json_payload"
            exit
        fi

        # Compare compressed file sizes
        log INFO "Comparing local and s3 compressed file sizes"
        log INFO "Current s3 compressed tsv size: ${s3_gz_size_bytes} bytes"
        log INFO "Local compressed tsv size: ${gz_file_size_bytes} bytes"
        try=0

        while [[ "${gz_file_size_bytes}" != "${s3_gz_size_bytes}" ]]; do
            try=$((${try} + 1))
            if [[ ${try} -gt ${S3_TRY_MAX} ]]; then
                break
            fi
            s3_gz_size_bytes=$(aws s3 ls "${s3_gz_part_file}" | grep -E "${s3_gz_part_file_name}$" | awk '{ print $3 }')
            log INFO "Try: ${try}/${S3_TRY_MAX}
            local: ${gz_file_size_bytes} bytes
            s3:    ${s3_gz_size_bytes} bytes"
            sleep 20
        done

        if [[ ${try} -gt ${S3_TRY_MAX} ]]; then
            message="Mismatch between local and s3 compressed files"
            log WARN "${message}"
            fail_mail "${message}"
            aws s3 rm "${s3_part_file}" || true
            aws s3 rm "${s3_gz_part_file}" || true
            if ! $nodelete; then
                rm -f "${archive_thread_file}"
            fi
            json_payload=$(jq -n \
                --arg id "$id" \
                --arg enc_key "$enckey" \
                --arg error_message "$message" \
                '{
                    id: $id,
                    enc_key: $enc_key,
                    "export_status": "Stopped",
                    "is_export_error": 1,
                    "error_message":  $error_message            
                }')
            send_post_request "$WEBHOOK_URL" "$json_payload"
            exit
        fi

        # Rename the original file from .part to the final name
        if ! aws s3 mv "${s3_part_file}" "${s3_file}" >/dev/null; then
            message="Failed to rename original s3 file from .part to final name"
            log WARN "${message}"
            fail_mail "${message}"
            aws s3 rm "${s3_part_file}" || true
            aws s3 rm "${s3_gz_part_file}" || true
            if ! $nodelete; then
                rm -f "${archive_thread_file}"
            fi
            json_payload=$(jq -n \
                --arg id "$id" \
                --arg enc_key "$enckey" \
                --arg error_message "$message" \
                '{
                    id: $id,
                    enc_key: $enc_key,
                    "export_status": "Stopped",
                    "is_export_error": 1,
                    "error_message":  $error_message            
                }')
            send_post_request "$WEBHOOK_URL" "$json_payload"
            exit
        fi

        # Rename the compressed file from .part to the final name
        if ! aws s3 mv "${s3_gz_part_file}" "${s3_gz_file}" >/dev/null; then
            message="Failed to rename compressed s3 file from .part to final name"
            log WARN "${message}"
            fail_mail "${message}"
            # Try to clean up the original file that was successfully renamed
            aws s3 rm "${s3_file}" || true
            if ! $nodelete; then
                rm -f "${archive_thread_file}"
            fi
            json_payload=$(jq -n \
                --arg id "$id" \
                --arg enc_key "$enckey" \
                --arg error_message "$message" \
                '{
                    id: $id,
                    enc_key: $enc_key,
                    "export_status": "Stopped",
                    "is_export_error": 1,
                    "error_message":  $error_message            
                }')
            send_post_request "$WEBHOOK_URL" "$json_payload"
            exit
        fi

        # Remove the local files
        log INFO "Removing local tsv files"
        rm -f "${tsv_file}"
        rm -f "${gz_file}"

        
        if [[ "${export_type}" = "archive" ]]; then

            #########################################################
            ###################### DANGER ZONE ######################
            #########################################################
            log INFO "Deleting data : ${database_host}.${database_schema}.${table_name}"
            if [[ "${is_partitioned}" = "1" ]]; then
                standby_table_name="${table_name}_standby"
                staged_table_name="${table_name}_staged"
                # Make sure there is something to do before swapping anything around
                log INFO "Partitions are enabled. Will use PARTITION DROPing"
                log INFO "Get partitions older then: ${retention_in_days} days"
                get_partitions_sql_query="
                    SELECT PARTITION_NAME
                    FROM information_schema.partitions
                    WHERE TABLE_SCHEMA             = '${database_schema}'
                    AND   TABLE_NAME               = '${table_name}'
                    AND   PARTITION_DESCRIPTION   <= UNIX_TIMESTAMP('${max_date}') 
                    AND   PARTITION_NAME        LIKE 'p________';
                    "
                log INFO "SQL query for partition names
                        ${get_partitions_sql_query}"
                partitions_to_delete=$(mysql_cmd "${database_host}" "${get_partitions_sql_query}" | tr '\n' ',' | sed 's/,$//')
                if [[ -z "${partitions_to_delete}" ]]; then
                    log INFO "No paritions to delete. SKIPPING!"
                    if ! $nodelete; then
                        rm -f "${archive_thread_file}"
                    fi
                    exit
                fi
                # Make sure that the standby table exists
                log INFO "Ensuring standby table exists"
                standby_table_sql="
                    CREATE TABLE IF NOT EXISTS ${database_schema}.${standby_table_name}
                    LIKE ${database_schema}.${table_name} 
                    "
                log INFO "SQL command for standby table:
                        ${standby_table_sql}"
                if ! mysql_cmd "${database_host}" "${standby_table_sql}"; then
                    message="Failed to enforce standby table"
                    log WARN "${message}"
                    fail_mail "${message}"
                    if ! $nodelete; then
                        rm -f "${archive_thread_file}"
                    fi
                    exit
                fi
                log INFO "Swapping tables"
                rename_tables_sql="
                    RENAME TABLE
                    ${database_schema}.${table_name} TO ${database_schema}.${staged_table_name},
                    ${database_schema}.${standby_table_name} TO ${database_schema}.${table_name},
                    ${database_schema}.${staged_table_name} TO ${database_schema}.${standby_table_name};
                    "
                log INFO "SQL command for table renaming
                        ${rename_tables_sql}"
                if ! mysql_cmd "${database_host}" "${rename_tables_sql}"; then
                    message="Failed to rename tables. INVESTIGATE NOW!"
                    log CRIT "${message}"
                    fail_mail "${message}"
                    if ! $nodelete; then
                        rm -f "${archive_thread_file}"
                    fi
                    exit
                fi
                log INFO "Dropping partitions from standby table"
                delete_partitions_sql="
                    ALTER TABLE     ${database_schema}.${standby_table_name}
                    DROP  PARTITION ${partitions_to_delete};
                    "
                log INFO "SQL command for partition delete
                    ${delete_partitions_sql}"
                if ! mysql_cmd "${database_host}" "${delete_partitions_sql}"; then
                    message="Failed to delete partitions"
                    log FAIL "${message}"
                    fail_mail "${message}"
                    if ! $nodelete; then
                        rm -f "${archive_thread_file}"
                    fi
                    exit
                fi
            else
                if [[ "${export_type}" = "Archive" ]]; then
                    log INFO "Partitions are not enabled. Will use DELETEing"
                    delete_data_sql="
                        DELETE
                        FROM   ${database_schema}.${table_name}
                        WHERE  ${unique_key} BETWEEN ${current_batch_id_start} AND ${current_batch_id_end}
                        AND    ${attr_arg} > '${max_date}';
                        "
                    log INFO "SQL command for data delete
                        ${delete_data_sql}"
                    if ! mysql_cmd "${database_host}" "${delete_data_sql}"; then
                        message="Failed to delete data"
                        log FAIL "${message}"
                        fail_mail "${message}"
                        if ! $nodelete; then
                            rm -f "${archive_thread_file}"
                        fi
                        exit
                    fi
                fi
            fi
            if ! $nodelete; then
                rm -f "${archive_thread_file}"
            fi
            ## END DANGER ZONE ##
        else
            log INFO "Export type is not archive. Skipping data deletion"
        fi


        json_payload=$(jq -n \
        --arg id "$id" \
        --arg enc_key "$enckey" \
        --arg s3_file_name "$s3_file_name" \
        --arg s3_file "$s3_file" \
        --arg local_tsv_size_bytes "$local_tsv_size_bytes" \
        --arg current_max_id "$current_max_id" \
        --arg total_db_rows "$total_db_rows" \
        --arg db_data_size_kb "$db_data_size_kb" \
        --arg s3_tsv_size_bytes "$s3_tsv_size_bytes" \
        '{
            "id": $id,
            "enc_key": $enc_key,
            "export_status": "Completed",
            "is_export_error": 0,
            "file_exported_name": $s3_file_name,
            "object_storage_dir_name": $s3_file,
            "file_export_size": $local_tsv_size_bytes,
            "last_exported_id": $current_max_id,
            "total_table_rows_archived": $total_db_rows,
            "total_table_data_size_archived": $db_data_size_kb,
            "total_db_data_size_archived": $db_data_size_kb,
            "file_stored_size": $local_tsv_size_bytes,
            "error_message": null,
            "is_current": true
        }')

        send_post_request "$WEBHOOK_URL" "$json_payload"
    ) &
    # Script needs time to create the thread file so we do not rush though
    sleep 2
done <<<"${mysql_results_archival_info}"
wait

archival_time=$(date -d@"${SECONDS}" -u +"%H Hours %M Minutes %S Seconds")
log INFO "Processing took: ${archival_time}"
log INFO "Last archived row unique key: ${current_max_id}"
log INFO "Finished Archive Run"
