#!/bin/bash

# Function to display usage
usage() {
    echo "Usage: $0 -d <db_name> -s <s3_bucket> [-P] [-N]"
    exit 1
}

# Parse parameters
while getopts ":d:s:PN" opt; do
  case $opt in
    d) DB_NAME=$OPTARG ;;
    s) S3_BUCKET=$OPTARG ;;
    P) PROCESS_PARENT=true ;;
    N) NON_PROCESS_PARENT=true ;;
    *) usage ;;
  esac
done

# Check if mandatory parameters are provided
if [[ -z "$DB_NAME" || -z "$S3_BUCKET" ]]; then
    usage
fi

TODAY=$(date +%Y-%m-%d)

# Function to execute MySQL query
exec_query() {
    local query=$1
    mysql --login-path=creditsnap-test -D "$DB_NAME" -se "$query"
}

# Function to export rows to TSV and sync to S3
export_and_sync() {
    local table=$1
    local select_query=$2
    local retention_date=$3

    # Calculate row count
    row_count=$(exec_query "SELECT COUNT(*) FROM ($select_query) as subquery")

    if [[ $row_count -gt 0 ]]; then
        local tsv_file="/tmp/${table}_${retention_date}_${row_count}.tsv"
        local gz_file="${tsv_file}.gz"
        
        # Check if file already exists in S3
        if aws s3 ls "s3://$S3_BUCKET/${gz_file##*/}" > /dev/null 2>&1; then
            echo "File already exists in S3: ${gz_file##*/}"
        else
            exec_query "$select_query" > "$tsv_file"
            gzip "$tsv_file"  # Compress the file
            aws s3 cp "$gz_file" "s3://$S3_BUCKET/"
            if [[ $? -eq 0 ]]; then
                # Convert SELECT query to DELETE query
                delete_query=$(echo "$select_query" | sed 's/^SELECT \* /DELETE /')
                exec_query "$delete_query"
                echo "Deleted $row_count rows from $table"
            fi
            rm "$gz_file"
        fi
    else
        echo "No rows to export from $table"
    fi
}

# Process parent tables if -P is passed
if [[ $PROCESS_PARENT ]]; then
    # Query to get parent tables with retention days from archive_table
    parent_tables=$(exec_query "SELECT table_name, retention_days FROM archive_table WHERE is_parent_table=1 AND retention_days IS NOT NULL")
    
    # Read parent tables into an array
    mapfile -t parent_tables_array <<< "$parent_tables"

    # Loop through each parent table
    for parent_table_row in "${parent_tables_array[@]}"; do
        IFS=$'\t' read -r parent_table retention_days <<< "$parent_table_row"
        # Calculate retention date based on today's date and retention days
        retention_date=$(date -d "$TODAY - $retention_days days" +%Y-%m-%d)
        retention_datetime=$(date -d "$retention_date 00:00:00" +%Y-%m-%dT%H:%M:%S)

        # Get the parent column name from archive_table
        parent_column_name=$(exec_query "SELECT parent_column_name FROM archive_table WHERE table_name='$parent_table'")

        # Query to get the maximum value of the parent column within the retention date
        max_num=$(exec_query "SELECT MAX($parent_column_name) FROM $parent_table WHERE createdDate < '$retention_datetime'")
        
        # Get child tables associated with the current parent table from archive_table
        child_tables=$(exec_query "SELECT table_name FROM archive_table WHERE parent_table_name='$parent_table'")

        # Read child tables into an array
        mapfile -t child_tables_array <<< "$child_tables"

        # Loop through each child table
        for child_table in "${child_tables_array[@]}"; do
            # Export and sync data from child table
            select_query="SELECT * FROM $child_table WHERE $parent_column_name <= $max_num"
            export_and_sync "$child_table" "$select_query" "$retention_date"
        done

        # Export and sync data from parent table
        select_query="SELECT * FROM $parent_table WHERE $parent_column_name <= $max_num"
        export_and_sync "$parent_table" "$select_query" "$retention_date"
    done
fi

# Process non-parent tables if -N is passed
if [[ $NON_PROCESS_PARENT ]]; then
    non_parent_tables=$(exec_query "SELECT table_name, retention_days FROM archive_table WHERE is_single_table_to_archive=1 AND retention_days IS NOT NULL")

    # Read non-parent tables into an array
    mapfile -t non_parent_tables_array <<< "$non_parent_tables"

    for non_parent_row in "${non_parent_tables_array[@]}"; do
        IFS=$'\t' read -r non_parent_table retention_days <<< "$non_parent_row"
        retention_date=$(date -d "$TODAY - $retention_days days" +%Y-%m-%d)
        retention_datetime=$(date -d "$retention_date 00:00:00" +%Y-%m-%dT%H:%M:%S)
        select_query="SELECT * FROM $non_parent_table WHERE createdDate < '$retention_datetime'"
        export_and_sync "$non_parent_table" "$select_query" "$retention_date"
    done
fi