get_table_row_size() {
    local database_host="$1"
    local database_schema="$2" 
    local table_name="$3"
    
    # Get table statistics
    table_stats_query="
        SELECT 
            TABLE_ROWS,
            DATA_LENGTH,
            INDEX_LENGTH,
            AVG_ROW_LENGTH,
            ENGINE
        FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA='${database_schema}' AND TABLE_NAME='${table_name}';"
    
    read table_rows data_length index_length avg_row_length engine <<< \
        $(mysql_cmd "${database_host}" "${table_stats_query}" | tr '\t' ' ')
    
    log INFO "Table stats - Rows: ${table_rows}, Data: ${data_length}, Index: ${index_length}, Engine: ${engine}"
    
    # If table has data, use actual statistics
    if [[ ${table_rows} -gt 0 && ${data_length} -gt 0 ]]; then
        actual_avg_size=$(( (data_length + index_length) / table_rows ))
        log INFO "Using actual average row size: ${actual_avg_size} bytes"
        echo ${actual_avg_size}
        return
    fi
    
    # For empty tables, sample or estimate
    if [[ ${table_rows} -eq 0 ]]; then
        log WARN "Empty table, using column-based estimation"
        estimated_size=$(get_estimated_row_size "${database_host}" "${database_schema}" "${table_name}")
        echo ${estimated_size}
        return
    fi
    
    # Fallback to sampling for unreliable statistics
    log INFO "Sampling rows for size calculation"
    sample_size=$(get_sample_row_size "${database_host}" "${database_schema}" "${table_name}")
    echo ${sample_size}
}