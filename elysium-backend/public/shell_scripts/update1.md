1- for the while loop starting at line 306 move the actual logic starting at line 366 to archive and sync table to a function "export_and_sync" while keeping all tracking and loging the same.

2- analyze parent_table_recursive_.sh and update db_archive.sh to include the logic to archive parent and child tables. continue the work from where I left off. make sure to pass all needed parameters to the function.
if the table has child tables, then loop through them and call the function for each child table. aim is to archive parent and child tables in one run. first archive the child tables and then the parent table. child table rows are filtered by parent column 

3- update avg_row_length_query to use function from row_size.sh for better estimation of row size. copy and update the function to return the size in bytes.