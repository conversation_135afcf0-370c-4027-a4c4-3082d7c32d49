#!/bin/bash

# Stop containers
docker-compose down

# Remove any existing vendor directory in the host
rm -rf ./elysium-backend/vendor

# Rebuild the backend container
docker-compose build backend

# Start containers
docker-compose up -d

# Check if vendor directory was created
echo "Checking if vendor directory exists..."
docker-compose exec backend ls -la /var/www/html/vendor

# Check if autoload.php exists
echo "Checking if autoload.php exists..."
docker-compose exec backend ls -la /var/www/html/vendor/autoload.php

# Show backend logs
echo "Backend logs:"
docker-compose logs backend