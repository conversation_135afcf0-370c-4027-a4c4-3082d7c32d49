-- Adminer 5.0.4 MySQL 8.0.35 dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;


CREATE TABLE `subscription_type` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `subscription_type_name_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `subscription_type` (`id`, `name`, `is_active`, `deleted_at`, `created_at`, `updated_at`) VALUES
(1,	'Trial',	1,	NULL,	'2023-12-21 13:56:41',	'2023-12-21 13:56:41'),
(2,	'Monthly',	1,	NULL,	'2023-12-21 13:56:42',	'2023-12-21 13:56:42'),
(3,	'Yearly',	1,	NULL,	'2023-12-21 13:56:43',	'2023-12-21 13:56:43');


CREATE TABLE `table_job_schedule` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `client_db_server_table_id` bigint unsigned NOT NULL,
  `agent_uuid` varchar(250) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `database_host` varchar(300) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `database_port` smallint DEFAULT NULL,
  `database_name` varchar(150) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `schema_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `table_name` varchar(250) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `data_retention_days` smallint unsigned DEFAULT NULL,
  `object_backup_location` varchar(250) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_partitioned` tinyint NOT NULL DEFAULT '0',
  `batch_size` int NOT NULL DEFAULT '10000',
  `is_active` tinyint NOT NULL DEFAULT '0',
  `is_current` tinyint unsigned NOT NULL DEFAULT '1',
  `archive_day_of_week` tinyint unsigned NOT NULL DEFAULT '0',
  `archive_untill_date_utc` time DEFAULT NULL,
  `archive_start_at_utc` time DEFAULT NULL,
  `local_time_zone` varchar(35) COLLATE utf8mb4_unicode_ci DEFAULT 'PST',
  `s3_file` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `total_rows` int NOT NULL DEFAULT '0',
  `schedule_type` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `retention_index` int DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `database_job_schedule_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `table_job_schedule_database_job_schedule_id_foreign` (`database_job_schedule_id`),
  KEY `table_job_schedule_client_db_server_table_id_foreign` (`client_db_server_table_id`),
  CONSTRAINT `table_job_schedule_client_db_server_table_id_foreign` FOREIGN KEY (`client_db_server_table_id`) REFERENCES `client_db_server_table` (`id`),
  CONSTRAINT `table_job_schedule_database_job_schedule_id_foreign` FOREIGN KEY (`database_job_schedule_id`) REFERENCES `database_job_schedule` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;



CREATE TABLE `table_job_schedule_execution_log` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `table_job_schedule_id` bigint unsigned NOT NULL,
  `day_of_week` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `schedule_enc_key` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `execution_datetime` datetime DEFAULT NULL,
  `status` enum('awaiting','in_progress','ssh_connection_failed','time_window_expired','execution_successfull') COLLATE utf8mb4_unicode_ci NOT NULL,
  `request_json` json DEFAULT NULL,
  `response_json` json DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `table_job_schedule_execution_log_table_job_schedule_id_foreign` (`table_job_schedule_id`),
  CONSTRAINT `table_job_schedule_execution_log_table_job_schedule_id_foreign` FOREIGN KEY (`table_job_schedule_id`) REFERENCES `table_job_schedule` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `table_process_status` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `table_action_name` varchar(35) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `table_process_status` (`id`, `table_action_name`, `created_at`, `updated_at`) VALUES
(1,	'No Action',	'2024-02-14 14:57:21',	'2024-02-14 14:57:21'),
(2,	'Export',	'2024-02-14 14:57:21',	'2024-02-14 14:57:21'),
(3,	'Archive',	'2024-02-14 14:57:21',	'2024-02-14 14:57:21'),
(4,	'Analyze',	'2024-02-14 14:57:21',	'2024-02-14 14:57:21');


CREATE TABLE `timezone` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `timezone_name_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `timezone` (`id`, `name`, `is_active`, `deleted_at`, `created_at`, `updated_at`) VALUES
(1,	'Etc/GMT+12',	1,	NULL,	NULL,	NULL),
(2,	'Etc/GMT+11',	1,	NULL,	NULL,	NULL),
(3,	'Pacific/Pago_Pago',	1,	NULL,	NULL,	NULL),
(4,	'Pacific/Niue',	1,	NULL,	NULL,	NULL),
(5,	'Pacific/Midway',	1,	NULL,	NULL,	NULL),
(6,	'America/Adak',	1,	NULL,	NULL,	NULL),
(7,	'Pacific/Honolulu',	1,	NULL,	NULL,	NULL),
(8,	'Pacific/Rarotonga',	1,	NULL,	NULL,	NULL),
(9,	'Pacific/Tahiti',	1,	NULL,	NULL,	NULL),
(10,	'Pacific/Johnston',	1,	NULL,	NULL,	NULL),
(11,	'Etc/GMT+10',	1,	NULL,	NULL,	NULL),
(12,	'Pacific/Marquesas',	1,	NULL,	NULL,	NULL),
(13,	'America/Anchorage',	1,	NULL,	NULL,	NULL),
(14,	'America/Juneau',	1,	NULL,	NULL,	NULL),
(15,	'America/Metlakatla',	1,	NULL,	NULL,	NULL),
(16,	'America/Nome',	1,	NULL,	NULL,	NULL),
(17,	'America/Sitka',	1,	NULL,	NULL,	NULL),
(18,	'America/Yakutat',	1,	NULL,	NULL,	NULL),
(19,	'Etc/GMT+9',	1,	NULL,	NULL,	NULL),
(20,	'Pacific/Gambier',	1,	NULL,	NULL,	NULL),
(21,	'America/Tijuana',	1,	NULL,	NULL,	NULL),
(22,	'America/Santa_Isabel',	1,	NULL,	NULL,	NULL),
(23,	'Etc/GMT+8',	1,	NULL,	NULL,	NULL),
(24,	'Pacific/Pitcairn',	1,	NULL,	NULL,	NULL),
(25,	'America/Los_Angeles',	1,	NULL,	NULL,	NULL),
(26,	'America/Vancouver',	1,	NULL,	NULL,	NULL),
(27,	'America/Dawson',	1,	NULL,	NULL,	NULL),
(28,	'America/Whitehorse',	1,	NULL,	NULL,	NULL),
(29,	'PST8PDT',	1,	NULL,	NULL,	NULL),
(30,	'America/Phoenix',	1,	NULL,	NULL,	NULL),
(31,	'America/Dawson_Creek',	1,	NULL,	NULL,	NULL),
(32,	'America/Creston',	1,	NULL,	NULL,	NULL),
(33,	'America/Fort_Nelson',	1,	NULL,	NULL,	NULL),
(34,	'America/Hermosillo',	1,	NULL,	NULL,	NULL),
(35,	'Etc/GMT+7',	1,	NULL,	NULL,	NULL),
(36,	'America/Chihuahua',	1,	NULL,	NULL,	NULL),
(37,	'America/Mazatlan',	1,	NULL,	NULL,	NULL),
(38,	'America/Denver',	1,	NULL,	NULL,	NULL),
(39,	'America/Edmonton',	1,	NULL,	NULL,	NULL),
(40,	'America/Cambridge_Bay',	1,	NULL,	NULL,	NULL),
(41,	'America/Inuvik',	1,	NULL,	NULL,	NULL),
(42,	'America/Yellowknife',	1,	NULL,	NULL,	NULL),
(43,	'America/Ojinaga',	1,	NULL,	NULL,	NULL),
(44,	'America/Boise',	1,	NULL,	NULL,	NULL),
(45,	'MST7MDT',	1,	NULL,	NULL,	NULL),
(46,	'America/Guatemala',	1,	NULL,	NULL,	NULL),
(47,	'America/Belize',	1,	NULL,	NULL,	NULL),
(48,	'America/Costa_Rica',	1,	NULL,	NULL,	NULL),
(49,	'Pacific/Galapagos',	1,	NULL,	NULL,	NULL),
(50,	'America/Tegucigalpa',	1,	NULL,	NULL,	NULL),
(51,	'America/Managua',	1,	NULL,	NULL,	NULL),
(52,	'America/El_Salvador',	1,	NULL,	NULL,	NULL),
(53,	'Etc/GMT+6',	1,	NULL,	NULL,	NULL),
(54,	'America/Chicago',	1,	NULL,	NULL,	NULL),
(55,	'America/Winnipeg',	1,	NULL,	NULL,	NULL),
(56,	'America/Rainy_River',	1,	NULL,	NULL,	NULL),
(57,	'America/Rankin_Inlet',	1,	NULL,	NULL,	NULL),
(58,	'America/Resolute',	1,	NULL,	NULL,	NULL),
(59,	'America/Matamoros',	1,	NULL,	NULL,	NULL),
(60,	'America/Indiana/Knox',	1,	NULL,	NULL,	NULL),
(61,	'America/Indiana/Tell_City',	1,	NULL,	NULL,	NULL),
(62,	'America/Menominee',	1,	NULL,	NULL,	NULL),
(63,	'America/North_Dakota/Beulah',	1,	NULL,	NULL,	NULL),
(64,	'America/North_Dakota/Center',	1,	NULL,	NULL,	NULL),
(65,	'America/North_Dakota/New_Salem',	1,	NULL,	NULL,	NULL),
(66,	'CST6CDT',	1,	NULL,	NULL,	NULL),
(67,	'Pacific/Easter',	1,	NULL,	NULL,	NULL),
(68,	'America/Mexico_City',	1,	NULL,	NULL,	NULL),
(69,	'America/Bahia_Banderas',	1,	NULL,	NULL,	NULL),
(70,	'America/Merida',	1,	NULL,	NULL,	NULL),
(71,	'America/Monterrey',	1,	NULL,	NULL,	NULL),
(72,	'America/Regina',	1,	NULL,	NULL,	NULL),
(73,	'America/Swift_Current',	1,	NULL,	NULL,	NULL),
(74,	'America/Bogota',	1,	NULL,	NULL,	NULL),
(75,	'America/Rio_Branco',	1,	NULL,	NULL,	NULL),
(76,	'America/Eirunepe',	1,	NULL,	NULL,	NULL),
(77,	'America/Coral_Harbour',	1,	NULL,	NULL,	NULL),
(78,	'America/Guayaquil',	1,	NULL,	NULL,	NULL),
(79,	'America/Jamaica',	1,	NULL,	NULL,	NULL),
(80,	'America/Cayman',	1,	NULL,	NULL,	NULL),
(81,	'America/Panama',	1,	NULL,	NULL,	NULL),
(82,	'America/Lima',	1,	NULL,	NULL,	NULL),
(83,	'Etc/GMT+5',	1,	NULL,	NULL,	NULL),
(84,	'America/Cancun',	1,	NULL,	NULL,	NULL),
(85,	'America/New_York',	1,	NULL,	NULL,	NULL),
(86,	'America/Nassau',	1,	NULL,	NULL,	NULL),
(87,	'America/Toronto',	1,	NULL,	NULL,	NULL),
(88,	'America/Iqaluit',	1,	NULL,	NULL,	NULL),
(89,	'America/Montreal',	1,	NULL,	NULL,	NULL),
(90,	'America/Nipigon',	1,	NULL,	NULL,	NULL),
(91,	'America/Pangnirtung',	1,	NULL,	NULL,	NULL),
(92,	'America/Thunder_Bay',	1,	NULL,	NULL,	NULL),
(93,	'America/Detroit',	1,	NULL,	NULL,	NULL),
(94,	'America/Indiana/Petersburg',	1,	NULL,	NULL,	NULL),
(95,	'America/Indiana/Vincennes',	1,	NULL,	NULL,	NULL),
(96,	'America/Indiana/Winamac',	1,	NULL,	NULL,	NULL),
(97,	'America/Kentucky/Monticello',	1,	NULL,	NULL,	NULL),
(98,	'America/Louisville',	1,	NULL,	NULL,	NULL),
(99,	'EST5EDT',	1,	NULL,	NULL,	NULL),
(100,	'America/Port-au-Prince',	1,	NULL,	NULL,	NULL),
(101,	'America/Havana',	1,	NULL,	NULL,	NULL),
(102,	'America/Indianapolis',	1,	NULL,	NULL,	NULL),
(103,	'America/Indiana/Marengo',	1,	NULL,	NULL,	NULL),
(104,	'America/Indiana/Vevay',	1,	NULL,	NULL,	NULL),
(105,	'America/Grand_Turk',	1,	NULL,	NULL,	NULL),
(106,	'America/Asuncion',	1,	NULL,	NULL,	NULL),
(107,	'America/Halifax',	1,	NULL,	NULL,	NULL),
(108,	'Atlantic/Bermuda',	1,	NULL,	NULL,	NULL),
(109,	'America/Glace_Bay',	1,	NULL,	NULL,	NULL),
(110,	'America/Goose_Bay',	1,	NULL,	NULL,	NULL),
(111,	'America/Moncton',	1,	NULL,	NULL,	NULL),
(112,	'America/Thule',	1,	NULL,	NULL,	NULL),
(113,	'America/Caracas',	1,	NULL,	NULL,	NULL),
(114,	'America/Cuiaba',	1,	NULL,	NULL,	NULL),
(115,	'America/Campo_Grande',	1,	NULL,	NULL,	NULL),
(116,	'America/La_Paz',	1,	NULL,	NULL,	NULL),
(117,	'America/Antigua',	1,	NULL,	NULL,	NULL),
(118,	'America/Anguilla',	1,	NULL,	NULL,	NULL),
(119,	'America/Aruba',	1,	NULL,	NULL,	NULL),
(120,	'America/Barbados',	1,	NULL,	NULL,	NULL),
(121,	'America/St_Barthelemy',	1,	NULL,	NULL,	NULL),
(122,	'America/Kralendijk',	1,	NULL,	NULL,	NULL),
(123,	'America/Manaus',	1,	NULL,	NULL,	NULL),
(124,	'America/Boa_Vista',	1,	NULL,	NULL,	NULL),
(125,	'America/Porto_Velho',	1,	NULL,	NULL,	NULL),
(126,	'America/Blanc-Sablon',	1,	NULL,	NULL,	NULL),
(127,	'America/Curacao',	1,	NULL,	NULL,	NULL),
(128,	'America/Dominica',	1,	NULL,	NULL,	NULL),
(129,	'America/Santo_Domingo',	1,	NULL,	NULL,	NULL),
(130,	'America/Grenada',	1,	NULL,	NULL,	NULL),
(131,	'America/Guadeloupe',	1,	NULL,	NULL,	NULL),
(132,	'America/Guyana',	1,	NULL,	NULL,	NULL),
(133,	'America/St_Kitts',	1,	NULL,	NULL,	NULL),
(134,	'America/St_Lucia',	1,	NULL,	NULL,	NULL),
(135,	'America/Marigot',	1,	NULL,	NULL,	NULL),
(136,	'America/Martinique',	1,	NULL,	NULL,	NULL),
(137,	'America/Montserrat',	1,	NULL,	NULL,	NULL),
(138,	'America/Puerto_Rico',	1,	NULL,	NULL,	NULL),
(139,	'America/Lower_Princes',	1,	NULL,	NULL,	NULL),
(140,	'America/Port_of_Spain',	1,	NULL,	NULL,	NULL),
(141,	'America/St_Vincent',	1,	NULL,	NULL,	NULL),
(142,	'America/Tortola',	1,	NULL,	NULL,	NULL),
(143,	'America/St_Thomas',	1,	NULL,	NULL,	NULL),
(144,	'Etc/GMT+4',	1,	NULL,	NULL,	NULL),
(145,	'America/Santiago',	1,	NULL,	NULL,	NULL),
(146,	'America/St_Johns',	1,	NULL,	NULL,	NULL),
(147,	'America/Araguaina',	1,	NULL,	NULL,	NULL),
(148,	'America/Sao_Paulo',	1,	NULL,	NULL,	NULL),
(149,	'America/Cayenne',	1,	NULL,	NULL,	NULL),
(150,	'Antarctica/Rothera',	1,	NULL,	NULL,	NULL),
(151,	'Antarctica/Palmer',	1,	NULL,	NULL,	NULL),
(152,	'America/Fortaleza',	1,	NULL,	NULL,	NULL),
(153,	'America/Belem',	1,	NULL,	NULL,	NULL),
(154,	'America/Maceio',	1,	NULL,	NULL,	NULL),
(155,	'America/Recife',	1,	NULL,	NULL,	NULL),
(156,	'America/Santarem',	1,	NULL,	NULL,	NULL),
(157,	'Atlantic/Stanley',	1,	NULL,	NULL,	NULL),
(158,	'America/Paramaribo',	1,	NULL,	NULL,	NULL),
(159,	'Etc/GMT+3',	1,	NULL,	NULL,	NULL),
(160,	'America/Buenos_Aires',	1,	NULL,	NULL,	NULL),
(161,	'America/Argentina/La_Rioja',	1,	NULL,	NULL,	NULL),
(162,	'America/Argentina/Rio_Gallegos',	1,	NULL,	NULL,	NULL),
(163,	'America/Argentina/Salta',	1,	NULL,	NULL,	NULL),
(164,	'America/Argentina/San_Juan',	1,	NULL,	NULL,	NULL),
(165,	'America/Argentina/San_Luis',	1,	NULL,	NULL,	NULL),
(166,	'America/Argentina/Tucuman',	1,	NULL,	NULL,	NULL),
(167,	'America/Argentina/Ushuaia',	1,	NULL,	NULL,	NULL),
(168,	'America/Catamarca',	1,	NULL,	NULL,	NULL),
(169,	'America/Cordoba',	1,	NULL,	NULL,	NULL),
(170,	'America/Jujuy',	1,	NULL,	NULL,	NULL),
(171,	'America/Mendoza',	1,	NULL,	NULL,	NULL),
(172,	'America/Godthab',	1,	NULL,	NULL,	NULL),
(173,	'America/Montevideo',	1,	NULL,	NULL,	NULL),
(174,	'America/Punta_Arenas',	1,	NULL,	NULL,	NULL),
(175,	'America/Miquelon',	1,	NULL,	NULL,	NULL),
(176,	'America/Bahia',	1,	NULL,	NULL,	NULL),
(177,	'Etc/GMT+2',	1,	NULL,	NULL,	NULL),
(178,	'America/Noronha',	1,	NULL,	NULL,	NULL),
(179,	'Atlantic/South_Georgia',	1,	NULL,	NULL,	NULL),
(180,	'Atlantic/Azores',	1,	NULL,	NULL,	NULL),
(181,	'America/Scoresbysund',	1,	NULL,	NULL,	NULL),
(182,	'Atlantic/Cape_Verde',	1,	NULL,	NULL,	NULL),
(183,	'Etc/GMT+1',	1,	NULL,	NULL,	NULL),
(184,	'Etc/GMT',	1,	NULL,	NULL,	NULL),
(185,	'America/Danmarkshavn',	1,	NULL,	NULL,	NULL),
(186,	'Etc/UTC',	1,	NULL,	NULL,	NULL),
(187,	'Europe/London',	1,	NULL,	NULL,	NULL),
(188,	'Atlantic/Canary',	1,	NULL,	NULL,	NULL),
(189,	'Atlantic/Faeroe',	1,	NULL,	NULL,	NULL),
(190,	'Europe/Guernsey',	1,	NULL,	NULL,	NULL),
(191,	'Europe/Dublin',	1,	NULL,	NULL,	NULL),
(192,	'Europe/Isle_of_Man',	1,	NULL,	NULL,	NULL),
(193,	'Europe/Jersey',	1,	NULL,	NULL,	NULL),
(194,	'Europe/Lisbon',	1,	NULL,	NULL,	NULL),
(195,	'Atlantic/Madeira',	1,	NULL,	NULL,	NULL),
(196,	'Atlantic/Reykjavik',	1,	NULL,	NULL,	NULL),
(197,	'Africa/Ouagadougou',	1,	NULL,	NULL,	NULL),
(198,	'Africa/Abidjan',	1,	NULL,	NULL,	NULL),
(199,	'Africa/Accra',	1,	NULL,	NULL,	NULL),
(200,	'Africa/Banjul',	1,	NULL,	NULL,	NULL),
(201,	'Africa/Conakry',	1,	NULL,	NULL,	NULL),
(202,	'Africa/Bissau',	1,	NULL,	NULL,	NULL),
(203,	'Africa/Monrovia',	1,	NULL,	NULL,	NULL),
(204,	'Africa/Bamako',	1,	NULL,	NULL,	NULL),
(205,	'Africa/Nouakchott',	1,	NULL,	NULL,	NULL),
(206,	'Atlantic/St_Helena',	1,	NULL,	NULL,	NULL),
(207,	'Africa/Freetown',	1,	NULL,	NULL,	NULL),
(208,	'Africa/Dakar',	1,	NULL,	NULL,	NULL),
(209,	'Africa/Lome',	1,	NULL,	NULL,	NULL),
(210,	'Africa/Sao_Tome',	1,	NULL,	NULL,	NULL),
(211,	'Africa/Casablanca',	1,	NULL,	NULL,	NULL),
(212,	'Africa/El_Aaiun',	1,	NULL,	NULL,	NULL),
(213,	'Europe/Berlin',	1,	NULL,	NULL,	NULL),
(214,	'Europe/Andorra',	1,	NULL,	NULL,	NULL),
(215,	'Europe/Vienna',	1,	NULL,	NULL,	NULL),
(216,	'Europe/Zurich',	1,	NULL,	NULL,	NULL),
(217,	'Europe/Busingen',	1,	NULL,	NULL,	NULL),
(218,	'Europe/Gibraltar',	1,	NULL,	NULL,	NULL),
(219,	'Europe/Rome',	1,	NULL,	NULL,	NULL),
(220,	'Europe/Vaduz',	1,	NULL,	NULL,	NULL),
(221,	'Europe/Luxembourg',	1,	NULL,	NULL,	NULL),
(222,	'Europe/Monaco',	1,	NULL,	NULL,	NULL),
(223,	'Europe/Malta',	1,	NULL,	NULL,	NULL),
(224,	'Europe/Amsterdam',	1,	NULL,	NULL,	NULL),
(225,	'Europe/Oslo',	1,	NULL,	NULL,	NULL),
(226,	'Europe/Stockholm',	1,	NULL,	NULL,	NULL),
(227,	'Arctic/Longyearbyen',	1,	NULL,	NULL,	NULL),
(228,	'Europe/San_Marino',	1,	NULL,	NULL,	NULL),
(229,	'Europe/Vatican',	1,	NULL,	NULL,	NULL),
(230,	'Europe/Budapest',	1,	NULL,	NULL,	NULL),
(231,	'Europe/Tirane',	1,	NULL,	NULL,	NULL),
(232,	'Europe/Prague',	1,	NULL,	NULL,	NULL),
(233,	'Europe/Podgorica',	1,	NULL,	NULL,	NULL),
(234,	'Europe/Belgrade',	1,	NULL,	NULL,	NULL),
(235,	'Europe/Ljubljana',	1,	NULL,	NULL,	NULL),
(236,	'Europe/Bratislava',	1,	NULL,	NULL,	NULL),
(237,	'Europe/Paris',	1,	NULL,	NULL,	NULL),
(238,	'Europe/Brussels',	1,	NULL,	NULL,	NULL),
(239,	'Europe/Copenhagen',	1,	NULL,	NULL,	NULL),
(240,	'Europe/Madrid',	1,	NULL,	NULL,	NULL),
(241,	'Africa/Ceuta',	1,	NULL,	NULL,	NULL),
(242,	'Europe/Warsaw',	1,	NULL,	NULL,	NULL),
(243,	'Europe/Sarajevo',	1,	NULL,	NULL,	NULL),
(244,	'Europe/Zagreb',	1,	NULL,	NULL,	NULL),
(245,	'Europe/Skopje',	1,	NULL,	NULL,	NULL),
(246,	'Africa/Lagos',	1,	NULL,	NULL,	NULL),
(247,	'Africa/Luanda',	1,	NULL,	NULL,	NULL),
(248,	'Africa/Porto-Novo',	1,	NULL,	NULL,	NULL),
(249,	'Africa/Kinshasa',	1,	NULL,	NULL,	NULL),
(250,	'Africa/Bangui',	1,	NULL,	NULL,	NULL),
(251,	'Africa/Brazzaville',	1,	NULL,	NULL,	NULL),
(252,	'Africa/Douala',	1,	NULL,	NULL,	NULL),
(253,	'Africa/Algiers',	1,	NULL,	NULL,	NULL),
(254,	'Africa/Libreville',	1,	NULL,	NULL,	NULL),
(255,	'Africa/Malabo',	1,	NULL,	NULL,	NULL),
(256,	'Africa/Niamey',	1,	NULL,	NULL,	NULL),
(257,	'Africa/Ndjamena',	1,	NULL,	NULL,	NULL),
(258,	'Africa/Tunis',	1,	NULL,	NULL,	NULL),
(259,	'Etc/GMT-1',	1,	NULL,	NULL,	NULL),
(260,	'Asia/Amman',	1,	NULL,	NULL,	NULL),
(261,	'Europe/Bucharest',	1,	NULL,	NULL,	NULL),
(262,	'Asia/Nicosia',	1,	NULL,	NULL,	NULL),
(263,	'Asia/Famagusta',	1,	NULL,	NULL,	NULL),
(264,	'Europe/Athens',	1,	NULL,	NULL,	NULL),
(265,	'Asia/Beirut',	1,	NULL,	NULL,	NULL),
(266,	'Africa/Cairo',	1,	NULL,	NULL,	NULL),
(267,	'Europe/Chisinau',	1,	NULL,	NULL,	NULL),
(268,	'Asia/Damascus',	1,	NULL,	NULL,	NULL),
(269,	'Asia/Hebron',	1,	NULL,	NULL,	NULL),
(270,	'Asia/Gaza',	1,	NULL,	NULL,	NULL),
(271,	'Africa/Johannesburg',	1,	NULL,	NULL,	NULL),
(272,	'Africa/Bujumbura',	1,	NULL,	NULL,	NULL),
(273,	'Africa/Gaborone',	1,	NULL,	NULL,	NULL),
(274,	'Africa/Lubumbashi',	1,	NULL,	NULL,	NULL),
(275,	'Africa/Maseru',	1,	NULL,	NULL,	NULL),
(276,	'Africa/Blantyre',	1,	NULL,	NULL,	NULL),
(277,	'Africa/Maputo',	1,	NULL,	NULL,	NULL),
(278,	'Africa/Kigali',	1,	NULL,	NULL,	NULL),
(279,	'Africa/Mbabane',	1,	NULL,	NULL,	NULL),
(280,	'Africa/Lusaka',	1,	NULL,	NULL,	NULL),
(281,	'Africa/Harare',	1,	NULL,	NULL,	NULL),
(282,	'Etc/GMT-2',	1,	NULL,	NULL,	NULL),
(283,	'Europe/Kiev',	1,	NULL,	NULL,	NULL),
(284,	'Europe/Mariehamn',	1,	NULL,	NULL,	NULL),
(285,	'Europe/Sofia',	1,	NULL,	NULL,	NULL),
(286,	'Europe/Tallinn',	1,	NULL,	NULL,	NULL),
(287,	'Europe/Helsinki',	1,	NULL,	NULL,	NULL),
(288,	'Europe/Vilnius',	1,	NULL,	NULL,	NULL),
(289,	'Europe/Riga',	1,	NULL,	NULL,	NULL),
(290,	'Europe/Uzhgorod',	1,	NULL,	NULL,	NULL),
(291,	'Europe/Zaporozhye',	1,	NULL,	NULL,	NULL),
(292,	'Asia/Jerusalem',	1,	NULL,	NULL,	NULL),
(293,	'Europe/Kaliningrad',	1,	NULL,	NULL,	NULL),
(294,	'Africa/Khartoum',	1,	NULL,	NULL,	NULL),
(295,	'Africa/Tripoli',	1,	NULL,	NULL,	NULL),
(296,	'Africa/Windhoek',	1,	NULL,	NULL,	NULL),
(297,	'Asia/Baghdad',	1,	NULL,	NULL,	NULL),
(298,	'Europe/Istanbul',	1,	NULL,	NULL,	NULL),
(299,	'Asia/Riyadh',	1,	NULL,	NULL,	NULL),
(300,	'Asia/Bahrain',	1,	NULL,	NULL,	NULL),
(301,	'Asia/Kuwait',	1,	NULL,	NULL,	NULL),
(302,	'Asia/Qatar',	1,	NULL,	NULL,	NULL),
(303,	'Asia/Aden',	1,	NULL,	NULL,	NULL),
(304,	'Europe/Minsk',	1,	NULL,	NULL,	NULL),
(305,	'Europe/Moscow',	1,	NULL,	NULL,	NULL),
(306,	'Europe/Kirov',	1,	NULL,	NULL,	NULL),
(307,	'Europe/Simferopol',	1,	NULL,	NULL,	NULL),
(308,	'Africa/Nairobi',	1,	NULL,	NULL,	NULL),
(309,	'Antarctica/Syowa',	1,	NULL,	NULL,	NULL),
(310,	'Africa/Djibouti',	1,	NULL,	NULL,	NULL),
(311,	'Africa/Asmera',	1,	NULL,	NULL,	NULL),
(312,	'Africa/Addis_Ababa',	1,	NULL,	NULL,	NULL),
(313,	'Indian/Comoro',	1,	NULL,	NULL,	NULL),
(314,	'Indian/Antananarivo',	1,	NULL,	NULL,	NULL),
(315,	'Africa/Mogadishu',	1,	NULL,	NULL,	NULL),
(316,	'Africa/Juba',	1,	NULL,	NULL,	NULL),
(317,	'Africa/Dar_es_Salaam',	1,	NULL,	NULL,	NULL),
(318,	'Africa/Kampala',	1,	NULL,	NULL,	NULL),
(319,	'Indian/Mayotte',	1,	NULL,	NULL,	NULL),
(320,	'Etc/GMT-3',	1,	NULL,	NULL,	NULL),
(321,	'Asia/Tehran',	1,	NULL,	NULL,	NULL),
(322,	'Asia/Dubai',	1,	NULL,	NULL,	NULL),
(323,	'Asia/Muscat',	1,	NULL,	NULL,	NULL),
(324,	'Etc/GMT-4',	1,	NULL,	NULL,	NULL),
(325,	'Europe/Astrakhan',	1,	NULL,	NULL,	NULL),
(326,	'Europe/Ulyanovsk',	1,	NULL,	NULL,	NULL),
(327,	'Asia/Baku',	1,	NULL,	NULL,	NULL),
(328,	'Europe/Samara',	1,	NULL,	NULL,	NULL),
(329,	'Indian/Mauritius',	1,	NULL,	NULL,	NULL),
(330,	'Indian/Reunion',	1,	NULL,	NULL,	NULL),
(331,	'Indian/Mahe',	1,	NULL,	NULL,	NULL),
(332,	'Europe/Saratov',	1,	NULL,	NULL,	NULL),
(333,	'Asia/Tbilisi',	1,	NULL,	NULL,	NULL),
(334,	'Europe/Volgograd',	1,	NULL,	NULL,	NULL),
(335,	'Asia/Yerevan',	1,	NULL,	NULL,	NULL),
(336,	'Asia/Kabul',	1,	NULL,	NULL,	NULL),
(337,	'Asia/Tashkent',	1,	NULL,	NULL,	NULL),
(338,	'Antarctica/Mawson',	1,	NULL,	NULL,	NULL),
(339,	'Asia/Oral',	1,	NULL,	NULL,	NULL),
(340,	'Asia/Aqtau',	1,	NULL,	NULL,	NULL),
(341,	'Asia/Aqtobe',	1,	NULL,	NULL,	NULL),
(342,	'Asia/Atyrau',	1,	NULL,	NULL,	NULL),
(343,	'Indian/Maldives',	1,	NULL,	NULL,	NULL),
(344,	'Indian/Kerguelen',	1,	NULL,	NULL,	NULL),
(345,	'Asia/Dushanbe',	1,	NULL,	NULL,	NULL),
(346,	'Asia/Ashgabat',	1,	NULL,	NULL,	NULL),
(347,	'Asia/Samarkand',	1,	NULL,	NULL,	NULL),
(348,	'Etc/GMT-5',	1,	NULL,	NULL,	NULL),
(349,	'Asia/Yekaterinburg',	1,	NULL,	NULL,	NULL),
(350,	'Asia/Karachi',	1,	NULL,	NULL,	NULL),
(351,	'Asia/Qyzylorda',	1,	NULL,	NULL,	NULL),
(352,	'Asia/Calcutta',	1,	NULL,	NULL,	NULL),
(353,	'Asia/Colombo',	1,	NULL,	NULL,	NULL),
(354,	'Asia/Katmandu',	1,	NULL,	NULL,	NULL),
(355,	'Asia/Almaty',	1,	NULL,	NULL,	NULL),
(356,	'Antarctica/Vostok',	1,	NULL,	NULL,	NULL),
(357,	'Asia/Urumqi',	1,	NULL,	NULL,	NULL),
(358,	'Indian/Chagos',	1,	NULL,	NULL,	NULL),
(359,	'Asia/Bishkek',	1,	NULL,	NULL,	NULL),
(360,	'Asia/Qostanay',	1,	NULL,	NULL,	NULL),
(361,	'Etc/GMT-6',	1,	NULL,	NULL,	NULL),
(362,	'Asia/Dhaka',	1,	NULL,	NULL,	NULL),
(363,	'Asia/Thimphu',	1,	NULL,	NULL,	NULL),
(364,	'Asia/Omsk',	1,	NULL,	NULL,	NULL),
(365,	'Asia/Rangoon',	1,	NULL,	NULL,	NULL),
(366,	'Indian/Cocos',	1,	NULL,	NULL,	NULL),
(367,	'Asia/Bangkok',	1,	NULL,	NULL,	NULL),
(368,	'Antarctica/Davis',	1,	NULL,	NULL,	NULL),
(369,	'Indian/Christmas',	1,	NULL,	NULL,	NULL),
(370,	'Asia/Jakarta',	1,	NULL,	NULL,	NULL),
(371,	'Asia/Pontianak',	1,	NULL,	NULL,	NULL),
(372,	'Asia/Phnom_Penh',	1,	NULL,	NULL,	NULL),
(373,	'Asia/Vientiane',	1,	NULL,	NULL,	NULL),
(374,	'Asia/Saigon',	1,	NULL,	NULL,	NULL),
(375,	'Etc/GMT-7',	1,	NULL,	NULL,	NULL),
(376,	'Asia/Barnaul',	1,	NULL,	NULL,	NULL),
(377,	'Asia/Hovd',	1,	NULL,	NULL,	NULL),
(378,	'Asia/Krasnoyarsk',	1,	NULL,	NULL,	NULL),
(379,	'Asia/Novokuznetsk',	1,	NULL,	NULL,	NULL),
(380,	'Asia/Novosibirsk',	1,	NULL,	NULL,	NULL),
(381,	'Asia/Tomsk',	1,	NULL,	NULL,	NULL),
(382,	'Asia/Shanghai',	1,	NULL,	NULL,	NULL),
(383,	'Asia/Hong_Kong',	1,	NULL,	NULL,	NULL),
(384,	'Asia/Macau',	1,	NULL,	NULL,	NULL),
(385,	'Asia/Irkutsk',	1,	NULL,	NULL,	NULL),
(386,	'Asia/Singapore',	1,	NULL,	NULL,	NULL),
(387,	'Antarctica/Casey',	1,	NULL,	NULL,	NULL),
(388,	'Asia/Brunei',	1,	NULL,	NULL,	NULL),
(389,	'Asia/Makassar',	1,	NULL,	NULL,	NULL),
(390,	'Asia/Kuala_Lumpur',	1,	NULL,	NULL,	NULL),
(391,	'Asia/Kuching',	1,	NULL,	NULL,	NULL),
(392,	'Asia/Manila',	1,	NULL,	NULL,	NULL),
(393,	'Etc/GMT-8',	1,	NULL,	NULL,	NULL),
(394,	'Australia/Perth',	1,	NULL,	NULL,	NULL),
(395,	'Asia/Taipei',	1,	NULL,	NULL,	NULL),
(396,	'Asia/Ulaanbaatar',	1,	NULL,	NULL,	NULL),
(397,	'Asia/Choibalsan',	1,	NULL,	NULL,	NULL),
(398,	'Australia/Eucla',	1,	NULL,	NULL,	NULL),
(399,	'Asia/Chita',	1,	NULL,	NULL,	NULL),
(400,	'Asia/Tokyo',	1,	NULL,	NULL,	NULL),
(401,	'Asia/Jayapura',	1,	NULL,	NULL,	NULL),
(402,	'Pacific/Palau',	1,	NULL,	NULL,	NULL),
(403,	'Asia/Dili',	1,	NULL,	NULL,	NULL),
(404,	'Etc/GMT-9',	1,	NULL,	NULL,	NULL),
(405,	'Asia/Pyongyang',	1,	NULL,	NULL,	NULL),
(406,	'Asia/Seoul',	1,	NULL,	NULL,	NULL),
(407,	'Asia/Yakutsk',	1,	NULL,	NULL,	NULL),
(408,	'Asia/Khandyga',	1,	NULL,	NULL,	NULL),
(409,	'Australia/Adelaide',	1,	NULL,	NULL,	NULL),
(410,	'Australia/Broken_Hill',	1,	NULL,	NULL,	NULL),
(411,	'Australia/Darwin',	1,	NULL,	NULL,	NULL),
(412,	'Australia/Brisbane',	1,	NULL,	NULL,	NULL),
(413,	'Australia/Lindeman',	1,	NULL,	NULL,	NULL),
(414,	'Australia/Sydney',	1,	NULL,	NULL,	NULL),
(415,	'Australia/Melbourne',	1,	NULL,	NULL,	NULL),
(416,	'Pacific/Port_Moresby',	1,	NULL,	NULL,	NULL),
(417,	'Antarctica/DumontDUrville',	1,	NULL,	NULL,	NULL),
(418,	'Pacific/Truk',	1,	NULL,	NULL,	NULL),
(419,	'Pacific/Guam',	1,	NULL,	NULL,	NULL),
(420,	'Pacific/Saipan',	1,	NULL,	NULL,	NULL),
(421,	'Etc/GMT-10',	1,	NULL,	NULL,	NULL),
(422,	'Australia/Hobart',	1,	NULL,	NULL,	NULL),
(423,	'Australia/Currie',	1,	NULL,	NULL,	NULL),
(424,	'Asia/Vladivostok',	1,	NULL,	NULL,	NULL),
(425,	'Asia/Ust-Nera',	1,	NULL,	NULL,	NULL),
(426,	'Australia/Lord_Howe',	1,	NULL,	NULL,	NULL),
(427,	'Pacific/Bougainville',	1,	NULL,	NULL,	NULL),
(428,	'Asia/Srednekolymsk',	1,	NULL,	NULL,	NULL),
(429,	'Asia/Magadan',	1,	NULL,	NULL,	NULL),
(430,	'Pacific/Norfolk',	1,	NULL,	NULL,	NULL),
(431,	'Asia/Sakhalin',	1,	NULL,	NULL,	NULL),
(432,	'Pacific/Guadalcanal',	1,	NULL,	NULL,	NULL),
(433,	'Antarctica/Macquarie',	1,	NULL,	NULL,	NULL),
(434,	'Pacific/Ponape',	1,	NULL,	NULL,	NULL),
(435,	'Pacific/Kosrae',	1,	NULL,	NULL,	NULL),
(436,	'Pacific/Noumea',	1,	NULL,	NULL,	NULL),
(437,	'Pacific/Efate',	1,	NULL,	NULL,	NULL),
(438,	'Etc/GMT-11',	1,	NULL,	NULL,	NULL),
(439,	'Asia/Kamchatka',	1,	NULL,	NULL,	NULL),
(440,	'Asia/Anadyr',	1,	NULL,	NULL,	NULL),
(441,	'Pacific/Auckland',	1,	NULL,	NULL,	NULL),
(442,	'Antarctica/McMurdo',	1,	NULL,	NULL,	NULL),
(443,	'Etc/GMT-12',	1,	NULL,	NULL,	NULL),
(444,	'Pacific/Tarawa',	1,	NULL,	NULL,	NULL),
(445,	'Pacific/Majuro',	1,	NULL,	NULL,	NULL),
(446,	'Pacific/Kwajalein',	1,	NULL,	NULL,	NULL),
(447,	'Pacific/Nauru',	1,	NULL,	NULL,	NULL),
(448,	'Pacific/Funafuti',	1,	NULL,	NULL,	NULL),
(449,	'Pacific/Wake',	1,	NULL,	NULL,	NULL),
(450,	'Pacific/Wallis',	1,	NULL,	NULL,	NULL),
(451,	'Pacific/Fiji',	1,	NULL,	NULL,	NULL),
(452,	'Pacific/Chatham',	1,	NULL,	NULL,	NULL),
(453,	'Etc/GMT-13',	1,	NULL,	NULL,	NULL),
(454,	'Pacific/Enderbury',	1,	NULL,	NULL,	NULL),
(455,	'Pacific/Fakaofo',	1,	NULL,	NULL,	NULL),
(456,	'Pacific/Tongatapu',	1,	NULL,	NULL,	NULL),
(457,	'Pacific/Apia',	1,	NULL,	NULL,	NULL),
(458,	'Pacific/Kiritimati',	1,	NULL,	NULL,	NULL),
(459,	'Etc/GMT-14',	1,	NULL,	NULL,	NULL);


CREATE TABLE `user` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `first_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `otp` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_trial_active` tinyint(1) NOT NULL DEFAULT '0',
  `company_id` bigint unsigned NOT NULL,
  `timezone_id` bigint unsigned DEFAULT NULL,
  `stripe_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `pm_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `pm_last_four` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `trial_ends_at` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '0',
  `remember_token` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_email_unique` (`email`),
  KEY `user_company_id_foreign` (`company_id`),
  KEY `user_timezone_id_foreign` (`timezone_id`),
  KEY `user_stripe_id_index` (`stripe_id`),
  CONSTRAINT `user_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `company` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_timezone_id_foreign` FOREIGN KEY (`timezone_id`) REFERENCES `timezone` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `user` (`id`, `first_name`, `last_name`, `email`, `email_verified_at`, `otp`, `password`, `image`, `is_trial_active`, `company_id`, `timezone_id`, `stripe_id`, `pm_type`, `pm_last_four`, `trial_ends_at`, `is_active`, `remember_token`, `deleted_at`, `created_at`, `updated_at`) VALUES
(6,	'Muhammad',	'Ahmad',	'<EMAIL>',	NULL,	NULL,	'$2y$10$pj40pu.DzCJA6D9D7SJ6yOl7z.MVr9c1DoH52S8IlDYxkZd9zwx6m',	'1704197521.jpg',	1,	6,	1,	'cus_PIiEMy0zSQWp4e',	'visa',	'4242',	'2024-02-05 10:20:14',	1,	'CsoZ12HRM8N3ClWYrjH0FHZ9fAYSnUVenfyEsLCFUeBQmNb0yFu675m0HCm9',	NULL,	'2024-01-02 12:09:59',	'2024-01-22 10:20:14'),
(7,	'Test',	'Giga',	'<EMAIL>',	NULL,	NULL,	'$2y$10$UquM82OgJ/kF9GXpsH9.yOhNKyQl8Izngl0yMnDhAxFfKnzRiig0.',	NULL,	1,	7,	9,	'cus_PIiIs6t25jXp6F',	'visa',	'4242',	NULL,	1,	'ssWa1Ngbd7dlFZT5B2Om1DiPXfdmH46ZvrqjPgvg91k2ZVVEfQnQY65GCqao',	NULL,	'2024-01-02 12:12:45',	'2024-01-22 12:45:50'),
(8,	'Farhan',	'Akram',	'<EMAIL>',	NULL,	NULL,	'$2y$10$kkpKKomx7t8CsvrYMDA3a.mj4aPgfpUrJzBGd7aiEy6siLhw7WEFO',	NULL,	1,	8,	18,	NULL,	NULL,	NULL,	'2024-01-18 17:08:45',	1,	'FhDRXNxk2wG9oSsFkLYahmvAWbQy9eJDvW6jam3OAMG6IJvy628NlXzJBfyi',	NULL,	'2024-01-04 17:05:59',	'2024-04-23 13:07:24'),
(9,	'fiaz',	'tariq',	'<EMAIL>',	NULL,	NULL,	'$2y$10$bjXGVR2yh0Lr/rN0O.AYM.6AjTo2gpqCxs2Pm.xhCLS/zwqt2qNdi',	'1705322148.jpg',	0,	9,	1,	'cus_PRevy1jusnr8Zm',	'visa',	'4242',	'2024-01-19 11:14:29',	1,	'8B6ljTgkd36r73e4ZWTYESCr2BCb3MJBCaItGfpoE2FZeL5plFN3Y54tNBeT',	NULL,	'2024-01-05 11:13:28',	'2024-01-29 09:07:08'),
(10,	'tester',	'tester 2',	'<EMAIL>',	NULL,	NULL,	'$2y$10$aBcU.Gvp083dNDQ.kugdUuEZC6uoLyXJoCXg2zSszKjVA394OVzYS',	'1706093467.jpg',	0,	10,	18,	'cus_PQw4iDL45A55w7',	'jcb',	'0000',	'2024-02-07 10:48:12',	1,	'hm5pkhtsEYAMiaK8UQrNG2IcvEaztGcYvaAbbhtxiBlIWnIjE6rMzAQcSoVz',	NULL,	'2024-01-24 10:47:40',	'2024-01-24 11:00:54'),
(11,	'fiaz',	'tariq',	'<EMAIL>',	NULL,	NULL,	'$2y$10$AmBuYImkI4XdTkn7JLsfEOZqfrdkEjJ9TEiDZXrV2spG6CZ/8548u',	NULL,	0,	11,	NULL,	NULL,	NULL,	NULL,	NULL,	1,	'5CLkhDHG99CnbWKH7t0vY3TYDSjGQTYhl8YdyBLbBxbyAGNyIobF19cNEmPN',	NULL,	'2024-01-24 10:53:32',	'2024-01-24 10:53:49'),
(12,	'fiaz',	'tariq',	'<EMAIL>',	NULL,	NULL,	'$2y$10$YM8zFldL3fVk0W.x8vwCWOc1VFIUcGvb2OB39/vc9FRfcto8TWMQS',	NULL,	0,	12,	NULL,	'cus_PQwGJc4hPqLEul',	'visa',	'4242',	NULL,	1,	'5CsXqbrhWpz5ZLgxV9LOWBmtp6XkjBa7PKkj2QPXN8JtDLs1Bg6DxMKFyCSg',	NULL,	'2024-01-24 11:12:24',	'2024-01-24 11:13:03'),
(13,	'Test',	'Giga',	'<EMAIL>',	NULL,	NULL,	'$2y$10$RhigjXJL3QZhjVVg3rNLV.QkWhH6ZyuMZd2Vs45I8pP2pMcINTQX.',	NULL,	0,	13,	NULL,	'cus_PQxvYsmHFZr5cF',	'unionpay',	'0004',	NULL,	1,	'gtvGQ5JR7NVrAiKWQZ67QNcqPuNcI62IJAL2anXHZ2OnqcvA4FgBbbKBbEWy',	NULL,	'2024-01-24 12:54:50',	'2024-01-24 12:55:41'),
(14,	'Omer',	'Obaid',	'<EMAIL>',	NULL,	NULL,	'$2y$10$kFWGV1KCmJ.n/cc0oe97ROBvCv/ot0Dcfz88pOF4IFnyVY1FdVlBa',	NULL,	0,	14,	NULL,	'cus_PRMRzjZVCz2ggB',	'visa',	'4242',	NULL,	1,	'F15tKBLySytKFtFwe5EB1i9WGVipyYW31jHrr8lFLydsBtz32I4Q1yBDpLj4',	NULL,	'2024-01-25 14:15:00',	'2024-01-25 14:16:00'),
(15,	'fiaz',	'tariq',	'<EMAIL>',	NULL,	'257270',	'$2y$10$4OTgSRSXSNGIPXyCoLvOeODXxSaG8wB0thCtSQzpjezCZ7nAO8P1W',	NULL,	0,	15,	NULL,	NULL,	NULL,	NULL,	NULL,	0,	NULL,	NULL,	'2024-01-25 15:30:04',	'2024-01-25 15:30:04'),
(16,	'Omer',	'Obaid',	'<EMAIL>',	NULL,	NULL,	'$2y$10$ANHNuO5Mnt5jz30I7/Z.3OC7Pmd9sL2ge53mPJrvAtnOLHUG4m0h6',	NULL,	0,	16,	NULL,	NULL,	NULL,	NULL,	NULL,	1,	'ACebrOPZK01gZpCiTr9jdxKPpQJVPATXl4K82iTUHg9j7OKPlLOOPQxVTf8X',	NULL,	'2024-01-29 08:41:18',	'2024-01-29 08:50:20'),
(17,	'Muhammad Usman',	'Faisal',	'<EMAIL>',	NULL,	NULL,	'$2y$10$7kxtXCnjD/BYlJVgCkxKa.lhMuTmoNDnDT2hAxFM3ysHyJVs1rsj6',	NULL,	0,	17,	NULL,	'cus_PTXZLsN23oAOqy',	'visa',	'4242',	NULL,	1,	'W5lG707oxwogLsykqe5w3LeO1zm3zDPK8HjBDvGUmbOCLcM6KdWscHCyfQWf',	NULL,	'2024-01-31 09:52:55',	'2024-03-05 15:21:24'),
(18,	'asdad',	'asdad',	'<EMAIL>',	NULL,	'602560',	'$2y$10$WuU6I6iaAgLu6MhB5PEWtekMPFPJsOgP49u5Gh8zfJABbYFcUppk2',	NULL,	0,	18,	NULL,	NULL,	NULL,	NULL,	NULL,	0,	NULL,	NULL,	'2024-02-06 14:41:55',	'2024-02-06 14:41:55'),
(19,	'assad',	'aqdsasd',	'<EMAIL>',	NULL,	NULL,	'$2y$10$Y5Gomna8FoHZw8xXCwHiFeLOBHsJ5uaGV80l2bzS00ln4V8dq41Cq',	NULL,	1,	19,	NULL,	NULL,	NULL,	NULL,	'2024-02-20 14:43:59',	1,	'AFTITKgiR7mdpS8DyMJRuakssRp4AfIZ50RrLcEdkNmIzRqZtWP32v35pHtv',	NULL,	'2024-02-06 14:43:07',	'2024-02-06 14:43:59'),
(20,	'Omer',	'Obaid',	'<EMAIL>',	NULL,	NULL,	'$2y$10$BOd0FQivVNKVQGmtvDwaFuHPuHro6v/q6thScb6svAICmwx/Mnc0W',	NULL,	0,	20,	NULL,	'cus_PZavKK10i3vvrG',	'visa',	'4242',	NULL,	1,	'5bHfyVxpgc6TgHvS6DOi6nc0Rlo6utGwXz8aSOhC4zPl3ZmOjldLrNxmFBnN',	NULL,	'2024-02-16 13:45:11',	'2024-02-16 13:46:05'),
(21,	'fiaz',	'tariq',	'<EMAIL>',	NULL,	NULL,	'$2y$10$Xs8Dhv94ubda.yynl3dxq.68krKbrO4V4JYhXJwVoNjTD.qPpPNAu',	'1710924394.jpg',	0,	21,	1,	'cus_PlWLHmacQ7Bpfn',	'visa',	'4242',	NULL,	1,	'mw47Rd1b02fq1Blp8FiORjBSQg2J6iW73dUd2SZa8k3zq2OoPC0HUhJoG2SM',	NULL,	'2024-03-19 09:49:15',	'2024-03-20 08:46:34'),
(22,	'Hassan',	'Ali',	'<EMAIL>',	NULL,	NULL,	'$2y$10$Kiv/eGe6OsDyKjanpMNyl.tbs.NQIZiNctyQs9WRrq/l7w9UUyYRS',	NULL,	1,	22,	NULL,	NULL,	NULL,	NULL,	'2024-05-02 22:15:00',	1,	'EsT7RXYRt3Uy9AKD8HIMoOi3w6vXlodnvYQ1tbFcl1JEgdyX2rmm5xLh3krf',	NULL,	'2024-04-18 22:14:31',	'2024-04-18 22:15:00');

-- 2025-04-24 20:33:17 UTC
