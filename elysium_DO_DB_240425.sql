-- Adminer 5.0.4 MySQL 8.0.35 dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;

DROP TABLE IF EXISTS `assigned_port`;
CREATE TABLE `assigned_port` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `port` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `assigned_port` (`id`, `port`, `created_at`, `updated_at`) VALUES
(12,	'13306',	'2024-01-10 11:45:15',	'2024-01-10 11:45:15'),
(19,	'13307',	'2024-01-12 12:44:29',	'2024-01-12 12:44:29'),
(21,	'13308',	'2024-01-12 12:51:10',	'2024-01-12 12:51:10'),
(23,	'13309',	'2024-01-15 12:56:44',	'2024-01-15 12:56:44'),
(26,	'13310',	'2024-01-24 10:55:59',	'2024-01-24 10:55:59'),
(27,	'13311',	'2024-01-24 10:56:52',	'2024-01-24 10:56:52'),
(29,	'13312',	'2024-03-08 15:19:57',	'2024-03-08 15:19:57');

DROP TABLE IF EXISTS `client_database`;
CREATE TABLE `client_database` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `client_db_server_id` bigint unsigned NOT NULL,
  `db_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `total_current_db_size` int unsigned NOT NULL DEFAULT '0',
  `total_current_db_data_size` int unsigned NOT NULL DEFAULT '0',
  `total_current_db_index_size` int unsigned NOT NULL DEFAULT '0',
  `total_table` int unsigned NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `is_deleted` tinyint NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `color_code` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `client_database_client_db_server_id_foreign` (`client_db_server_id`),
  CONSTRAINT `client_database_client_db_server_id_foreign` FOREIGN KEY (`client_db_server_id`) REFERENCES `client_db_server` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `client_db_schema`;
CREATE TABLE `client_db_schema` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `client_database_id` bigint unsigned NOT NULL,
  `schema_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_deleted` tinyint NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `client_db_schema` (`id`, `deleted_at`, `created_at`, `updated_at`, `client_database_id`, `schema_name`, `is_deleted`) VALUES
(30,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	'',	0),
(31,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	1,	'sakila',	0),
(32,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	2,	'sakila',	0),
(33,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	3,	'sakila',	0);

DROP TABLE IF EXISTS `client_db_server`;
CREATE TABLE `client_db_server` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id` bigint unsigned NOT NULL,
  `remote_server_id` bigint unsigned NOT NULL,
  `client_db_server_type_id` bigint unsigned NOT NULL,
  `remote_server_status_id` bigint unsigned NOT NULL,
  `db_server_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `db_server_alias_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `client_db_server_uuid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `hostname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `port` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '0',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `timezone` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `total_current_db_storage_setup_size` int unsigned NOT NULL DEFAULT '1' COMMENT 'Size in GB. Accessible via AWS CLI',
  `color_code` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `client_db_server_db_server_alias_name_unique` (`db_server_alias_name`),
  UNIQUE KEY `client_db_server_client_db_server_uuid_unique` (`client_db_server_uuid`),
  KEY `client_db_server_company_id_foreign` (`company_id`),
  KEY `client_db_server_remote_server_id_foreign` (`remote_server_id`),
  KEY `client_db_server_client_db_server_type_id_foreign` (`client_db_server_type_id`),
  KEY `client_db_server_remote_server_status_id_foreign` (`remote_server_status_id`),
  CONSTRAINT `client_db_server_client_db_server_type_id_foreign` FOREIGN KEY (`client_db_server_type_id`) REFERENCES `client_db_server_type` (`id`) ON DELETE CASCADE,
  CONSTRAINT `client_db_server_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `company` (`id`) ON DELETE CASCADE,
  CONSTRAINT `client_db_server_remote_server_id_foreign` FOREIGN KEY (`remote_server_id`) REFERENCES `remote_server` (`id`) ON DELETE CASCADE,
  CONSTRAINT `client_db_server_remote_server_status_id_foreign` FOREIGN KEY (`remote_server_status_id`) REFERENCES `remote_server_status` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `client_db_server_stat`;
CREATE TABLE `client_db_server_stat` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `dim_date_id` bigint unsigned NOT NULL,
  `client_db_server_id` bigint unsigned NOT NULL,
  `company_id` bigint unsigned NOT NULL,
  `db_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `stat_date` date NOT NULL,
  `total_db_rows` bigint unsigned NOT NULL DEFAULT (_utf8mb4'0') COMMENT 'Total table rows on that day',
  `total_db_rows_archived` bigint unsigned NOT NULL DEFAULT '0',
  `total_db_storage_setup` bigint unsigned NOT NULL DEFAULT '0',
  `total_db_size` bigint unsigned NOT NULL DEFAULT '0',
  `total_db_index` bigint unsigned NOT NULL DEFAULT '0',
  `total_db_storage_setup_cost` decimal(8,2) unsigned NOT NULL DEFAULT '0.00',
  `total_db_data_size_archived` bigint unsigned NOT NULL DEFAULT '0',
  `total_db_object_storage_used` bigint unsigned NOT NULL DEFAULT '0',
  `total_table_storage_cost_saving` decimal(6,2) unsigned NOT NULL DEFAULT '0.00',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `client_db_server_stat_dim_date_id_foreign` (`dim_date_id`),
  KEY `client_db_server_stat_client_db_server_id_foreign` (`client_db_server_id`),
  KEY `client_db_server_stat_company_id_foreign` (`company_id`),
  CONSTRAINT `client_db_server_stat_client_db_server_id_foreign` FOREIGN KEY (`client_db_server_id`) REFERENCES `client_db_server` (`id`) ON DELETE CASCADE,
  CONSTRAINT `client_db_server_stat_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `company` (`id`) ON DELETE CASCADE,
  CONSTRAINT `client_db_server_stat_dim_date_id_foreign` FOREIGN KEY (`dim_date_id`) REFERENCES `dim_date` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `client_db_server_table`;
CREATE TABLE `client_db_server_table` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `client_db_schema_id` bigint unsigned NOT NULL,
  `client_db_server_table_uuid` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `table_schema_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `table_database_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `table_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `table_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `engine` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `version` int unsigned DEFAULT NULL,
  `row_format` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `total_current_table_rows` int unsigned NOT NULL DEFAULT '0',
  `total_current_data_length` int unsigned NOT NULL DEFAULT '0',
  `avg_row_length` int unsigned DEFAULT NULL,
  `max_data_length` int unsigned DEFAULT NULL,
  `data_free` int unsigned DEFAULT NULL,
  `check_time` timestamp NULL DEFAULT NULL,
  `table_collation` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `checksum` int unsigned DEFAULT NULL,
  `create_options` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `table_comment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `timezone` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `client_database_id` bigint unsigned NOT NULL,
  `client_db_server_id` bigint unsigned NOT NULL,
  `has_reference_integrity` int unsigned NOT NULL DEFAULT '0' COMMENT 'The table has referential integrity, which could affect archiving.',
  `color_code` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `table_process_status_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `client_db_server_table_client_db_schema_id_foreign` (`client_db_schema_id`),
  KEY `client_db_server_table_table_process_status_id_foreign` (`table_process_status_id`),
  CONSTRAINT `client_db_server_table_client_db_schema_id_foreign` FOREIGN KEY (`client_db_schema_id`) REFERENCES `client_db_schema` (`id`) ON DELETE CASCADE,
  CONSTRAINT `client_db_server_table_table_process_status_id_foreign` FOREIGN KEY (`table_process_status_id`) REFERENCES `table_process_status` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `client_db_server_table` (`id`, `client_db_schema_id`, `client_db_server_table_uuid`, `table_schema_name`, `table_database_name`, `table_name`, `table_type`, `engine`, `version`, `row_format`, `total_current_table_rows`, `total_current_data_length`, `avg_row_length`, `max_data_length`, `data_free`, `check_time`, `table_collation`, `checksum`, `create_options`, `table_comment`, `timezone`, `deleted_at`, `created_at`, `updated_at`, `client_database_id`, `client_db_server_id`, `has_reference_integrity`, `color_code`, `table_process_status_id`) VALUES
(668,	30,	'3f1163db-5df7-4d04-9aba-afbc01cdfd98',	'',	'sakila',	'actor',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	200,	16384,	81,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	0,	0,	NULL,	NULL),
(669,	30,	'ad66a686-5ea7-4dff-9783-7daa2a9c5806',	'',	'sakila',	'actor_info',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	0,	0,	NULL,	NULL),
(670,	30,	'99eb877d-c509-4f5c-9c24-19b378759fb7',	'',	'sakila',	'address',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	603,	98304,	163,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	0,	0,	NULL,	NULL),
(671,	30,	'c700979f-67d8-4dab-9058-8e37375cb00a',	'',	'sakila',	'category',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	16,	16384,	1024,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	0,	0,	NULL,	NULL),
(672,	30,	'70b49308-d9d0-490f-bf38-b2a198c983cd',	'',	'sakila',	'city',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	600,	49152,	81,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	0,	0,	NULL,	NULL),
(673,	30,	'a6f93124-11ee-42e8-802a-6379a5e47dd0',	'',	'sakila',	'country',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	109,	16384,	150,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	0,	0,	NULL,	NULL),
(674,	30,	'f1413a62-04d7-4399-a00c-6696f933d965',	'',	'sakila',	'customer',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	599,	81920,	136,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	0,	0,	NULL,	NULL),
(675,	30,	'697bd7c5-cb07-4c6e-8c5a-067638d0cd37',	'',	'sakila',	'customer_list',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	0,	0,	NULL,	NULL),
(676,	30,	'1830f757-6c6b-4ce5-b2ac-879f37a46246',	'',	'sakila',	'film',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	1000,	196608,	196,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	0,	0,	NULL,	NULL),
(677,	30,	'dd1e2bfe-b25e-4234-a2a7-4f366ac00fb7',	'',	'sakila',	'film_actor',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	5462,	196608,	35,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	0,	0,	NULL,	NULL),
(678,	30,	'89554982-7e83-4400-b546-33a8c120f153',	'',	'sakila',	'film_category',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	1000,	65536,	65,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	0,	0,	NULL,	NULL),
(679,	30,	'8f729854-972d-43af-bc43-62b202cf99d0',	'',	'sakila',	'film_list',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	0,	0,	NULL,	NULL),
(680,	30,	'af4d3317-0206-4348-a930-bcbb654994ac',	'',	'sakila',	'film_text',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	0,	16384,	0,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	0,	0,	NULL,	NULL),
(681,	30,	'e3547b32-9c47-4339-b2f5-c352dd525192',	'',	'sakila',	'inventory',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	4581,	180224,	39,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	0,	0,	NULL,	NULL),
(682,	30,	'9a59c940-f14c-42d5-985d-4e2eb07ff590',	'',	'sakila',	'language',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	6,	16384,	2730,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	0,	0,	NULL,	NULL),
(683,	30,	'7eee5336-45d4-413b-8dd2-5789dd6758a7',	'',	'sakila',	'nicer_but_slower_film_list',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	0,	0,	NULL,	NULL),
(684,	30,	'9d5a5c26-379d-49fc-a9d6-3a60f5618f10',	'',	'sakila',	'payment',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	15411,	1589248,	103,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	0,	0,	NULL,	NULL),
(685,	30,	'2279c25a-3b78-41e7-921a-c08bd6ba44f9',	'',	'sakila',	'rental',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	16424,	1589248,	96,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	0,	0,	NULL,	NULL),
(686,	30,	'802f35f5-fb16-4a95-b2ed-2ae614c20f48',	'',	'sakila',	'sales_by_film_category',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	0,	0,	NULL,	NULL),
(687,	30,	'4f324301-b446-4b26-83a2-b789e6eb907d',	'',	'sakila',	'sales_by_store',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	0,	0,	NULL,	NULL),
(688,	30,	'07b5e701-6771-4d96-8cd9-62c1d086647b',	'',	'sakila',	'staff',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	2,	65536,	32768,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	0,	0,	NULL,	NULL),
(689,	30,	'88ba55a3-e332-4ec8-a521-2b45d332876a',	'',	'sakila',	'staff_list',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	0,	0,	NULL,	NULL),
(690,	30,	'908bb3b9-3889-4122-9051-d44a4cb08bdf',	'',	'sakila',	'store',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	2,	16384,	8192,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	0,	0,	NULL,	NULL),
(691,	31,	'1ba26df5-20ef-4775-867f-2edd65aca363',	'',	'sakila',	'actor',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	200,	16384,	81,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	1,	105,	0,	NULL,	NULL),
(692,	31,	'878df4ef-cf5d-4a88-8ee8-b256d396326f',	'',	'sakila',	'actor_info',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	1,	105,	0,	NULL,	NULL),
(693,	31,	'1cbd5bcd-b575-4f88-aa84-ec72ca75aaaf',	'',	'sakila',	'address',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	603,	98304,	163,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	1,	105,	0,	NULL,	NULL),
(694,	31,	'6191335d-465f-4fee-9886-b1a1924582d9',	'',	'sakila',	'category',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	16,	16384,	1024,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	1,	105,	0,	NULL,	NULL),
(695,	31,	'83bca17e-92d7-4721-b86e-3891db05fed4',	'',	'sakila',	'city',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	600,	49152,	81,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	1,	105,	0,	NULL,	NULL),
(696,	31,	'307a6f2d-fcbf-44e3-a249-ab9166e9ba94',	'',	'sakila',	'country',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	109,	16384,	150,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	1,	105,	0,	NULL,	NULL),
(697,	31,	'dc511be3-735a-44df-938d-9e76bce4e6e7',	'',	'sakila',	'customer',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	599,	81920,	136,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	1,	105,	0,	NULL,	NULL),
(698,	31,	'8d801933-776b-4dcd-804f-a40cc8d2d709',	'',	'sakila',	'customer_list',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	1,	105,	0,	NULL,	NULL),
(699,	31,	'161fa091-fb16-4d38-8517-eb795a28ea4d',	'',	'sakila',	'film',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	1000,	196608,	196,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	1,	105,	0,	NULL,	NULL),
(700,	31,	'3c745bf6-0f68-4244-894e-4e284ff7bb7c',	'',	'sakila',	'film_actor',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	5462,	196608,	35,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	1,	105,	0,	NULL,	NULL),
(701,	31,	'7b20c93f-b842-4648-9677-76a71fa4b31e',	'',	'sakila',	'film_category',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	1000,	65536,	65,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	1,	105,	0,	NULL,	NULL),
(702,	31,	'3556ff87-eff9-4618-97a2-6160cfbbd65c',	'',	'sakila',	'film_list',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	1,	105,	0,	NULL,	NULL),
(703,	31,	'ac40e074-21e9-4cf5-82a3-f36238e36cc9',	'',	'sakila',	'film_text',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	0,	16384,	0,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	1,	105,	0,	NULL,	NULL),
(704,	31,	'0ace0049-4fe3-4ad4-9de2-81ca6a54f4fe',	'',	'sakila',	'inventory',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	4581,	180224,	39,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	1,	105,	0,	NULL,	NULL),
(705,	31,	'ae070e34-60e7-4841-8194-2cb93a1db2e4',	'',	'sakila',	'language',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	6,	16384,	2730,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	1,	105,	0,	NULL,	NULL),
(706,	31,	'df74fb04-cf6d-4901-a816-f1321da46cca',	'',	'sakila',	'nicer_but_slower_film_list',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	1,	105,	0,	NULL,	NULL),
(707,	31,	'c1e52b1f-bf4d-4406-a0ed-ea97ce4ad4a1',	'',	'sakila',	'payment',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	15411,	1589248,	103,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	1,	105,	0,	NULL,	NULL),
(708,	31,	'0335781e-7730-4766-b81d-b84982f9a28e',	'',	'sakila',	'rental',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	16424,	1589248,	96,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	1,	105,	0,	NULL,	NULL),
(709,	31,	'2fd013fa-b4d0-4a87-8a30-252093429fd1',	'',	'sakila',	'sales_by_film_category',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	1,	105,	0,	NULL,	NULL),
(710,	31,	'20d09fca-b62a-4101-ba7a-abb2328fc477',	'',	'sakila',	'sales_by_store',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	1,	105,	0,	NULL,	NULL),
(711,	31,	'87930aed-612f-4f2a-8907-07e633179487',	'',	'sakila',	'staff',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	2,	65536,	32768,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	1,	105,	0,	NULL,	NULL),
(712,	31,	'c6cc9c72-efa2-4c9c-88da-88a744ee85cd',	'',	'sakila',	'staff_list',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	1,	105,	0,	NULL,	NULL),
(713,	31,	'94ed88f3-11a0-42c8-aff5-79e1678e9944',	'',	'sakila',	'store',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	2,	16384,	8192,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	1,	105,	0,	NULL,	NULL),
(714,	32,	'777701bd-b6dc-49c3-bee4-536cd39b8d9b',	'',	'sakila',	'actor',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	200,	16384,	81,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	2,	107,	0,	NULL,	1),
(715,	32,	'83ef0949-bbcf-4b78-a6aa-d8cd298ed934',	'',	'sakila',	'actor_info',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	2,	107,	0,	NULL,	1),
(716,	32,	'009c35a7-ee76-4f3c-a240-2d32fdc4a14a',	'',	'sakila',	'address',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	603,	98304,	163,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	2,	107,	0,	NULL,	1),
(717,	32,	'0afdc955-d51d-4368-82ca-04ce178b279d',	'',	'sakila',	'category',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	16,	16384,	1024,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	2,	107,	0,	NULL,	1),
(718,	32,	'6a20b76c-0a60-4bfb-96f9-fad446613453',	'',	'sakila',	'city',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	600,	49152,	81,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	2,	107,	0,	NULL,	1),
(719,	32,	'fbc66a72-db9c-4383-a709-8ecf910f3141',	'',	'sakila',	'country',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	109,	16384,	150,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	2,	107,	0,	NULL,	1),
(720,	32,	'd84621ae-5203-4495-bdd2-bfb619875790',	'',	'sakila',	'customer',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	599,	81920,	136,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	2,	107,	0,	NULL,	1),
(721,	32,	'7a6dfaea-fbc1-45d9-b484-a4c4cc5434a1',	'',	'sakila',	'customer_list',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	2,	107,	0,	NULL,	1),
(722,	32,	'7eb57fa9-5e6e-4cea-a408-efd2a899846b',	'',	'sakila',	'film',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	1000,	196608,	196,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	2,	107,	0,	NULL,	1),
(723,	32,	'5b2335cd-a7bd-462b-887e-135b8c11f034',	'',	'sakila',	'film_actor',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	5462,	196608,	35,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	2,	107,	0,	NULL,	1),
(724,	32,	'b26f26bc-e593-42a5-aa35-2f6fa295c231',	'',	'sakila',	'film_category',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	1000,	65536,	65,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	2,	107,	0,	NULL,	1),
(725,	32,	'7a197be6-4a4b-407d-bfb0-80f5ac725f49',	'',	'sakila',	'film_list',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	2,	107,	0,	NULL,	1),
(726,	32,	'86b0f2e2-ff32-48be-811c-21b1549825fb',	'',	'sakila',	'film_text',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	0,	16384,	0,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	2,	107,	0,	NULL,	1),
(727,	32,	'4d20e730-d7ad-4a24-9f01-b3ee718f5e63',	'',	'sakila',	'inventory',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	4581,	180224,	39,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	2,	107,	0,	NULL,	1),
(728,	32,	'8ed55d7a-2de9-4a02-ac75-96066a79cfcb',	'',	'sakila',	'language',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	6,	16384,	2730,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	2,	107,	0,	NULL,	1),
(729,	32,	'c0343721-eb5b-4e41-8d88-b46b485c3c05',	'',	'sakila',	'nicer_but_slower_film_list',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	2,	107,	0,	NULL,	1),
(730,	32,	'794465a4-a271-49f7-a33b-76a9e3423744',	'',	'sakila',	'payment',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	15411,	1589248,	103,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	2,	107,	0,	NULL,	1),
(731,	32,	'acc0d99b-73b9-45f6-a127-2122170c5ee7',	'',	'sakila',	'rental',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	16424,	1589248,	96,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	2,	107,	0,	NULL,	1),
(732,	32,	'7fd2084a-b676-48c2-a72f-ace74090220a',	'',	'sakila',	'sales_by_film_category',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	2,	107,	0,	NULL,	1),
(733,	32,	'c5c38acd-e1d3-4e7e-90df-16aa81092674',	'',	'sakila',	'sales_by_store',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	2,	107,	0,	NULL,	1),
(734,	32,	'69a9592b-f20c-47ba-b002-494b8a15d7a6',	'',	'sakila',	'staff',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	2,	65536,	32768,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	2,	107,	0,	NULL,	1),
(735,	32,	'cf050587-5d12-42d1-870e-93783c092584',	'',	'sakila',	'staff_list',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	2,	107,	0,	NULL,	1),
(736,	32,	'7061590d-21cc-43dd-9a9b-857ca4bc66d1',	'',	'sakila',	'store',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	2,	16384,	8192,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	2,	107,	0,	NULL,	1),
(737,	33,	'bb07456e-91d7-4369-a262-b169fb9a60b6',	'',	'sakila',	'actor',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	200,	16384,	81,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	3,	108,	0,	NULL,	1),
(738,	33,	'a767df33-fb18-4301-9c87-9aeba1ab2b21',	'',	'sakila',	'actor_info',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	3,	108,	0,	NULL,	1),
(739,	33,	'42267831-42ef-4ae9-aa3a-3b32aa4a176d',	'',	'sakila',	'address',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	603,	98304,	163,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	3,	108,	0,	NULL,	1),
(740,	33,	'a8aa8c22-089b-4845-84a0-33d201a26a31',	'',	'sakila',	'category',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	16,	16384,	1024,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	3,	108,	0,	NULL,	1),
(741,	33,	'c0e1ad07-5912-4656-b323-236035d44084',	'',	'sakila',	'city',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	600,	49152,	81,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	3,	108,	0,	NULL,	1),
(742,	33,	'79f98ee2-6d23-47c5-a89f-861a68ec4d1c',	'',	'sakila',	'country',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	109,	16384,	150,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	3,	108,	0,	NULL,	1),
(743,	33,	'f56cef3a-242d-4084-8fb7-5b8c64eb5d95',	'',	'sakila',	'customer',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	599,	81920,	136,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	3,	108,	0,	NULL,	1),
(744,	33,	'5a633e59-43b1-4de6-a949-2754a72d5e73',	'',	'sakila',	'customer_list',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	3,	108,	0,	NULL,	1),
(745,	33,	'f6fd70dc-8201-4936-9496-03881dbde9c6',	'',	'sakila',	'film',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	1000,	196608,	196,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	3,	108,	0,	NULL,	1),
(746,	33,	'5f41f500-2cca-4a02-bde2-89b1840312b9',	'',	'sakila',	'film_actor',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	5462,	196608,	35,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	3,	108,	0,	NULL,	1),
(747,	33,	'b86c7a1d-1ae1-4795-9a2c-720303625bd8',	'',	'sakila',	'film_category',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	1000,	65536,	65,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	3,	108,	0,	NULL,	1),
(748,	33,	'9f6675fe-20ff-492f-84a8-fd2b79cec806',	'',	'sakila',	'film_list',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	3,	108,	0,	NULL,	1),
(749,	33,	'da49b9d1-8f64-4244-b915-e16937616ed9',	'',	'sakila',	'film_text',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	0,	16384,	0,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	3,	108,	0,	NULL,	1),
(750,	33,	'8e37eee2-b30a-429f-b90c-81dfc5dd1526',	'',	'sakila',	'inventory',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	4581,	180224,	39,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	3,	108,	0,	NULL,	1),
(751,	33,	'381caa12-5291-46bc-aeb5-e73cfcfa2161',	'',	'sakila',	'language',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	6,	16384,	2730,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	3,	108,	0,	NULL,	1),
(752,	33,	'882aae70-0c3b-4a76-9330-95713f4086ee',	'',	'sakila',	'nicer_but_slower_film_list',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	3,	108,	0,	NULL,	1),
(753,	33,	'87108f5e-b1ac-4d83-a6bf-49d50b9d119b',	'',	'sakila',	'payment',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	15411,	1589248,	103,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	3,	108,	0,	NULL,	1),
(754,	33,	'02a0c378-f0f0-4c6c-a44e-db276b6a9941',	'',	'sakila',	'rental',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	16424,	1589248,	96,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	3,	108,	0,	NULL,	1),
(755,	33,	'cecd5aa0-18c4-4e93-b481-f06c26aa07f7',	'',	'sakila',	'sales_by_film_category',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	3,	108,	0,	NULL,	1),
(756,	33,	'4a976c2a-0a2e-4fa0-b8c7-1f97841aa571',	'',	'sakila',	'sales_by_store',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	3,	108,	0,	NULL,	1),
(757,	33,	'989c038e-6d68-4df3-b0e6-9496cd2a7893',	'',	'sakila',	'staff',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	2,	65536,	32768,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	3,	108,	0,	NULL,	1),
(758,	33,	'72073bda-c361-4b7e-9240-5bcdbd8a5ced',	'',	'sakila',	'staff_list',	'VIEW',	'NULL',	0,	'NULL',	0,	0,	0,	0,	0,	NULL,	'NULL',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	3,	108,	0,	NULL,	1),
(759,	33,	'56ed2947-45f4-4d5d-a564-d1b75313115e',	'',	'sakila',	'store',	'BASE TABLE',	'InnoDB',	10,	'Dynamic',	2,	16384,	8192,	0,	0,	NULL,	'utf8mb3_general_ci',	NULL,	'NULL',	'NULL',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	3,	108,	0,	NULL,	1);

DROP TABLE IF EXISTS `client_db_server_table_column`;
CREATE TABLE `client_db_server_table_column` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `client_db_server_table_id` bigint unsigned NOT NULL,
  `table_schema_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `table_database_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `table_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `column_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `ordinal_position` int unsigned NOT NULL DEFAULT '0',
  `is_nullable` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `data_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `character_max_length` int unsigned NOT NULL DEFAULT '0',
  `column_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `column_key` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `column_comment` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `is_deleted` tinyint NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `client_db_server_table_column_client_db_server_table_id_foreign` (`client_db_server_table_id`),
  CONSTRAINT `client_db_server_table_column_client_db_server_table_id_foreign` FOREIGN KEY (`client_db_server_table_id`) REFERENCES `client_db_server_table` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `client_db_server_table_column` (`id`, `client_db_server_table_id`, `table_schema_name`, `table_database_name`, `table_name`, `column_name`, `ordinal_position`, `is_nullable`, `data_type`, `character_max_length`, `column_type`, `column_key`, `column_comment`, `created_at`, `updated_at`, `is_deleted`, `deleted_at`) VALUES
(3829,	668,	'',	'sakila',	'actor',	'actor_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3830,	668,	'',	'sakila',	'actor',	'first_name',	2,	'NO',	'varchar',	45,	'varchar(45)',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3831,	668,	'',	'sakila',	'actor',	'last_name',	3,	'NO',	'varchar',	45,	'varchar(45)',	'MUL',	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3832,	668,	'',	'sakila',	'actor',	'last_update',	4,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3833,	669,	'',	'sakila',	'actor_info',	'actor_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3834,	669,	'',	'sakila',	'actor_info',	'film_info',	4,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3835,	669,	'',	'sakila',	'actor_info',	'first_name',	2,	'NO',	'varchar',	45,	'varchar(45)',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3836,	669,	'',	'sakila',	'actor_info',	'last_name',	3,	'NO',	'varchar',	45,	'varchar(45)',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3837,	670,	'',	'sakila',	'address',	'address',	2,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3838,	670,	'',	'sakila',	'address',	'address2',	3,	'YES',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3839,	670,	'',	'sakila',	'address',	'address_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3840,	670,	'',	'sakila',	'address',	'city_id',	5,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3841,	670,	'',	'sakila',	'address',	'district',	4,	'NO',	'varchar',	20,	'varchar(20)',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3842,	670,	'',	'sakila',	'address',	'last_update',	9,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3843,	670,	'',	'sakila',	'address',	'location',	8,	'NO',	'geometry',	0,	'geometry',	'MUL',	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3844,	670,	'',	'sakila',	'address',	'phone',	7,	'NO',	'varchar',	20,	'varchar(20)',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3845,	670,	'',	'sakila',	'address',	'postal_code',	6,	'YES',	'varchar',	10,	'varchar(10)',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3846,	671,	'',	'sakila',	'category',	'category_id',	1,	'NO',	'tinyint',	0,	'tinyint unsigned',	'PRI',	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3847,	671,	'',	'sakila',	'category',	'last_update',	3,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3848,	671,	'',	'sakila',	'category',	'name',	2,	'NO',	'varchar',	25,	'varchar(25)',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3849,	672,	'',	'sakila',	'city',	'city',	2,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3850,	672,	'',	'sakila',	'city',	'city_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3851,	672,	'',	'sakila',	'city',	'country_id',	3,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3852,	672,	'',	'sakila',	'city',	'last_update',	4,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3853,	673,	'',	'sakila',	'country',	'country',	2,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3854,	673,	'',	'sakila',	'country',	'country_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3855,	673,	'',	'sakila',	'country',	'last_update',	3,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3856,	674,	'',	'sakila',	'customer',	'active',	7,	'NO',	'tinyint',	0,	'tinyint(1)',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3857,	674,	'',	'sakila',	'customer',	'address_id',	6,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3858,	674,	'',	'sakila',	'customer',	'create_date',	8,	'NO',	'datetime',	0,	'datetime',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3859,	674,	'',	'sakila',	'customer',	'customer_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3860,	674,	'',	'sakila',	'customer',	'email',	5,	'YES',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3861,	674,	'',	'sakila',	'customer',	'first_name',	3,	'NO',	'varchar',	45,	'varchar(45)',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3862,	674,	'',	'sakila',	'customer',	'last_name',	4,	'NO',	'varchar',	45,	'varchar(45)',	'MUL',	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3863,	674,	'',	'sakila',	'customer',	'last_update',	9,	'YES',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3864,	674,	'',	'sakila',	'customer',	'store_id',	2,	'NO',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3865,	675,	'',	'sakila',	'customer_list',	'address',	3,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3866,	675,	'',	'sakila',	'customer_list',	'city',	6,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3867,	675,	'',	'sakila',	'customer_list',	'country',	7,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3868,	675,	'',	'sakila',	'customer_list',	'ID',	1,	'NO',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3869,	675,	'',	'sakila',	'customer_list',	'name',	2,	'YES',	'varchar',	91,	'varchar(91)',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3870,	675,	'',	'sakila',	'customer_list',	'notes',	8,	'NO',	'varchar',	6,	'varchar(6)',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3871,	675,	'',	'sakila',	'customer_list',	'phone',	5,	'NO',	'varchar',	20,	'varchar(20)',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3872,	675,	'',	'sakila',	'customer_list',	'SID',	9,	'NO',	'tinyint',	0,	'tinyint unsigned',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3873,	675,	'',	'sakila',	'customer_list',	'zip code',	4,	'YES',	'varchar',	10,	'varchar(10)',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3874,	676,	'',	'sakila',	'film',	'description',	3,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3875,	676,	'',	'sakila',	'film',	'film_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3876,	676,	'',	'sakila',	'film',	'language_id',	5,	'NO',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3877,	676,	'',	'sakila',	'film',	'last_update',	13,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3878,	676,	'',	'sakila',	'film',	'length',	9,	'YES',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3879,	676,	'',	'sakila',	'film',	'original_language_id',	6,	'YES',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3880,	676,	'',	'sakila',	'film',	'rating',	11,	'YES',	'enum',	5,	'enum(\'G\',\'PG\',\'PG-13\',\'R\',\'NC-17\')',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3881,	676,	'',	'sakila',	'film',	'release_year',	4,	'YES',	'year',	0,	'year',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3882,	676,	'',	'sakila',	'film',	'rental_duration',	7,	'NO',	'tinyint',	0,	'tinyint unsigned',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3883,	676,	'',	'sakila',	'film',	'rental_rate',	8,	'NO',	'decimal',	0,	'decimal(4,2)',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3884,	676,	'',	'sakila',	'film',	'replacement_cost',	10,	'NO',	'decimal',	0,	'decimal(5,2)',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3885,	676,	'',	'sakila',	'film',	'special_features',	12,	'YES',	'set',	54,	'set(\'Trailers\',\'Commentaries\',\'Deleted Scenes\',\'Behind the Scenes\')',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3886,	676,	'',	'sakila',	'film',	'title',	2,	'NO',	'varchar',	255,	'varchar(255)',	'MUL',	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3887,	677,	'',	'sakila',	'film_actor',	'actor_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3888,	677,	'',	'sakila',	'film_actor',	'film_id',	2,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3889,	677,	'',	'sakila',	'film_actor',	'last_update',	3,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3890,	678,	'',	'sakila',	'film_category',	'category_id',	2,	'NO',	'tinyint',	0,	'tinyint unsigned',	'PRI',	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3891,	678,	'',	'sakila',	'film_category',	'film_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3892,	678,	'',	'sakila',	'film_category',	'last_update',	3,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3893,	679,	'',	'sakila',	'film_list',	'actors',	8,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3894,	679,	'',	'sakila',	'film_list',	'category',	4,	'NO',	'varchar',	25,	'varchar(25)',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3895,	679,	'',	'sakila',	'film_list',	'description',	3,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3896,	679,	'',	'sakila',	'film_list',	'FID',	1,	'YES',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3897,	679,	'',	'sakila',	'film_list',	'length',	6,	'YES',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3898,	679,	'',	'sakila',	'film_list',	'price',	5,	'YES',	'decimal',	0,	'decimal(4,2)',	NULL,	NULL,	'2024-01-18 17:10:33',	'2024-01-18 17:10:33',	0,	NULL),
(3899,	679,	'',	'sakila',	'film_list',	'rating',	7,	'YES',	'enum',	5,	'enum(\'G\',\'PG\',\'PG-13\',\'R\',\'NC-17\')',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3900,	679,	'',	'sakila',	'film_list',	'title',	2,	'YES',	'varchar',	255,	'varchar(255)',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3901,	680,	'',	'sakila',	'film_text',	'description',	3,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3902,	680,	'',	'sakila',	'film_text',	'film_id',	1,	'NO',	'smallint',	0,	'smallint',	'PRI',	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3903,	680,	'',	'sakila',	'film_text',	'title',	2,	'NO',	'varchar',	255,	'varchar(255)',	'MUL',	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3904,	681,	'',	'sakila',	'inventory',	'film_id',	2,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3905,	681,	'',	'sakila',	'inventory',	'inventory_id',	1,	'NO',	'mediumint',	0,	'mediumint unsigned',	'PRI',	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3906,	681,	'',	'sakila',	'inventory',	'last_update',	4,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3907,	681,	'',	'sakila',	'inventory',	'store_id',	3,	'NO',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3908,	682,	'',	'sakila',	'language',	'language_id',	1,	'NO',	'tinyint',	0,	'tinyint unsigned',	'PRI',	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3909,	682,	'',	'sakila',	'language',	'last_update',	3,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3910,	682,	'',	'sakila',	'language',	'name',	2,	'NO',	'char',	20,	'char(20)',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3911,	683,	'',	'sakila',	'nicer_but_slower_film_list',	'actors',	8,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3912,	683,	'',	'sakila',	'nicer_but_slower_film_list',	'category',	4,	'NO',	'varchar',	25,	'varchar(25)',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3913,	683,	'',	'sakila',	'nicer_but_slower_film_list',	'description',	3,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3914,	683,	'',	'sakila',	'nicer_but_slower_film_list',	'FID',	1,	'YES',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3915,	683,	'',	'sakila',	'nicer_but_slower_film_list',	'length',	6,	'YES',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3916,	683,	'',	'sakila',	'nicer_but_slower_film_list',	'price',	5,	'YES',	'decimal',	0,	'decimal(4,2)',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3917,	683,	'',	'sakila',	'nicer_but_slower_film_list',	'rating',	7,	'YES',	'enum',	5,	'enum(\'G\',\'PG\',\'PG-13\',\'R\',\'NC-17\')',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3918,	683,	'',	'sakila',	'nicer_but_slower_film_list',	'title',	2,	'YES',	'varchar',	255,	'varchar(255)',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3919,	684,	'',	'sakila',	'payment',	'amount',	5,	'NO',	'decimal',	0,	'decimal(5,2)',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3920,	684,	'',	'sakila',	'payment',	'customer_id',	2,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3921,	684,	'',	'sakila',	'payment',	'last_update',	7,	'YES',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3922,	684,	'',	'sakila',	'payment',	'payment_date',	6,	'NO',	'datetime',	0,	'datetime',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3923,	684,	'',	'sakila',	'payment',	'payment_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3924,	684,	'',	'sakila',	'payment',	'rental_id',	4,	'YES',	'int',	0,	'int',	'MUL',	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3925,	684,	'',	'sakila',	'payment',	'staff_id',	3,	'NO',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3926,	685,	'',	'sakila',	'rental',	'customer_id',	4,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3927,	685,	'',	'sakila',	'rental',	'inventory_id',	3,	'NO',	'mediumint',	0,	'mediumint unsigned',	'MUL',	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3928,	685,	'',	'sakila',	'rental',	'last_update',	7,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3929,	685,	'',	'sakila',	'rental',	'rental_date',	2,	'NO',	'datetime',	0,	'datetime',	'MUL',	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3930,	685,	'',	'sakila',	'rental',	'rental_id',	1,	'NO',	'int',	0,	'int',	'PRI',	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3931,	685,	'',	'sakila',	'rental',	'return_date',	5,	'YES',	'datetime',	0,	'datetime',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3932,	685,	'',	'sakila',	'rental',	'staff_id',	6,	'NO',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3933,	686,	'',	'sakila',	'sales_by_film_category',	'category',	1,	'NO',	'varchar',	25,	'varchar(25)',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3934,	686,	'',	'sakila',	'sales_by_film_category',	'total_sales',	2,	'YES',	'decimal',	0,	'decimal(27,2)',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3935,	687,	'',	'sakila',	'sales_by_store',	'manager',	2,	'YES',	'varchar',	91,	'varchar(91)',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3936,	687,	'',	'sakila',	'sales_by_store',	'store',	1,	'YES',	'varchar',	101,	'varchar(101)',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3937,	687,	'',	'sakila',	'sales_by_store',	'total_sales',	3,	'YES',	'decimal',	0,	'decimal(27,2)',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3938,	688,	'',	'sakila',	'staff',	'active',	8,	'NO',	'tinyint',	0,	'tinyint(1)',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3939,	688,	'',	'sakila',	'staff',	'address_id',	4,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3940,	688,	'',	'sakila',	'staff',	'email',	6,	'YES',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3941,	688,	'',	'sakila',	'staff',	'first_name',	2,	'NO',	'varchar',	45,	'varchar(45)',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3942,	688,	'',	'sakila',	'staff',	'last_name',	3,	'NO',	'varchar',	45,	'varchar(45)',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3943,	688,	'',	'sakila',	'staff',	'last_update',	11,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3944,	688,	'',	'sakila',	'staff',	'password',	10,	'YES',	'varchar',	40,	'varchar(40)',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3945,	688,	'',	'sakila',	'staff',	'picture',	5,	'YES',	'blob',	65535,	'blob',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3946,	688,	'',	'sakila',	'staff',	'staff_id',	1,	'NO',	'tinyint',	0,	'tinyint unsigned',	'PRI',	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3947,	688,	'',	'sakila',	'staff',	'store_id',	7,	'NO',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3948,	688,	'',	'sakila',	'staff',	'username',	9,	'NO',	'varchar',	16,	'varchar(16)',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3949,	689,	'',	'sakila',	'staff_list',	'address',	3,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3950,	689,	'',	'sakila',	'staff_list',	'city',	6,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3951,	689,	'',	'sakila',	'staff_list',	'country',	7,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3952,	689,	'',	'sakila',	'staff_list',	'ID',	1,	'NO',	'tinyint',	0,	'tinyint unsigned',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3953,	689,	'',	'sakila',	'staff_list',	'name',	2,	'YES',	'varchar',	91,	'varchar(91)',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3954,	689,	'',	'sakila',	'staff_list',	'phone',	5,	'NO',	'varchar',	20,	'varchar(20)',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3955,	689,	'',	'sakila',	'staff_list',	'SID',	8,	'NO',	'tinyint',	0,	'tinyint unsigned',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3956,	689,	'',	'sakila',	'staff_list',	'zip code',	4,	'YES',	'varchar',	10,	'varchar(10)',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3957,	690,	'',	'sakila',	'store',	'address_id',	3,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3958,	690,	'',	'sakila',	'store',	'last_update',	4,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3959,	690,	'',	'sakila',	'store',	'manager_staff_id',	2,	'NO',	'tinyint',	0,	'tinyint unsigned',	'UNI',	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3960,	690,	'',	'sakila',	'store',	'store_id',	1,	'NO',	'tinyint',	0,	'tinyint unsigned',	'PRI',	NULL,	'2024-01-18 17:10:34',	'2024-01-18 17:10:34',	0,	NULL),
(3961,	691,	'',	'sakila',	'actor',	'actor_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3962,	691,	'',	'sakila',	'actor',	'first_name',	2,	'NO',	'varchar',	45,	'varchar(45)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3963,	691,	'',	'sakila',	'actor',	'last_name',	3,	'NO',	'varchar',	45,	'varchar(45)',	'MUL',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3964,	691,	'',	'sakila',	'actor',	'last_update',	4,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3965,	692,	'',	'sakila',	'actor_info',	'actor_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3966,	692,	'',	'sakila',	'actor_info',	'film_info',	4,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3967,	692,	'',	'sakila',	'actor_info',	'first_name',	2,	'NO',	'varchar',	45,	'varchar(45)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3968,	692,	'',	'sakila',	'actor_info',	'last_name',	3,	'NO',	'varchar',	45,	'varchar(45)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3969,	693,	'',	'sakila',	'address',	'address',	2,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3970,	693,	'',	'sakila',	'address',	'address2',	3,	'YES',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3971,	693,	'',	'sakila',	'address',	'address_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3972,	693,	'',	'sakila',	'address',	'city_id',	5,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3973,	693,	'',	'sakila',	'address',	'district',	4,	'NO',	'varchar',	20,	'varchar(20)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3974,	693,	'',	'sakila',	'address',	'last_update',	9,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3975,	693,	'',	'sakila',	'address',	'location',	8,	'NO',	'geometry',	0,	'geometry',	'MUL',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3976,	693,	'',	'sakila',	'address',	'phone',	7,	'NO',	'varchar',	20,	'varchar(20)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3977,	693,	'',	'sakila',	'address',	'postal_code',	6,	'YES',	'varchar',	10,	'varchar(10)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3978,	694,	'',	'sakila',	'category',	'category_id',	1,	'NO',	'tinyint',	0,	'tinyint unsigned',	'PRI',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3979,	694,	'',	'sakila',	'category',	'last_update',	3,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3980,	694,	'',	'sakila',	'category',	'name',	2,	'NO',	'varchar',	25,	'varchar(25)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3981,	695,	'',	'sakila',	'city',	'city',	2,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3982,	695,	'',	'sakila',	'city',	'city_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3983,	695,	'',	'sakila',	'city',	'country_id',	3,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3984,	695,	'',	'sakila',	'city',	'last_update',	4,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3985,	696,	'',	'sakila',	'country',	'country',	2,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3986,	696,	'',	'sakila',	'country',	'country_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3987,	696,	'',	'sakila',	'country',	'last_update',	3,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3988,	697,	'',	'sakila',	'customer',	'active',	7,	'NO',	'tinyint',	0,	'tinyint(1)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3989,	697,	'',	'sakila',	'customer',	'address_id',	6,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3990,	697,	'',	'sakila',	'customer',	'create_date',	8,	'NO',	'datetime',	0,	'datetime',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3991,	697,	'',	'sakila',	'customer',	'customer_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3992,	697,	'',	'sakila',	'customer',	'email',	5,	'YES',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3993,	697,	'',	'sakila',	'customer',	'first_name',	3,	'NO',	'varchar',	45,	'varchar(45)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3994,	697,	'',	'sakila',	'customer',	'last_name',	4,	'NO',	'varchar',	45,	'varchar(45)',	'MUL',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3995,	697,	'',	'sakila',	'customer',	'last_update',	9,	'YES',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3996,	697,	'',	'sakila',	'customer',	'store_id',	2,	'NO',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3997,	698,	'',	'sakila',	'customer_list',	'address',	3,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3998,	698,	'',	'sakila',	'customer_list',	'city',	6,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(3999,	698,	'',	'sakila',	'customer_list',	'country',	7,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4000,	698,	'',	'sakila',	'customer_list',	'ID',	1,	'NO',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4001,	698,	'',	'sakila',	'customer_list',	'name',	2,	'YES',	'varchar',	91,	'varchar(91)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4002,	698,	'',	'sakila',	'customer_list',	'notes',	8,	'NO',	'varchar',	6,	'varchar(6)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4003,	698,	'',	'sakila',	'customer_list',	'phone',	5,	'NO',	'varchar',	20,	'varchar(20)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4004,	698,	'',	'sakila',	'customer_list',	'SID',	9,	'NO',	'tinyint',	0,	'tinyint unsigned',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4005,	698,	'',	'sakila',	'customer_list',	'zip code',	4,	'YES',	'varchar',	10,	'varchar(10)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4006,	699,	'',	'sakila',	'film',	'description',	3,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4007,	699,	'',	'sakila',	'film',	'film_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4008,	699,	'',	'sakila',	'film',	'language_id',	5,	'NO',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4009,	699,	'',	'sakila',	'film',	'last_update',	13,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4010,	699,	'',	'sakila',	'film',	'length',	9,	'YES',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4011,	699,	'',	'sakila',	'film',	'original_language_id',	6,	'YES',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4012,	699,	'',	'sakila',	'film',	'rating',	11,	'YES',	'enum',	5,	'enum(\'G\',\'PG\',\'PG-13\',\'R\',\'NC-17\')',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4013,	699,	'',	'sakila',	'film',	'release_year',	4,	'YES',	'year',	0,	'year',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4014,	699,	'',	'sakila',	'film',	'rental_duration',	7,	'NO',	'tinyint',	0,	'tinyint unsigned',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4015,	699,	'',	'sakila',	'film',	'rental_rate',	8,	'NO',	'decimal',	0,	'decimal(4,2)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4016,	699,	'',	'sakila',	'film',	'replacement_cost',	10,	'NO',	'decimal',	0,	'decimal(5,2)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4017,	699,	'',	'sakila',	'film',	'special_features',	12,	'YES',	'set',	54,	'set(\'Trailers\',\'Commentaries\',\'Deleted Scenes\',\'Behind the Scenes\')',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4018,	699,	'',	'sakila',	'film',	'title',	2,	'NO',	'varchar',	255,	'varchar(255)',	'MUL',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4019,	700,	'',	'sakila',	'film_actor',	'actor_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4020,	700,	'',	'sakila',	'film_actor',	'film_id',	2,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4021,	700,	'',	'sakila',	'film_actor',	'last_update',	3,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4022,	701,	'',	'sakila',	'film_category',	'category_id',	2,	'NO',	'tinyint',	0,	'tinyint unsigned',	'PRI',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4023,	701,	'',	'sakila',	'film_category',	'film_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4024,	701,	'',	'sakila',	'film_category',	'last_update',	3,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4025,	702,	'',	'sakila',	'film_list',	'actors',	8,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4026,	702,	'',	'sakila',	'film_list',	'category',	4,	'NO',	'varchar',	25,	'varchar(25)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4027,	702,	'',	'sakila',	'film_list',	'description',	3,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4028,	702,	'',	'sakila',	'film_list',	'FID',	1,	'YES',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4029,	702,	'',	'sakila',	'film_list',	'length',	6,	'YES',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4030,	702,	'',	'sakila',	'film_list',	'price',	5,	'YES',	'decimal',	0,	'decimal(4,2)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4031,	702,	'',	'sakila',	'film_list',	'rating',	7,	'YES',	'enum',	5,	'enum(\'G\',\'PG\',\'PG-13\',\'R\',\'NC-17\')',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4032,	702,	'',	'sakila',	'film_list',	'title',	2,	'YES',	'varchar',	255,	'varchar(255)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4033,	703,	'',	'sakila',	'film_text',	'description',	3,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4034,	703,	'',	'sakila',	'film_text',	'film_id',	1,	'NO',	'smallint',	0,	'smallint',	'PRI',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4035,	703,	'',	'sakila',	'film_text',	'title',	2,	'NO',	'varchar',	255,	'varchar(255)',	'MUL',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4036,	704,	'',	'sakila',	'inventory',	'film_id',	2,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4037,	704,	'',	'sakila',	'inventory',	'inventory_id',	1,	'NO',	'mediumint',	0,	'mediumint unsigned',	'PRI',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4038,	704,	'',	'sakila',	'inventory',	'last_update',	4,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4039,	704,	'',	'sakila',	'inventory',	'store_id',	3,	'NO',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4040,	705,	'',	'sakila',	'language',	'language_id',	1,	'NO',	'tinyint',	0,	'tinyint unsigned',	'PRI',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4041,	705,	'',	'sakila',	'language',	'last_update',	3,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4042,	705,	'',	'sakila',	'language',	'name',	2,	'NO',	'char',	20,	'char(20)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4043,	706,	'',	'sakila',	'nicer_but_slower_film_list',	'actors',	8,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4044,	706,	'',	'sakila',	'nicer_but_slower_film_list',	'category',	4,	'NO',	'varchar',	25,	'varchar(25)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4045,	706,	'',	'sakila',	'nicer_but_slower_film_list',	'description',	3,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4046,	706,	'',	'sakila',	'nicer_but_slower_film_list',	'FID',	1,	'YES',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4047,	706,	'',	'sakila',	'nicer_but_slower_film_list',	'length',	6,	'YES',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4048,	706,	'',	'sakila',	'nicer_but_slower_film_list',	'price',	5,	'YES',	'decimal',	0,	'decimal(4,2)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4049,	706,	'',	'sakila',	'nicer_but_slower_film_list',	'rating',	7,	'YES',	'enum',	5,	'enum(\'G\',\'PG\',\'PG-13\',\'R\',\'NC-17\')',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4050,	706,	'',	'sakila',	'nicer_but_slower_film_list',	'title',	2,	'YES',	'varchar',	255,	'varchar(255)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4051,	707,	'',	'sakila',	'payment',	'amount',	5,	'NO',	'decimal',	0,	'decimal(5,2)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4052,	707,	'',	'sakila',	'payment',	'customer_id',	2,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4053,	707,	'',	'sakila',	'payment',	'last_update',	7,	'YES',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4054,	707,	'',	'sakila',	'payment',	'payment_date',	6,	'NO',	'datetime',	0,	'datetime',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4055,	707,	'',	'sakila',	'payment',	'payment_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4056,	707,	'',	'sakila',	'payment',	'rental_id',	4,	'YES',	'int',	0,	'int',	'MUL',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4057,	707,	'',	'sakila',	'payment',	'staff_id',	3,	'NO',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4058,	708,	'',	'sakila',	'rental',	'customer_id',	4,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4059,	708,	'',	'sakila',	'rental',	'inventory_id',	3,	'NO',	'mediumint',	0,	'mediumint unsigned',	'MUL',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4060,	708,	'',	'sakila',	'rental',	'last_update',	7,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4061,	708,	'',	'sakila',	'rental',	'rental_date',	2,	'NO',	'datetime',	0,	'datetime',	'MUL',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4062,	708,	'',	'sakila',	'rental',	'rental_id',	1,	'NO',	'int',	0,	'int',	'PRI',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4063,	708,	'',	'sakila',	'rental',	'return_date',	5,	'YES',	'datetime',	0,	'datetime',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4064,	708,	'',	'sakila',	'rental',	'staff_id',	6,	'NO',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4065,	709,	'',	'sakila',	'sales_by_film_category',	'category',	1,	'NO',	'varchar',	25,	'varchar(25)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4066,	709,	'',	'sakila',	'sales_by_film_category',	'total_sales',	2,	'YES',	'decimal',	0,	'decimal(27,2)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4067,	710,	'',	'sakila',	'sales_by_store',	'manager',	2,	'YES',	'varchar',	91,	'varchar(91)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4068,	710,	'',	'sakila',	'sales_by_store',	'store',	1,	'YES',	'varchar',	101,	'varchar(101)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4069,	710,	'',	'sakila',	'sales_by_store',	'total_sales',	3,	'YES',	'decimal',	0,	'decimal(27,2)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4070,	711,	'',	'sakila',	'staff',	'active',	8,	'NO',	'tinyint',	0,	'tinyint(1)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4071,	711,	'',	'sakila',	'staff',	'address_id',	4,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4072,	711,	'',	'sakila',	'staff',	'email',	6,	'YES',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4073,	711,	'',	'sakila',	'staff',	'first_name',	2,	'NO',	'varchar',	45,	'varchar(45)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4074,	711,	'',	'sakila',	'staff',	'last_name',	3,	'NO',	'varchar',	45,	'varchar(45)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4075,	711,	'',	'sakila',	'staff',	'last_update',	11,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4076,	711,	'',	'sakila',	'staff',	'password',	10,	'YES',	'varchar',	40,	'varchar(40)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4077,	711,	'',	'sakila',	'staff',	'picture',	5,	'YES',	'blob',	65535,	'blob',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4078,	711,	'',	'sakila',	'staff',	'staff_id',	1,	'NO',	'tinyint',	0,	'tinyint unsigned',	'PRI',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4079,	711,	'',	'sakila',	'staff',	'store_id',	7,	'NO',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4080,	711,	'',	'sakila',	'staff',	'username',	9,	'NO',	'varchar',	16,	'varchar(16)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4081,	712,	'',	'sakila',	'staff_list',	'address',	3,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4082,	712,	'',	'sakila',	'staff_list',	'city',	6,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4083,	712,	'',	'sakila',	'staff_list',	'country',	7,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4084,	712,	'',	'sakila',	'staff_list',	'ID',	1,	'NO',	'tinyint',	0,	'tinyint unsigned',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4085,	712,	'',	'sakila',	'staff_list',	'name',	2,	'YES',	'varchar',	91,	'varchar(91)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4086,	712,	'',	'sakila',	'staff_list',	'phone',	5,	'NO',	'varchar',	20,	'varchar(20)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4087,	712,	'',	'sakila',	'staff_list',	'SID',	8,	'NO',	'tinyint',	0,	'tinyint unsigned',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4088,	712,	'',	'sakila',	'staff_list',	'zip code',	4,	'YES',	'varchar',	10,	'varchar(10)',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4089,	713,	'',	'sakila',	'store',	'address_id',	3,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4090,	713,	'',	'sakila',	'store',	'last_update',	4,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4091,	713,	'',	'sakila',	'store',	'manager_staff_id',	2,	'NO',	'tinyint',	0,	'tinyint unsigned',	'UNI',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4092,	713,	'',	'sakila',	'store',	'store_id',	1,	'NO',	'tinyint',	0,	'tinyint unsigned',	'PRI',	NULL,	'2024-01-24 10:18:54',	'2024-01-24 10:18:54',	0,	NULL),
(4093,	714,	'',	'sakila',	'actor',	'actor_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4094,	714,	'',	'sakila',	'actor',	'first_name',	2,	'NO',	'varchar',	45,	'varchar(45)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4095,	714,	'',	'sakila',	'actor',	'last_name',	3,	'NO',	'varchar',	45,	'varchar(45)',	'MUL',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4096,	714,	'',	'sakila',	'actor',	'last_update',	4,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4097,	715,	'',	'sakila',	'actor_info',	'actor_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4098,	715,	'',	'sakila',	'actor_info',	'film_info',	4,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4099,	715,	'',	'sakila',	'actor_info',	'first_name',	2,	'NO',	'varchar',	45,	'varchar(45)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4100,	715,	'',	'sakila',	'actor_info',	'last_name',	3,	'NO',	'varchar',	45,	'varchar(45)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4101,	716,	'',	'sakila',	'address',	'address',	2,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4102,	716,	'',	'sakila',	'address',	'address2',	3,	'YES',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4103,	716,	'',	'sakila',	'address',	'address_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4104,	716,	'',	'sakila',	'address',	'city_id',	5,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4105,	716,	'',	'sakila',	'address',	'district',	4,	'NO',	'varchar',	20,	'varchar(20)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4106,	716,	'',	'sakila',	'address',	'last_update',	9,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4107,	716,	'',	'sakila',	'address',	'location',	8,	'NO',	'geometry',	0,	'geometry',	'MUL',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4108,	716,	'',	'sakila',	'address',	'phone',	7,	'NO',	'varchar',	20,	'varchar(20)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4109,	716,	'',	'sakila',	'address',	'postal_code',	6,	'YES',	'varchar',	10,	'varchar(10)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4110,	717,	'',	'sakila',	'category',	'category_id',	1,	'NO',	'tinyint',	0,	'tinyint unsigned',	'PRI',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4111,	717,	'',	'sakila',	'category',	'last_update',	3,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4112,	717,	'',	'sakila',	'category',	'name',	2,	'NO',	'varchar',	25,	'varchar(25)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4113,	718,	'',	'sakila',	'city',	'city',	2,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4114,	718,	'',	'sakila',	'city',	'city_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4115,	718,	'',	'sakila',	'city',	'country_id',	3,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4116,	718,	'',	'sakila',	'city',	'last_update',	4,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4117,	719,	'',	'sakila',	'country',	'country',	2,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4118,	719,	'',	'sakila',	'country',	'country_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4119,	719,	'',	'sakila',	'country',	'last_update',	3,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4120,	720,	'',	'sakila',	'customer',	'active',	7,	'NO',	'tinyint',	0,	'tinyint(1)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4121,	720,	'',	'sakila',	'customer',	'address_id',	6,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4122,	720,	'',	'sakila',	'customer',	'create_date',	8,	'NO',	'datetime',	0,	'datetime',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4123,	720,	'',	'sakila',	'customer',	'customer_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4124,	720,	'',	'sakila',	'customer',	'email',	5,	'YES',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4125,	720,	'',	'sakila',	'customer',	'first_name',	3,	'NO',	'varchar',	45,	'varchar(45)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4126,	720,	'',	'sakila',	'customer',	'last_name',	4,	'NO',	'varchar',	45,	'varchar(45)',	'MUL',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4127,	720,	'',	'sakila',	'customer',	'last_update',	9,	'YES',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4128,	720,	'',	'sakila',	'customer',	'store_id',	2,	'NO',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4129,	721,	'',	'sakila',	'customer_list',	'address',	3,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4130,	721,	'',	'sakila',	'customer_list',	'city',	6,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4131,	721,	'',	'sakila',	'customer_list',	'country',	7,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4132,	721,	'',	'sakila',	'customer_list',	'ID',	1,	'NO',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4133,	721,	'',	'sakila',	'customer_list',	'name',	2,	'YES',	'varchar',	91,	'varchar(91)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4134,	721,	'',	'sakila',	'customer_list',	'notes',	8,	'NO',	'varchar',	6,	'varchar(6)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4135,	721,	'',	'sakila',	'customer_list',	'phone',	5,	'NO',	'varchar',	20,	'varchar(20)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4136,	721,	'',	'sakila',	'customer_list',	'SID',	9,	'NO',	'tinyint',	0,	'tinyint unsigned',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4137,	721,	'',	'sakila',	'customer_list',	'zip code',	4,	'YES',	'varchar',	10,	'varchar(10)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4138,	722,	'',	'sakila',	'film',	'description',	3,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4139,	722,	'',	'sakila',	'film',	'film_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4140,	722,	'',	'sakila',	'film',	'language_id',	5,	'NO',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4141,	722,	'',	'sakila',	'film',	'last_update',	13,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4142,	722,	'',	'sakila',	'film',	'length',	9,	'YES',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4143,	722,	'',	'sakila',	'film',	'original_language_id',	6,	'YES',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4144,	722,	'',	'sakila',	'film',	'rating',	11,	'YES',	'enum',	5,	'enum(\'G\',\'PG\',\'PG-13\',\'R\',\'NC-17\')',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4145,	722,	'',	'sakila',	'film',	'release_year',	4,	'YES',	'year',	0,	'year',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4146,	722,	'',	'sakila',	'film',	'rental_duration',	7,	'NO',	'tinyint',	0,	'tinyint unsigned',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4147,	722,	'',	'sakila',	'film',	'rental_rate',	8,	'NO',	'decimal',	0,	'decimal(4,2)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4148,	722,	'',	'sakila',	'film',	'replacement_cost',	10,	'NO',	'decimal',	0,	'decimal(5,2)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4149,	722,	'',	'sakila',	'film',	'special_features',	12,	'YES',	'set',	54,	'set(\'Trailers\',\'Commentaries\',\'Deleted Scenes\',\'Behind the Scenes\')',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4150,	722,	'',	'sakila',	'film',	'title',	2,	'NO',	'varchar',	255,	'varchar(255)',	'MUL',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4151,	723,	'',	'sakila',	'film_actor',	'actor_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4152,	723,	'',	'sakila',	'film_actor',	'film_id',	2,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4153,	723,	'',	'sakila',	'film_actor',	'last_update',	3,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4154,	724,	'',	'sakila',	'film_category',	'category_id',	2,	'NO',	'tinyint',	0,	'tinyint unsigned',	'PRI',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4155,	724,	'',	'sakila',	'film_category',	'film_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4156,	724,	'',	'sakila',	'film_category',	'last_update',	3,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4157,	725,	'',	'sakila',	'film_list',	'actors',	8,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4158,	725,	'',	'sakila',	'film_list',	'category',	4,	'NO',	'varchar',	25,	'varchar(25)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4159,	725,	'',	'sakila',	'film_list',	'description',	3,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4160,	725,	'',	'sakila',	'film_list',	'FID',	1,	'YES',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4161,	725,	'',	'sakila',	'film_list',	'length',	6,	'YES',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4162,	725,	'',	'sakila',	'film_list',	'price',	5,	'YES',	'decimal',	0,	'decimal(4,2)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4163,	725,	'',	'sakila',	'film_list',	'rating',	7,	'YES',	'enum',	5,	'enum(\'G\',\'PG\',\'PG-13\',\'R\',\'NC-17\')',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4164,	725,	'',	'sakila',	'film_list',	'title',	2,	'YES',	'varchar',	255,	'varchar(255)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4165,	726,	'',	'sakila',	'film_text',	'description',	3,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4166,	726,	'',	'sakila',	'film_text',	'film_id',	1,	'NO',	'smallint',	0,	'smallint',	'PRI',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4167,	726,	'',	'sakila',	'film_text',	'title',	2,	'NO',	'varchar',	255,	'varchar(255)',	'MUL',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4168,	727,	'',	'sakila',	'inventory',	'film_id',	2,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4169,	727,	'',	'sakila',	'inventory',	'inventory_id',	1,	'NO',	'mediumint',	0,	'mediumint unsigned',	'PRI',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4170,	727,	'',	'sakila',	'inventory',	'last_update',	4,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4171,	727,	'',	'sakila',	'inventory',	'store_id',	3,	'NO',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4172,	728,	'',	'sakila',	'language',	'language_id',	1,	'NO',	'tinyint',	0,	'tinyint unsigned',	'PRI',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4173,	728,	'',	'sakila',	'language',	'last_update',	3,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4174,	728,	'',	'sakila',	'language',	'name',	2,	'NO',	'char',	20,	'char(20)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4175,	729,	'',	'sakila',	'nicer_but_slower_film_list',	'actors',	8,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4176,	729,	'',	'sakila',	'nicer_but_slower_film_list',	'category',	4,	'NO',	'varchar',	25,	'varchar(25)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4177,	729,	'',	'sakila',	'nicer_but_slower_film_list',	'description',	3,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4178,	729,	'',	'sakila',	'nicer_but_slower_film_list',	'FID',	1,	'YES',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4179,	729,	'',	'sakila',	'nicer_but_slower_film_list',	'length',	6,	'YES',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4180,	729,	'',	'sakila',	'nicer_but_slower_film_list',	'price',	5,	'YES',	'decimal',	0,	'decimal(4,2)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4181,	729,	'',	'sakila',	'nicer_but_slower_film_list',	'rating',	7,	'YES',	'enum',	5,	'enum(\'G\',\'PG\',\'PG-13\',\'R\',\'NC-17\')',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4182,	729,	'',	'sakila',	'nicer_but_slower_film_list',	'title',	2,	'YES',	'varchar',	255,	'varchar(255)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4183,	730,	'',	'sakila',	'payment',	'amount',	5,	'NO',	'decimal',	0,	'decimal(5,2)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4184,	730,	'',	'sakila',	'payment',	'customer_id',	2,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4185,	730,	'',	'sakila',	'payment',	'last_update',	7,	'YES',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4186,	730,	'',	'sakila',	'payment',	'payment_date',	6,	'NO',	'datetime',	0,	'datetime',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4187,	730,	'',	'sakila',	'payment',	'payment_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4188,	730,	'',	'sakila',	'payment',	'rental_id',	4,	'YES',	'int',	0,	'int',	'MUL',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4189,	730,	'',	'sakila',	'payment',	'staff_id',	3,	'NO',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4190,	731,	'',	'sakila',	'rental',	'customer_id',	4,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4191,	731,	'',	'sakila',	'rental',	'inventory_id',	3,	'NO',	'mediumint',	0,	'mediumint unsigned',	'MUL',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4192,	731,	'',	'sakila',	'rental',	'last_update',	7,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4193,	731,	'',	'sakila',	'rental',	'rental_date',	2,	'NO',	'datetime',	0,	'datetime',	'MUL',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4194,	731,	'',	'sakila',	'rental',	'rental_id',	1,	'NO',	'int',	0,	'int',	'PRI',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4195,	731,	'',	'sakila',	'rental',	'return_date',	5,	'YES',	'datetime',	0,	'datetime',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4196,	731,	'',	'sakila',	'rental',	'staff_id',	6,	'NO',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4197,	732,	'',	'sakila',	'sales_by_film_category',	'category',	1,	'NO',	'varchar',	25,	'varchar(25)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4198,	732,	'',	'sakila',	'sales_by_film_category',	'total_sales',	2,	'YES',	'decimal',	0,	'decimal(27,2)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4199,	733,	'',	'sakila',	'sales_by_store',	'manager',	2,	'YES',	'varchar',	91,	'varchar(91)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4200,	733,	'',	'sakila',	'sales_by_store',	'store',	1,	'YES',	'varchar',	101,	'varchar(101)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4201,	733,	'',	'sakila',	'sales_by_store',	'total_sales',	3,	'YES',	'decimal',	0,	'decimal(27,2)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4202,	734,	'',	'sakila',	'staff',	'active',	8,	'NO',	'tinyint',	0,	'tinyint(1)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4203,	734,	'',	'sakila',	'staff',	'address_id',	4,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4204,	734,	'',	'sakila',	'staff',	'email',	6,	'YES',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4205,	734,	'',	'sakila',	'staff',	'first_name',	2,	'NO',	'varchar',	45,	'varchar(45)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4206,	734,	'',	'sakila',	'staff',	'last_name',	3,	'NO',	'varchar',	45,	'varchar(45)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4207,	734,	'',	'sakila',	'staff',	'last_update',	11,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4208,	734,	'',	'sakila',	'staff',	'password',	10,	'YES',	'varchar',	40,	'varchar(40)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4209,	734,	'',	'sakila',	'staff',	'picture',	5,	'YES',	'blob',	65535,	'blob',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4210,	734,	'',	'sakila',	'staff',	'staff_id',	1,	'NO',	'tinyint',	0,	'tinyint unsigned',	'PRI',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4211,	734,	'',	'sakila',	'staff',	'store_id',	7,	'NO',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4212,	734,	'',	'sakila',	'staff',	'username',	9,	'NO',	'varchar',	16,	'varchar(16)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4213,	735,	'',	'sakila',	'staff_list',	'address',	3,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4214,	735,	'',	'sakila',	'staff_list',	'city',	6,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4215,	735,	'',	'sakila',	'staff_list',	'country',	7,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4216,	735,	'',	'sakila',	'staff_list',	'ID',	1,	'NO',	'tinyint',	0,	'tinyint unsigned',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4217,	735,	'',	'sakila',	'staff_list',	'name',	2,	'YES',	'varchar',	91,	'varchar(91)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4218,	735,	'',	'sakila',	'staff_list',	'phone',	5,	'NO',	'varchar',	20,	'varchar(20)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4219,	735,	'',	'sakila',	'staff_list',	'SID',	8,	'NO',	'tinyint',	0,	'tinyint unsigned',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4220,	735,	'',	'sakila',	'staff_list',	'zip code',	4,	'YES',	'varchar',	10,	'varchar(10)',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4221,	736,	'',	'sakila',	'store',	'address_id',	3,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4222,	736,	'',	'sakila',	'store',	'last_update',	4,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4223,	736,	'',	'sakila',	'store',	'manager_staff_id',	2,	'NO',	'tinyint',	0,	'tinyint unsigned',	'UNI',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4224,	736,	'',	'sakila',	'store',	'store_id',	1,	'NO',	'tinyint',	0,	'tinyint unsigned',	'PRI',	NULL,	'2024-03-08 15:09:17',	'2024-03-08 15:09:17',	0,	NULL),
(4225,	737,	'',	'sakila',	'actor',	'actor_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4226,	737,	'',	'sakila',	'actor',	'first_name',	2,	'NO',	'varchar',	45,	'varchar(45)',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4227,	737,	'',	'sakila',	'actor',	'last_name',	3,	'NO',	'varchar',	45,	'varchar(45)',	'MUL',	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4228,	737,	'',	'sakila',	'actor',	'last_update',	4,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4229,	738,	'',	'sakila',	'actor_info',	'actor_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4230,	738,	'',	'sakila',	'actor_info',	'film_info',	4,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4231,	738,	'',	'sakila',	'actor_info',	'first_name',	2,	'NO',	'varchar',	45,	'varchar(45)',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4232,	738,	'',	'sakila',	'actor_info',	'last_name',	3,	'NO',	'varchar',	45,	'varchar(45)',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4233,	739,	'',	'sakila',	'address',	'address',	2,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4234,	739,	'',	'sakila',	'address',	'address2',	3,	'YES',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4235,	739,	'',	'sakila',	'address',	'address_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4236,	739,	'',	'sakila',	'address',	'city_id',	5,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4237,	739,	'',	'sakila',	'address',	'district',	4,	'NO',	'varchar',	20,	'varchar(20)',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4238,	739,	'',	'sakila',	'address',	'last_update',	9,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4239,	739,	'',	'sakila',	'address',	'location',	8,	'NO',	'geometry',	0,	'geometry',	'MUL',	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4240,	739,	'',	'sakila',	'address',	'phone',	7,	'NO',	'varchar',	20,	'varchar(20)',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4241,	739,	'',	'sakila',	'address',	'postal_code',	6,	'YES',	'varchar',	10,	'varchar(10)',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4242,	740,	'',	'sakila',	'category',	'category_id',	1,	'NO',	'tinyint',	0,	'tinyint unsigned',	'PRI',	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4243,	740,	'',	'sakila',	'category',	'last_update',	3,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4244,	740,	'',	'sakila',	'category',	'name',	2,	'NO',	'varchar',	25,	'varchar(25)',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4245,	741,	'',	'sakila',	'city',	'city',	2,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4246,	741,	'',	'sakila',	'city',	'city_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4247,	741,	'',	'sakila',	'city',	'country_id',	3,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4248,	741,	'',	'sakila',	'city',	'last_update',	4,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4249,	742,	'',	'sakila',	'country',	'country',	2,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4250,	742,	'',	'sakila',	'country',	'country_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4251,	742,	'',	'sakila',	'country',	'last_update',	3,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4252,	743,	'',	'sakila',	'customer',	'active',	7,	'NO',	'tinyint',	0,	'tinyint(1)',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4253,	743,	'',	'sakila',	'customer',	'address_id',	6,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4254,	743,	'',	'sakila',	'customer',	'create_date',	8,	'NO',	'datetime',	0,	'datetime',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4255,	743,	'',	'sakila',	'customer',	'customer_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4256,	743,	'',	'sakila',	'customer',	'email',	5,	'YES',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4257,	743,	'',	'sakila',	'customer',	'first_name',	3,	'NO',	'varchar',	45,	'varchar(45)',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4258,	743,	'',	'sakila',	'customer',	'last_name',	4,	'NO',	'varchar',	45,	'varchar(45)',	'MUL',	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4259,	743,	'',	'sakila',	'customer',	'last_update',	9,	'YES',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4260,	743,	'',	'sakila',	'customer',	'store_id',	2,	'NO',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-03-08 15:21:14',	'2024-03-08 15:21:14',	0,	NULL),
(4261,	744,	'',	'sakila',	'customer_list',	'address',	3,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4262,	744,	'',	'sakila',	'customer_list',	'city',	6,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4263,	744,	'',	'sakila',	'customer_list',	'country',	7,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4264,	744,	'',	'sakila',	'customer_list',	'ID',	1,	'NO',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4265,	744,	'',	'sakila',	'customer_list',	'name',	2,	'YES',	'varchar',	91,	'varchar(91)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4266,	744,	'',	'sakila',	'customer_list',	'notes',	8,	'NO',	'varchar',	6,	'varchar(6)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4267,	744,	'',	'sakila',	'customer_list',	'phone',	5,	'NO',	'varchar',	20,	'varchar(20)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4268,	744,	'',	'sakila',	'customer_list',	'SID',	9,	'NO',	'tinyint',	0,	'tinyint unsigned',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4269,	744,	'',	'sakila',	'customer_list',	'zip code',	4,	'YES',	'varchar',	10,	'varchar(10)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4270,	745,	'',	'sakila',	'film',	'description',	3,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4271,	745,	'',	'sakila',	'film',	'film_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4272,	745,	'',	'sakila',	'film',	'language_id',	5,	'NO',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4273,	745,	'',	'sakila',	'film',	'last_update',	13,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4274,	745,	'',	'sakila',	'film',	'length',	9,	'YES',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4275,	745,	'',	'sakila',	'film',	'original_language_id',	6,	'YES',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4276,	745,	'',	'sakila',	'film',	'rating',	11,	'YES',	'enum',	5,	'enum(\'G\',\'PG\',\'PG-13\',\'R\',\'NC-17\')',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4277,	745,	'',	'sakila',	'film',	'release_year',	4,	'YES',	'year',	0,	'year',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4278,	745,	'',	'sakila',	'film',	'rental_duration',	7,	'NO',	'tinyint',	0,	'tinyint unsigned',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4279,	745,	'',	'sakila',	'film',	'rental_rate',	8,	'NO',	'decimal',	0,	'decimal(4,2)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4280,	745,	'',	'sakila',	'film',	'replacement_cost',	10,	'NO',	'decimal',	0,	'decimal(5,2)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4281,	745,	'',	'sakila',	'film',	'special_features',	12,	'YES',	'set',	54,	'set(\'Trailers\',\'Commentaries\',\'Deleted Scenes\',\'Behind the Scenes\')',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4282,	745,	'',	'sakila',	'film',	'title',	2,	'NO',	'varchar',	255,	'varchar(255)',	'MUL',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4283,	746,	'',	'sakila',	'film_actor',	'actor_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4284,	746,	'',	'sakila',	'film_actor',	'film_id',	2,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4285,	746,	'',	'sakila',	'film_actor',	'last_update',	3,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4286,	747,	'',	'sakila',	'film_category',	'category_id',	2,	'NO',	'tinyint',	0,	'tinyint unsigned',	'PRI',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4287,	747,	'',	'sakila',	'film_category',	'film_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4288,	747,	'',	'sakila',	'film_category',	'last_update',	3,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4289,	748,	'',	'sakila',	'film_list',	'actors',	8,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4290,	748,	'',	'sakila',	'film_list',	'category',	4,	'NO',	'varchar',	25,	'varchar(25)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4291,	748,	'',	'sakila',	'film_list',	'description',	3,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4292,	748,	'',	'sakila',	'film_list',	'FID',	1,	'YES',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4293,	748,	'',	'sakila',	'film_list',	'length',	6,	'YES',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4294,	748,	'',	'sakila',	'film_list',	'price',	5,	'YES',	'decimal',	0,	'decimal(4,2)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4295,	748,	'',	'sakila',	'film_list',	'rating',	7,	'YES',	'enum',	5,	'enum(\'G\',\'PG\',\'PG-13\',\'R\',\'NC-17\')',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4296,	748,	'',	'sakila',	'film_list',	'title',	2,	'YES',	'varchar',	255,	'varchar(255)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4297,	749,	'',	'sakila',	'film_text',	'description',	3,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4298,	749,	'',	'sakila',	'film_text',	'film_id',	1,	'NO',	'smallint',	0,	'smallint',	'PRI',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4299,	749,	'',	'sakila',	'film_text',	'title',	2,	'NO',	'varchar',	255,	'varchar(255)',	'MUL',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4300,	750,	'',	'sakila',	'inventory',	'film_id',	2,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4301,	750,	'',	'sakila',	'inventory',	'inventory_id',	1,	'NO',	'mediumint',	0,	'mediumint unsigned',	'PRI',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4302,	750,	'',	'sakila',	'inventory',	'last_update',	4,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4303,	750,	'',	'sakila',	'inventory',	'store_id',	3,	'NO',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4304,	751,	'',	'sakila',	'language',	'language_id',	1,	'NO',	'tinyint',	0,	'tinyint unsigned',	'PRI',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4305,	751,	'',	'sakila',	'language',	'last_update',	3,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4306,	751,	'',	'sakila',	'language',	'name',	2,	'NO',	'char',	20,	'char(20)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4307,	752,	'',	'sakila',	'nicer_but_slower_film_list',	'actors',	8,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4308,	752,	'',	'sakila',	'nicer_but_slower_film_list',	'category',	4,	'NO',	'varchar',	25,	'varchar(25)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4309,	752,	'',	'sakila',	'nicer_but_slower_film_list',	'description',	3,	'YES',	'text',	65535,	'text',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4310,	752,	'',	'sakila',	'nicer_but_slower_film_list',	'FID',	1,	'YES',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4311,	752,	'',	'sakila',	'nicer_but_slower_film_list',	'length',	6,	'YES',	'smallint',	0,	'smallint unsigned',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4312,	752,	'',	'sakila',	'nicer_but_slower_film_list',	'price',	5,	'YES',	'decimal',	0,	'decimal(4,2)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4313,	752,	'',	'sakila',	'nicer_but_slower_film_list',	'rating',	7,	'YES',	'enum',	5,	'enum(\'G\',\'PG\',\'PG-13\',\'R\',\'NC-17\')',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4314,	752,	'',	'sakila',	'nicer_but_slower_film_list',	'title',	2,	'YES',	'varchar',	255,	'varchar(255)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4315,	753,	'',	'sakila',	'payment',	'amount',	5,	'NO',	'decimal',	0,	'decimal(5,2)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4316,	753,	'',	'sakila',	'payment',	'customer_id',	2,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4317,	753,	'',	'sakila',	'payment',	'last_update',	7,	'YES',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4318,	753,	'',	'sakila',	'payment',	'payment_date',	6,	'NO',	'datetime',	0,	'datetime',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4319,	753,	'',	'sakila',	'payment',	'payment_id',	1,	'NO',	'smallint',	0,	'smallint unsigned',	'PRI',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4320,	753,	'',	'sakila',	'payment',	'rental_id',	4,	'YES',	'int',	0,	'int',	'MUL',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4321,	753,	'',	'sakila',	'payment',	'staff_id',	3,	'NO',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4322,	754,	'',	'sakila',	'rental',	'customer_id',	4,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4323,	754,	'',	'sakila',	'rental',	'inventory_id',	3,	'NO',	'mediumint',	0,	'mediumint unsigned',	'MUL',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4324,	754,	'',	'sakila',	'rental',	'last_update',	7,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4325,	754,	'',	'sakila',	'rental',	'rental_date',	2,	'NO',	'datetime',	0,	'datetime',	'MUL',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4326,	754,	'',	'sakila',	'rental',	'rental_id',	1,	'NO',	'int',	0,	'int',	'PRI',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4327,	754,	'',	'sakila',	'rental',	'return_date',	5,	'YES',	'datetime',	0,	'datetime',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4328,	754,	'',	'sakila',	'rental',	'staff_id',	6,	'NO',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4329,	755,	'',	'sakila',	'sales_by_film_category',	'category',	1,	'NO',	'varchar',	25,	'varchar(25)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4330,	755,	'',	'sakila',	'sales_by_film_category',	'total_sales',	2,	'YES',	'decimal',	0,	'decimal(27,2)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4331,	756,	'',	'sakila',	'sales_by_store',	'manager',	2,	'YES',	'varchar',	91,	'varchar(91)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4332,	756,	'',	'sakila',	'sales_by_store',	'store',	1,	'YES',	'varchar',	101,	'varchar(101)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4333,	756,	'',	'sakila',	'sales_by_store',	'total_sales',	3,	'YES',	'decimal',	0,	'decimal(27,2)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4334,	757,	'',	'sakila',	'staff',	'active',	8,	'NO',	'tinyint',	0,	'tinyint(1)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4335,	757,	'',	'sakila',	'staff',	'address_id',	4,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4336,	757,	'',	'sakila',	'staff',	'email',	6,	'YES',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4337,	757,	'',	'sakila',	'staff',	'first_name',	2,	'NO',	'varchar',	45,	'varchar(45)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4338,	757,	'',	'sakila',	'staff',	'last_name',	3,	'NO',	'varchar',	45,	'varchar(45)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4339,	757,	'',	'sakila',	'staff',	'last_update',	11,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4340,	757,	'',	'sakila',	'staff',	'password',	10,	'YES',	'varchar',	40,	'varchar(40)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4341,	757,	'',	'sakila',	'staff',	'picture',	5,	'YES',	'blob',	65535,	'blob',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4342,	757,	'',	'sakila',	'staff',	'staff_id',	1,	'NO',	'tinyint',	0,	'tinyint unsigned',	'PRI',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4343,	757,	'',	'sakila',	'staff',	'store_id',	7,	'NO',	'tinyint',	0,	'tinyint unsigned',	'MUL',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4344,	757,	'',	'sakila',	'staff',	'username',	9,	'NO',	'varchar',	16,	'varchar(16)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4345,	758,	'',	'sakila',	'staff_list',	'address',	3,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4346,	758,	'',	'sakila',	'staff_list',	'city',	6,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4347,	758,	'',	'sakila',	'staff_list',	'country',	7,	'NO',	'varchar',	50,	'varchar(50)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4348,	758,	'',	'sakila',	'staff_list',	'ID',	1,	'NO',	'tinyint',	0,	'tinyint unsigned',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4349,	758,	'',	'sakila',	'staff_list',	'name',	2,	'YES',	'varchar',	91,	'varchar(91)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4350,	758,	'',	'sakila',	'staff_list',	'phone',	5,	'NO',	'varchar',	20,	'varchar(20)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4351,	758,	'',	'sakila',	'staff_list',	'SID',	8,	'NO',	'tinyint',	0,	'tinyint unsigned',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4352,	758,	'',	'sakila',	'staff_list',	'zip code',	4,	'YES',	'varchar',	10,	'varchar(10)',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4353,	759,	'',	'sakila',	'store',	'address_id',	3,	'NO',	'smallint',	0,	'smallint unsigned',	'MUL',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4354,	759,	'',	'sakila',	'store',	'last_update',	4,	'NO',	'timestamp',	0,	'timestamp',	NULL,	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4355,	759,	'',	'sakila',	'store',	'manager_staff_id',	2,	'NO',	'tinyint',	0,	'tinyint unsigned',	'UNI',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL),
(4356,	759,	'',	'sakila',	'store',	'store_id',	1,	'NO',	'tinyint',	0,	'tinyint unsigned',	'PRI',	NULL,	'2024-03-08 15:21:15',	'2024-03-08 15:21:15',	0,	NULL);

DROP TABLE IF EXISTS `client_db_server_table_stat`;
CREATE TABLE `client_db_server_table_stat` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `client_db_server_id` bigint unsigned NOT NULL,
  `client_db_server_table_id` bigint unsigned NOT NULL,
  `dim_date_id` bigint unsigned NOT NULL,
  `company_id` bigint unsigned NOT NULL,
  `stat_date` date NOT NULL,
  `total_table_rows` bigint unsigned NOT NULL DEFAULT '0',
  `total_table_rows_archived` bigint unsigned NOT NULL DEFAULT '0',
  `total_db_storage_setup` bigint unsigned NOT NULL DEFAULT '0',
  `total_table_size` bigint unsigned NOT NULL DEFAULT '0',
  `total_table_index` bigint unsigned NOT NULL DEFAULT '0',
  `total_db_storage_setup_cost` decimal(8,2) unsigned NOT NULL DEFAULT '0.00',
  `total_table_data_size_archived` bigint unsigned NOT NULL DEFAULT '0',
  `total_table_object_storage_used` bigint unsigned NOT NULL DEFAULT '0',
  `total_table_storage_cost_saving` decimal(6,2) unsigned NOT NULL DEFAULT '0.00',
  `is_deleted` tinyint NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `client_database_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `client_db_server_table_stat_dim_date_id_foreign` (`dim_date_id`),
  KEY `client_db_server_table_stat_client_db_server_id_foreign` (`client_db_server_id`),
  KEY `client_db_server_table_stat_company_id_foreign` (`company_id`),
  KEY `client_db_server_table_stat_client_database_id_foreign` (`client_database_id`),
  KEY `client_db_server_table_stat_client_db_server_table_id_foreign` (`client_db_server_table_id`),
  CONSTRAINT `client_db_server_table_stat_client_database_id_foreign` FOREIGN KEY (`client_database_id`) REFERENCES `client_database` (`id`) ON DELETE CASCADE,
  CONSTRAINT `client_db_server_table_stat_client_db_server_id_foreign` FOREIGN KEY (`client_db_server_id`) REFERENCES `client_db_server` (`id`) ON DELETE CASCADE,
  CONSTRAINT `client_db_server_table_stat_client_db_server_table_id_foreign` FOREIGN KEY (`client_db_server_table_id`) REFERENCES `client_db_server_table` (`id`) ON DELETE CASCADE,
  CONSTRAINT `client_db_server_table_stat_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `company` (`id`) ON DELETE CASCADE,
  CONSTRAINT `client_db_server_table_stat_dim_date_id_foreign` FOREIGN KEY (`dim_date_id`) REFERENCES `dim_date` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `client_db_server_type`;
CREATE TABLE `client_db_server_type` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `client_db_server_type_name_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `client_db_server_type` (`id`, `name`, `is_active`, `deleted_at`, `created_at`, `updated_at`) VALUES
(1,	'MySQL',	1,	NULL,	'2023-12-21 17:54:45',	'2023-12-21 17:54:45');

DROP TABLE IF EXISTS `client_object_storage`;
CREATE TABLE `client_object_storage` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `bucket_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `object_storage_type_id` tinyint unsigned NOT NULL,
  `company_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `client_object_storage_object_storage_type_id_foreign` (`object_storage_type_id`),
  KEY `client_object_storage_company_id_foreign` (`company_id`),
  CONSTRAINT `client_object_storage_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `company` (`id`) ON DELETE CASCADE,
  CONSTRAINT `client_object_storage_object_storage_type_id_foreign` FOREIGN KEY (`object_storage_type_id`) REFERENCES `object_storage_type` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `client_object_storage` (`id`, `bucket_name`, `object_storage_type_id`, `company_id`, `created_at`, `updated_at`) VALUES
(1,	'elysium-ai-sample-bucket',	1,	17,	'2024-02-26 12:37:24',	'2024-02-26 12:37:24'),
(2,	'Test Bucket',	6,	6,	'2024-02-28 09:32:14',	'2024-02-28 09:32:14'),
(3,	'Bucket One',	7,	8,	'2024-02-29 17:25:42',	'2024-04-23 13:29:56');

DROP TABLE IF EXISTS `company`;
CREATE TABLE `company` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `company_address` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `company_address2` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `company_city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `company_postalcode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `country_id` bigint unsigned NOT NULL,
  `state_id` bigint unsigned NOT NULL,
  `database_server_option_id` bigint unsigned NOT NULL,
  `database_table_option_id` bigint unsigned NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `company_company_name_unique` (`company_name`),
  KEY `company_country_id_foreign` (`country_id`),
  KEY `company_state_id_foreign` (`state_id`),
  KEY `company_database_server_option_id_foreign` (`database_server_option_id`),
  KEY `company_database_table_option_id_foreign` (`database_table_option_id`),
  CONSTRAINT `company_country_id_foreign` FOREIGN KEY (`country_id`) REFERENCES `country` (`id`) ON DELETE CASCADE,
  CONSTRAINT `company_database_server_option_id_foreign` FOREIGN KEY (`database_server_option_id`) REFERENCES `database_server_option` (`id`) ON DELETE CASCADE,
  CONSTRAINT `company_database_table_option_id_foreign` FOREIGN KEY (`database_table_option_id`) REFERENCES `database_table_option` (`id`) ON DELETE CASCADE,
  CONSTRAINT `company_state_id_foreign` FOREIGN KEY (`state_id`) REFERENCES `state` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `company` (`id`, `company_name`, `company_address`, `company_address2`, `company_city`, `company_postalcode`, `country_id`, `state_id`, `database_server_option_id`, `database_table_option_id`, `is_active`, `deleted_at`, `created_at`, `updated_at`) VALUES
(1,	'Gigalabs78',	'Test address 1',	NULL,	'Lahore',	'54000',	1,	1,	1,	1,	0,	NULL,	'2023-12-21 16:40:09',	'2023-12-21 16:40:09'),
(2,	'Gigalabs',	'502 newyork',	NULL,	'Brooklyn',	'11225',	1,	12,	1,	1,	0,	NULL,	'2024-01-01 12:51:46',	'2024-01-01 13:01:11'),
(3,	'Gigalabs333',	'test 1',	NULL,	'saa',	'54000',	1,	2,	2,	1,	0,	NULL,	'2024-01-02 10:49:54',	'2024-01-02 10:49:54'),
(4,	'Gigalabs4455',	'test 1',	NULL,	'saa',	'54000',	1,	2,	1,	1,	0,	NULL,	'2024-01-02 11:03:12',	'2024-01-02 11:03:12'),
(5,	'Gigalabs222',	'test 1',	NULL,	'saa',	'54000',	1,	2,	1,	1,	0,	NULL,	'2024-01-02 12:05:59',	'2024-01-02 12:05:59'),
(6,	'Gigalabse44',	'test 1',	NULL,	'saa',	'54000',	1,	4,	1,	1,	0,	NULL,	'2024-01-02 12:09:58',	'2024-01-02 12:09:58'),
(7,	'Gigalabs1',	'502 newyork',	NULL,	'Brooklyn',	'11225',	1,	12,	1,	1,	0,	NULL,	'2024-01-02 12:12:44',	'2024-01-02 12:12:44'),
(8,	'Data Sleek',	'Address 1',	NULL,	'Alabama',	'54000',	1,	1,	1,	1,	0,	NULL,	'2024-01-04 17:05:58',	'2024-01-04 17:05:58'),
(9,	'Gigalabssd',	'4250  Ocala Street, Orlando',	NULL,	'Orlando',	'32801',	1,	3,	2,	2,	0,	NULL,	'2024-01-05 11:13:28',	'2024-01-26 10:08:15'),
(10,	'Gigalabs11',	'502 newyork',	NULL,	'Brooklyn',	'11225',	1,	33,	1,	1,	0,	NULL,	'2024-01-24 10:47:40',	'2024-01-24 10:47:40'),
(11,	'Gigalabsss',	'4250  Ocala Street, Orlando',	NULL,	'Orlando',	'32801',	1,	3,	1,	1,	0,	NULL,	'2024-01-24 10:53:32',	'2024-01-24 10:53:32'),
(12,	'Gigalab',	'4250  Ocala Street, Orlando',	NULL,	'Orlando',	'32801',	1,	3,	1,	1,	0,	NULL,	'2024-01-24 11:12:24',	'2024-01-24 11:12:24'),
(13,	'Gigalabs1211',	'502 newyork',	NULL,	'Brooklyn',	'11225',	1,	33,	1,	5,	0,	NULL,	'2024-01-24 12:54:50',	'2024-01-24 12:54:50'),
(14,	'Data Sleek Test',	'Address 1',	NULL,	'Albama',	'54005',	1,	2,	1,	1,	0,	NULL,	'2024-01-25 14:15:00',	'2024-01-25 14:15:00'),
(15,	'Gigalabssss',	'4250  Ocala Street, Orlando',	NULL,	'Orlando',	'32801',	1,	3,	1,	1,	0,	NULL,	'2024-01-25 15:30:04',	'2024-01-25 15:30:04'),
(16,	'Dev Team',	'DEV Team',	NULL,	'Albama',	'54000',	1,	2,	3,	3,	0,	NULL,	'2024-01-29 08:41:18',	'2024-01-29 08:41:18'),
(17,	'Gigalabs.co',	'2nd Floor, 409-G4 Phase 2 Johar Town',	NULL,	'Lahore',	'54000',	1,	33,	1,	1,	0,	NULL,	'2024-01-31 09:52:55',	'2024-01-31 09:52:55'),
(18,	'sdqdq',	'qwdqwe',	NULL,	'asdqw',	'54000',	1,	3,	2,	2,	0,	NULL,	'2024-02-06 14:41:55',	'2024-02-06 14:41:55'),
(19,	'asdad',	'sadad',	NULL,	'asdad',	'540000',	1,	2,	2,	1,	0,	NULL,	'2024-02-06 14:43:07',	'2024-02-06 14:43:07'),
(20,	'Test Company',	'test address',	NULL,	'Test',	'54000',	1,	3,	2,	1,	0,	NULL,	'2024-02-16 13:45:11',	'2024-02-16 13:45:11'),
(21,	'Gigalabs2',	'4250  Ocala Street, Orlando',	NULL,	'Orlando',	'32801',	1,	3,	1,	2,	0,	NULL,	'2024-03-19 09:49:15',	'2024-03-19 09:49:15'),
(22,	'test',	'test',	NULL,	'Lahore',	'54000',	1,	2,	1,	1,	0,	NULL,	'2024-04-18 22:14:31',	'2024-04-18 22:14:31');

DROP TABLE IF EXISTS `country`;
CREATE TABLE `country` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `country_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `country_country_name_unique` (`country_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `country` (`id`, `country_name`, `is_active`, `deleted_at`, `created_at`, `updated_at`) VALUES
(1,	'United States',	1,	NULL,	'2023-12-21 13:55:48',	'2023-12-21 13:55:48');

DROP TABLE IF EXISTS `database_job_schedule`;
CREATE TABLE `database_job_schedule` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `client_db_server_id` bigint unsigned NOT NULL,
  `server_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `database_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `client_database_id` bigint unsigned NOT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `database_job_schedule_client_db_server_id_foreign` (`client_db_server_id`),
  KEY `database_job_schedule_client_database_id_foreign` (`client_database_id`),
  CONSTRAINT `database_job_schedule_client_database_id_foreign` FOREIGN KEY (`client_database_id`) REFERENCES `client_database` (`id`) ON DELETE CASCADE,
  CONSTRAINT `database_job_schedule_client_db_server_id_foreign` FOREIGN KEY (`client_db_server_id`) REFERENCES `client_db_server` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `database_server_option`;
CREATE TABLE `database_server_option` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `database_server_option_name_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `database_server_option` (`id`, `name`, `is_active`, `deleted_at`, `created_at`, `updated_at`) VALUES
(1,	'1-5',	1,	NULL,	'2023-12-21 13:56:32',	'2023-12-21 13:56:32'),
(2,	'6-10',	1,	NULL,	'2023-12-21 13:56:33',	'2023-12-21 13:56:33'),
(3,	'11-15',	1,	NULL,	'2023-12-21 13:56:34',	'2023-12-21 13:56:34'),
(4,	'16-20',	1,	NULL,	'2023-12-21 13:56:35',	'2023-12-21 13:56:35'),
(5,	'21-25',	1,	NULL,	'2023-12-21 13:56:36',	'2023-12-21 13:56:36');

DROP TABLE IF EXISTS `database_table_option`;
CREATE TABLE `database_table_option` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `database_table_option_name_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `database_table_option` (`id`, `name`, `is_active`, `deleted_at`, `created_at`, `updated_at`) VALUES
(1,	'1-200',	1,	NULL,	'2023-12-21 13:56:37',	'2023-12-21 13:56:37'),
(2,	'201-400',	1,	NULL,	'2023-12-21 13:56:38',	'2023-12-21 13:56:38'),
(3,	'401-600',	1,	NULL,	'2023-12-21 13:56:38',	'2023-12-21 13:56:38'),
(4,	'601-800',	1,	NULL,	'2023-12-21 13:56:39',	'2023-12-21 13:56:39'),
(5,	'801-1000',	1,	NULL,	'2023-12-21 13:56:40',	'2023-12-21 13:56:40');

DROP TABLE IF EXISTS `dim_date`;
CREATE TABLE `dim_date` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL,
  `day_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `week_number` tinyint NOT NULL,
  `week_name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `month_number` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `month_name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `quarter` char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `year` smallint NOT NULL,
  `is_deleted` tinyint NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unq_dim_date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `dim_date` (`id`, `date`, `day_name`, `week_number`, `week_name`, `month_number`, `month_name`, `quarter`, `year`, `is_deleted`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1,	'2023-01-01',	'Sunday',	0,	'Week 0',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:02',	'2024-02-22 12:37:02',	NULL),
(2,	'2023-01-02',	'Monday',	1,	'Week 1',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(3,	'2023-01-03',	'Tuesday',	1,	'Week 1',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(4,	'2023-01-04',	'Wednesday',	1,	'Week 1',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(5,	'2023-01-05',	'Thursday',	1,	'Week 1',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(6,	'2023-01-06',	'Friday',	1,	'Week 1',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(7,	'2023-01-07',	'Saturday',	1,	'Week 1',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(8,	'2023-01-08',	'Sunday',	1,	'Week 1',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(9,	'2023-01-09',	'Monday',	2,	'Week 2',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(10,	'2023-01-10',	'Tuesday',	2,	'Week 2',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(11,	'2023-01-11',	'Wednesday',	2,	'Week 2',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(12,	'2023-01-12',	'Thursday',	2,	'Week 2',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(13,	'2023-01-13',	'Friday',	2,	'Week 2',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(14,	'2023-01-14',	'Saturday',	2,	'Week 2',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(15,	'2023-01-15',	'Sunday',	2,	'Week 2',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(16,	'2023-01-16',	'Monday',	3,	'Week 3',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(17,	'2023-01-17',	'Tuesday',	3,	'Week 3',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(18,	'2023-01-18',	'Wednesday',	3,	'Week 3',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(19,	'2023-01-19',	'Thursday',	3,	'Week 3',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(20,	'2023-01-20',	'Friday',	3,	'Week 3',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(21,	'2023-01-21',	'Saturday',	3,	'Week 3',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(22,	'2023-01-22',	'Sunday',	3,	'Week 3',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(23,	'2023-01-23',	'Monday',	4,	'Week 4',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(24,	'2023-01-24',	'Tuesday',	4,	'Week 4',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(25,	'2023-01-25',	'Wednesday',	4,	'Week 4',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(26,	'2023-01-26',	'Thursday',	4,	'Week 4',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(27,	'2023-01-27',	'Friday',	4,	'Week 4',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(28,	'2023-01-28',	'Saturday',	4,	'Week 4',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(29,	'2023-01-29',	'Sunday',	4,	'Week 4',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(30,	'2023-01-30',	'Monday',	5,	'Week 5',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(31,	'2023-01-31',	'Tuesday',	5,	'Week 5',	'1',	'January',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(32,	'2023-02-01',	'Wednesday',	5,	'Week 5',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(33,	'2023-02-02',	'Thursday',	5,	'Week 5',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(34,	'2023-02-03',	'Friday',	5,	'Week 5',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(35,	'2023-02-04',	'Saturday',	5,	'Week 5',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(36,	'2023-02-05',	'Sunday',	5,	'Week 5',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(37,	'2023-02-06',	'Monday',	6,	'Week 6',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(38,	'2023-02-07',	'Tuesday',	6,	'Week 6',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(39,	'2023-02-08',	'Wednesday',	6,	'Week 6',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(40,	'2023-02-09',	'Thursday',	6,	'Week 6',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(41,	'2023-02-10',	'Friday',	6,	'Week 6',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(42,	'2023-02-11',	'Saturday',	6,	'Week 6',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(43,	'2023-02-12',	'Sunday',	6,	'Week 6',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(44,	'2023-02-13',	'Monday',	7,	'Week 7',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(45,	'2023-02-14',	'Tuesday',	7,	'Week 7',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(46,	'2023-02-15',	'Wednesday',	7,	'Week 7',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(47,	'2023-02-16',	'Thursday',	7,	'Week 7',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(48,	'2023-02-17',	'Friday',	7,	'Week 7',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(49,	'2023-02-18',	'Saturday',	7,	'Week 7',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(50,	'2023-02-19',	'Sunday',	7,	'Week 7',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(51,	'2023-02-20',	'Monday',	8,	'Week 8',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(52,	'2023-02-21',	'Tuesday',	8,	'Week 8',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(53,	'2023-02-22',	'Wednesday',	8,	'Week 8',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(54,	'2023-02-23',	'Thursday',	8,	'Week 8',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(55,	'2023-02-24',	'Friday',	8,	'Week 8',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(56,	'2023-02-25',	'Saturday',	8,	'Week 8',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(57,	'2023-02-26',	'Sunday',	8,	'Week 8',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(58,	'2023-02-27',	'Monday',	9,	'Week 9',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(59,	'2023-02-28',	'Tuesday',	9,	'Week 9',	'2',	'February',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(60,	'2023-03-01',	'Wednesday',	9,	'Week 9',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(61,	'2023-03-02',	'Thursday',	9,	'Week 9',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(62,	'2023-03-03',	'Friday',	9,	'Week 9',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(63,	'2023-03-04',	'Saturday',	9,	'Week 9',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(64,	'2023-03-05',	'Sunday',	9,	'Week 9',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(65,	'2023-03-06',	'Monday',	10,	'Week 10',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(66,	'2023-03-07',	'Tuesday',	10,	'Week 10',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(67,	'2023-03-08',	'Wednesday',	10,	'Week 10',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(68,	'2023-03-09',	'Thursday',	10,	'Week 10',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(69,	'2023-03-10',	'Friday',	10,	'Week 10',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(70,	'2023-03-11',	'Saturday',	10,	'Week 10',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(71,	'2023-03-12',	'Sunday',	10,	'Week 10',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(72,	'2023-03-13',	'Monday',	11,	'Week 11',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(73,	'2023-03-14',	'Tuesday',	11,	'Week 11',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(74,	'2023-03-15',	'Wednesday',	11,	'Week 11',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(75,	'2023-03-16',	'Thursday',	11,	'Week 11',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(76,	'2023-03-17',	'Friday',	11,	'Week 11',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(77,	'2023-03-18',	'Saturday',	11,	'Week 11',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(78,	'2023-03-19',	'Sunday',	11,	'Week 11',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(79,	'2023-03-20',	'Monday',	12,	'Week 12',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(80,	'2023-03-21',	'Tuesday',	12,	'Week 12',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(81,	'2023-03-22',	'Wednesday',	12,	'Week 12',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(82,	'2023-03-23',	'Thursday',	12,	'Week 12',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(83,	'2023-03-24',	'Friday',	12,	'Week 12',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(84,	'2023-03-25',	'Saturday',	12,	'Week 12',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(85,	'2023-03-26',	'Sunday',	12,	'Week 12',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(86,	'2023-03-27',	'Monday',	13,	'Week 13',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(87,	'2023-03-28',	'Tuesday',	13,	'Week 13',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(88,	'2023-03-29',	'Wednesday',	13,	'Week 13',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(89,	'2023-03-30',	'Thursday',	13,	'Week 13',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(90,	'2023-03-31',	'Friday',	13,	'Week 13',	'3',	'March',	'Q1',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(91,	'2023-04-01',	'Saturday',	13,	'Week 13',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(92,	'2023-04-02',	'Sunday',	13,	'Week 13',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(93,	'2023-04-03',	'Monday',	14,	'Week 14',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(94,	'2023-04-04',	'Tuesday',	14,	'Week 14',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(95,	'2023-04-05',	'Wednesday',	14,	'Week 14',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(96,	'2023-04-06',	'Thursday',	14,	'Week 14',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(97,	'2023-04-07',	'Friday',	14,	'Week 14',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(98,	'2023-04-08',	'Saturday',	14,	'Week 14',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(99,	'2023-04-09',	'Sunday',	14,	'Week 14',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(100,	'2023-04-10',	'Monday',	15,	'Week 15',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(101,	'2023-04-11',	'Tuesday',	15,	'Week 15',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(102,	'2023-04-12',	'Wednesday',	15,	'Week 15',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(103,	'2023-04-13',	'Thursday',	15,	'Week 15',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(104,	'2023-04-14',	'Friday',	15,	'Week 15',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(105,	'2023-04-15',	'Saturday',	15,	'Week 15',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(106,	'2023-04-16',	'Sunday',	15,	'Week 15',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(107,	'2023-04-17',	'Monday',	16,	'Week 16',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(108,	'2023-04-18',	'Tuesday',	16,	'Week 16',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(109,	'2023-04-19',	'Wednesday',	16,	'Week 16',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(110,	'2023-04-20',	'Thursday',	16,	'Week 16',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(111,	'2023-04-21',	'Friday',	16,	'Week 16',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(112,	'2023-04-22',	'Saturday',	16,	'Week 16',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(113,	'2023-04-23',	'Sunday',	16,	'Week 16',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(114,	'2023-04-24',	'Monday',	17,	'Week 17',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(115,	'2023-04-25',	'Tuesday',	17,	'Week 17',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(116,	'2023-04-26',	'Wednesday',	17,	'Week 17',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(117,	'2023-04-27',	'Thursday',	17,	'Week 17',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(118,	'2023-04-28',	'Friday',	17,	'Week 17',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(119,	'2023-04-29',	'Saturday',	17,	'Week 17',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(120,	'2023-04-30',	'Sunday',	17,	'Week 17',	'4',	'April',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(121,	'2023-05-01',	'Monday',	18,	'Week 18',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(122,	'2023-05-02',	'Tuesday',	18,	'Week 18',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(123,	'2023-05-03',	'Wednesday',	18,	'Week 18',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(124,	'2023-05-04',	'Thursday',	18,	'Week 18',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(125,	'2023-05-05',	'Friday',	18,	'Week 18',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(126,	'2023-05-06',	'Saturday',	18,	'Week 18',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(127,	'2023-05-07',	'Sunday',	18,	'Week 18',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(128,	'2023-05-08',	'Monday',	19,	'Week 19',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(129,	'2023-05-09',	'Tuesday',	19,	'Week 19',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(130,	'2023-05-10',	'Wednesday',	19,	'Week 19',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(131,	'2023-05-11',	'Thursday',	19,	'Week 19',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(132,	'2023-05-12',	'Friday',	19,	'Week 19',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(133,	'2023-05-13',	'Saturday',	19,	'Week 19',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(134,	'2023-05-14',	'Sunday',	19,	'Week 19',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(135,	'2023-05-15',	'Monday',	20,	'Week 20',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(136,	'2023-05-16',	'Tuesday',	20,	'Week 20',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(137,	'2023-05-17',	'Wednesday',	20,	'Week 20',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(138,	'2023-05-18',	'Thursday',	20,	'Week 20',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(139,	'2023-05-19',	'Friday',	20,	'Week 20',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(140,	'2023-05-20',	'Saturday',	20,	'Week 20',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(141,	'2023-05-21',	'Sunday',	20,	'Week 20',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(142,	'2023-05-22',	'Monday',	21,	'Week 21',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(143,	'2023-05-23',	'Tuesday',	21,	'Week 21',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(144,	'2023-05-24',	'Wednesday',	21,	'Week 21',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(145,	'2023-05-25',	'Thursday',	21,	'Week 21',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(146,	'2023-05-26',	'Friday',	21,	'Week 21',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(147,	'2023-05-27',	'Saturday',	21,	'Week 21',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(148,	'2023-05-28',	'Sunday',	21,	'Week 21',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(149,	'2023-05-29',	'Monday',	22,	'Week 22',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(150,	'2023-05-30',	'Tuesday',	22,	'Week 22',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(151,	'2023-05-31',	'Wednesday',	22,	'Week 22',	'5',	'May',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(152,	'2023-06-01',	'Thursday',	22,	'Week 22',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(153,	'2023-06-02',	'Friday',	22,	'Week 22',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(154,	'2023-06-03',	'Saturday',	22,	'Week 22',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(155,	'2023-06-04',	'Sunday',	22,	'Week 22',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(156,	'2023-06-05',	'Monday',	23,	'Week 23',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(157,	'2023-06-06',	'Tuesday',	23,	'Week 23',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(158,	'2023-06-07',	'Wednesday',	23,	'Week 23',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(159,	'2023-06-08',	'Thursday',	23,	'Week 23',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(160,	'2023-06-09',	'Friday',	23,	'Week 23',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(161,	'2023-06-10',	'Saturday',	23,	'Week 23',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(162,	'2023-06-11',	'Sunday',	23,	'Week 23',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(163,	'2023-06-12',	'Monday',	24,	'Week 24',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(164,	'2023-06-13',	'Tuesday',	24,	'Week 24',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(165,	'2023-06-14',	'Wednesday',	24,	'Week 24',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(166,	'2023-06-15',	'Thursday',	24,	'Week 24',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(167,	'2023-06-16',	'Friday',	24,	'Week 24',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(168,	'2023-06-17',	'Saturday',	24,	'Week 24',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(169,	'2023-06-18',	'Sunday',	24,	'Week 24',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(170,	'2023-06-19',	'Monday',	25,	'Week 25',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(171,	'2023-06-20',	'Tuesday',	25,	'Week 25',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(172,	'2023-06-21',	'Wednesday',	25,	'Week 25',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(173,	'2023-06-22',	'Thursday',	25,	'Week 25',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(174,	'2023-06-23',	'Friday',	25,	'Week 25',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(175,	'2023-06-24',	'Saturday',	25,	'Week 25',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(176,	'2023-06-25',	'Sunday',	25,	'Week 25',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(177,	'2023-06-26',	'Monday',	26,	'Week 26',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(178,	'2023-06-27',	'Tuesday',	26,	'Week 26',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(179,	'2023-06-28',	'Wednesday',	26,	'Week 26',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(180,	'2023-06-29',	'Thursday',	26,	'Week 26',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(181,	'2023-06-30',	'Friday',	26,	'Week 26',	'6',	'June',	'Q2',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(182,	'2023-07-01',	'Saturday',	26,	'Week 26',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(183,	'2023-07-02',	'Sunday',	26,	'Week 26',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(184,	'2023-07-03',	'Monday',	27,	'Week 27',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(185,	'2023-07-04',	'Tuesday',	27,	'Week 27',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(186,	'2023-07-05',	'Wednesday',	27,	'Week 27',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(187,	'2023-07-06',	'Thursday',	27,	'Week 27',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(188,	'2023-07-07',	'Friday',	27,	'Week 27',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(189,	'2023-07-08',	'Saturday',	27,	'Week 27',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(190,	'2023-07-09',	'Sunday',	27,	'Week 27',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(191,	'2023-07-10',	'Monday',	28,	'Week 28',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(192,	'2023-07-11',	'Tuesday',	28,	'Week 28',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(193,	'2023-07-12',	'Wednesday',	28,	'Week 28',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(194,	'2023-07-13',	'Thursday',	28,	'Week 28',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(195,	'2023-07-14',	'Friday',	28,	'Week 28',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(196,	'2023-07-15',	'Saturday',	28,	'Week 28',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(197,	'2023-07-16',	'Sunday',	28,	'Week 28',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(198,	'2023-07-17',	'Monday',	29,	'Week 29',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(199,	'2023-07-18',	'Tuesday',	29,	'Week 29',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(200,	'2023-07-19',	'Wednesday',	29,	'Week 29',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(201,	'2023-07-20',	'Thursday',	29,	'Week 29',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(202,	'2023-07-21',	'Friday',	29,	'Week 29',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(203,	'2023-07-22',	'Saturday',	29,	'Week 29',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(204,	'2023-07-23',	'Sunday',	29,	'Week 29',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(205,	'2023-07-24',	'Monday',	30,	'Week 30',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(206,	'2023-07-25',	'Tuesday',	30,	'Week 30',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(207,	'2023-07-26',	'Wednesday',	30,	'Week 30',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(208,	'2023-07-27',	'Thursday',	30,	'Week 30',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(209,	'2023-07-28',	'Friday',	30,	'Week 30',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(210,	'2023-07-29',	'Saturday',	30,	'Week 30',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(211,	'2023-07-30',	'Sunday',	30,	'Week 30',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(212,	'2023-07-31',	'Monday',	31,	'Week 31',	'7',	'July',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(213,	'2023-08-01',	'Tuesday',	31,	'Week 31',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(214,	'2023-08-02',	'Wednesday',	31,	'Week 31',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(215,	'2023-08-03',	'Thursday',	31,	'Week 31',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(216,	'2023-08-04',	'Friday',	31,	'Week 31',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(217,	'2023-08-05',	'Saturday',	31,	'Week 31',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(218,	'2023-08-06',	'Sunday',	31,	'Week 31',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(219,	'2023-08-07',	'Monday',	32,	'Week 32',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(220,	'2023-08-08',	'Tuesday',	32,	'Week 32',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(221,	'2023-08-09',	'Wednesday',	32,	'Week 32',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(222,	'2023-08-10',	'Thursday',	32,	'Week 32',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(223,	'2023-08-11',	'Friday',	32,	'Week 32',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(224,	'2023-08-12',	'Saturday',	32,	'Week 32',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(225,	'2023-08-13',	'Sunday',	32,	'Week 32',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(226,	'2023-08-14',	'Monday',	33,	'Week 33',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(227,	'2023-08-15',	'Tuesday',	33,	'Week 33',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(228,	'2023-08-16',	'Wednesday',	33,	'Week 33',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(229,	'2023-08-17',	'Thursday',	33,	'Week 33',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(230,	'2023-08-18',	'Friday',	33,	'Week 33',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(231,	'2023-08-19',	'Saturday',	33,	'Week 33',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(232,	'2023-08-20',	'Sunday',	33,	'Week 33',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(233,	'2023-08-21',	'Monday',	34,	'Week 34',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(234,	'2023-08-22',	'Tuesday',	34,	'Week 34',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(235,	'2023-08-23',	'Wednesday',	34,	'Week 34',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(236,	'2023-08-24',	'Thursday',	34,	'Week 34',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(237,	'2023-08-25',	'Friday',	34,	'Week 34',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(238,	'2023-08-26',	'Saturday',	34,	'Week 34',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(239,	'2023-08-27',	'Sunday',	34,	'Week 34',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(240,	'2023-08-28',	'Monday',	35,	'Week 35',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(241,	'2023-08-29',	'Tuesday',	35,	'Week 35',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(242,	'2023-08-30',	'Wednesday',	35,	'Week 35',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(243,	'2023-08-31',	'Thursday',	35,	'Week 35',	'8',	'August',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(244,	'2023-09-01',	'Friday',	35,	'Week 35',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(245,	'2023-09-02',	'Saturday',	35,	'Week 35',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(246,	'2023-09-03',	'Sunday',	35,	'Week 35',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(247,	'2023-09-04',	'Monday',	36,	'Week 36',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(248,	'2023-09-05',	'Tuesday',	36,	'Week 36',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(249,	'2023-09-06',	'Wednesday',	36,	'Week 36',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(250,	'2023-09-07',	'Thursday',	36,	'Week 36',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(251,	'2023-09-08',	'Friday',	36,	'Week 36',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(252,	'2023-09-09',	'Saturday',	36,	'Week 36',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(253,	'2023-09-10',	'Sunday',	36,	'Week 36',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(254,	'2023-09-11',	'Monday',	37,	'Week 37',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(255,	'2023-09-12',	'Tuesday',	37,	'Week 37',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(256,	'2023-09-13',	'Wednesday',	37,	'Week 37',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(257,	'2023-09-14',	'Thursday',	37,	'Week 37',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(258,	'2023-09-15',	'Friday',	37,	'Week 37',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(259,	'2023-09-16',	'Saturday',	37,	'Week 37',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(260,	'2023-09-17',	'Sunday',	37,	'Week 37',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(261,	'2023-09-18',	'Monday',	38,	'Week 38',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(262,	'2023-09-19',	'Tuesday',	38,	'Week 38',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(263,	'2023-09-20',	'Wednesday',	38,	'Week 38',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(264,	'2023-09-21',	'Thursday',	38,	'Week 38',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(265,	'2023-09-22',	'Friday',	38,	'Week 38',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(266,	'2023-09-23',	'Saturday',	38,	'Week 38',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(267,	'2023-09-24',	'Sunday',	38,	'Week 38',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(268,	'2023-09-25',	'Monday',	39,	'Week 39',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(269,	'2023-09-26',	'Tuesday',	39,	'Week 39',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(270,	'2023-09-27',	'Wednesday',	39,	'Week 39',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(271,	'2023-09-28',	'Thursday',	39,	'Week 39',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(272,	'2023-09-29',	'Friday',	39,	'Week 39',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(273,	'2023-09-30',	'Saturday',	39,	'Week 39',	'9',	'September',	'Q3',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(274,	'2023-10-01',	'Sunday',	39,	'Week 39',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(275,	'2023-10-02',	'Monday',	40,	'Week 40',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(276,	'2023-10-03',	'Tuesday',	40,	'Week 40',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(277,	'2023-10-04',	'Wednesday',	40,	'Week 40',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(278,	'2023-10-05',	'Thursday',	40,	'Week 40',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(279,	'2023-10-06',	'Friday',	40,	'Week 40',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(280,	'2023-10-07',	'Saturday',	40,	'Week 40',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(281,	'2023-10-08',	'Sunday',	40,	'Week 40',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(282,	'2023-10-09',	'Monday',	41,	'Week 41',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(283,	'2023-10-10',	'Tuesday',	41,	'Week 41',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(284,	'2023-10-11',	'Wednesday',	41,	'Week 41',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(285,	'2023-10-12',	'Thursday',	41,	'Week 41',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(286,	'2023-10-13',	'Friday',	41,	'Week 41',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(287,	'2023-10-14',	'Saturday',	41,	'Week 41',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(288,	'2023-10-15',	'Sunday',	41,	'Week 41',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(289,	'2023-10-16',	'Monday',	42,	'Week 42',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(290,	'2023-10-17',	'Tuesday',	42,	'Week 42',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(291,	'2023-10-18',	'Wednesday',	42,	'Week 42',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(292,	'2023-10-19',	'Thursday',	42,	'Week 42',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(293,	'2023-10-20',	'Friday',	42,	'Week 42',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(294,	'2023-10-21',	'Saturday',	42,	'Week 42',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(295,	'2023-10-22',	'Sunday',	42,	'Week 42',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(296,	'2023-10-23',	'Monday',	43,	'Week 43',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(297,	'2023-10-24',	'Tuesday',	43,	'Week 43',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(298,	'2023-10-25',	'Wednesday',	43,	'Week 43',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(299,	'2023-10-26',	'Thursday',	43,	'Week 43',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(300,	'2023-10-27',	'Friday',	43,	'Week 43',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(301,	'2023-10-28',	'Saturday',	43,	'Week 43',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(302,	'2023-10-29',	'Sunday',	43,	'Week 43',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(303,	'2023-10-30',	'Monday',	44,	'Week 44',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(304,	'2023-10-31',	'Tuesday',	44,	'Week 44',	'10',	'October',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(305,	'2023-11-01',	'Wednesday',	44,	'Week 44',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(306,	'2023-11-02',	'Thursday',	44,	'Week 44',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(307,	'2023-11-03',	'Friday',	44,	'Week 44',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(308,	'2023-11-04',	'Saturday',	44,	'Week 44',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(309,	'2023-11-05',	'Sunday',	44,	'Week 44',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(310,	'2023-11-06',	'Monday',	45,	'Week 45',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(311,	'2023-11-07',	'Tuesday',	45,	'Week 45',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(312,	'2023-11-08',	'Wednesday',	45,	'Week 45',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(313,	'2023-11-09',	'Thursday',	45,	'Week 45',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(314,	'2023-11-10',	'Friday',	45,	'Week 45',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(315,	'2023-11-11',	'Saturday',	45,	'Week 45',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(316,	'2023-11-12',	'Sunday',	45,	'Week 45',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(317,	'2023-11-13',	'Monday',	46,	'Week 46',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(318,	'2023-11-14',	'Tuesday',	46,	'Week 46',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(319,	'2023-11-15',	'Wednesday',	46,	'Week 46',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(320,	'2023-11-16',	'Thursday',	46,	'Week 46',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(321,	'2023-11-17',	'Friday',	46,	'Week 46',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(322,	'2023-11-18',	'Saturday',	46,	'Week 46',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(323,	'2023-11-19',	'Sunday',	46,	'Week 46',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(324,	'2023-11-20',	'Monday',	47,	'Week 47',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(325,	'2023-11-21',	'Tuesday',	47,	'Week 47',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(326,	'2023-11-22',	'Wednesday',	47,	'Week 47',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(327,	'2023-11-23',	'Thursday',	47,	'Week 47',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(328,	'2023-11-24',	'Friday',	47,	'Week 47',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(329,	'2023-11-25',	'Saturday',	47,	'Week 47',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(330,	'2023-11-26',	'Sunday',	47,	'Week 47',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(331,	'2023-11-27',	'Monday',	48,	'Week 48',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(332,	'2023-11-28',	'Tuesday',	48,	'Week 48',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(333,	'2023-11-29',	'Wednesday',	48,	'Week 48',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(334,	'2023-11-30',	'Thursday',	48,	'Week 48',	'11',	'November',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(335,	'2023-12-01',	'Friday',	48,	'Week 48',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(336,	'2023-12-02',	'Saturday',	48,	'Week 48',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(337,	'2023-12-03',	'Sunday',	48,	'Week 48',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(338,	'2023-12-04',	'Monday',	49,	'Week 49',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(339,	'2023-12-05',	'Tuesday',	49,	'Week 49',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(340,	'2023-12-06',	'Wednesday',	49,	'Week 49',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(341,	'2023-12-07',	'Thursday',	49,	'Week 49',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(342,	'2023-12-08',	'Friday',	49,	'Week 49',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(343,	'2023-12-09',	'Saturday',	49,	'Week 49',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(344,	'2023-12-10',	'Sunday',	49,	'Week 49',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(345,	'2023-12-11',	'Monday',	50,	'Week 50',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(346,	'2023-12-12',	'Tuesday',	50,	'Week 50',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(347,	'2023-12-13',	'Wednesday',	50,	'Week 50',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(348,	'2023-12-14',	'Thursday',	50,	'Week 50',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(349,	'2023-12-15',	'Friday',	50,	'Week 50',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(350,	'2023-12-16',	'Saturday',	50,	'Week 50',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(351,	'2023-12-17',	'Sunday',	50,	'Week 50',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(352,	'2023-12-18',	'Monday',	51,	'Week 51',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(353,	'2023-12-19',	'Tuesday',	51,	'Week 51',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(354,	'2023-12-20',	'Wednesday',	51,	'Week 51',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(355,	'2023-12-21',	'Thursday',	51,	'Week 51',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(356,	'2023-12-22',	'Friday',	51,	'Week 51',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(357,	'2023-12-23',	'Saturday',	51,	'Week 51',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(358,	'2023-12-24',	'Sunday',	51,	'Week 51',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(359,	'2023-12-25',	'Monday',	52,	'Week 52',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(360,	'2023-12-26',	'Tuesday',	52,	'Week 52',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(361,	'2023-12-27',	'Wednesday',	52,	'Week 52',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(362,	'2023-12-28',	'Thursday',	52,	'Week 52',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(363,	'2023-12-29',	'Friday',	52,	'Week 52',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(364,	'2023-12-30',	'Saturday',	52,	'Week 52',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(365,	'2023-12-31',	'Sunday',	52,	'Week 52',	'12',	'December',	'Q4',	2023,	0,	'2024-02-22 12:37:03',	'2024-02-22 12:37:03',	NULL),
(366,	'2024-01-01',	'Monday',	1,	'Week 1',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(367,	'2024-01-02',	'Tuesday',	1,	'Week 1',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(368,	'2024-01-03',	'Wednesday',	1,	'Week 1',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(369,	'2024-01-04',	'Thursday',	1,	'Week 1',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(370,	'2024-01-05',	'Friday',	1,	'Week 1',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(371,	'2024-01-06',	'Saturday',	1,	'Week 1',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(372,	'2024-01-07',	'Sunday',	1,	'Week 1',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(373,	'2024-01-08',	'Monday',	2,	'Week 2',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(374,	'2024-01-09',	'Tuesday',	2,	'Week 2',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(375,	'2024-01-10',	'Wednesday',	2,	'Week 2',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(376,	'2024-01-11',	'Thursday',	2,	'Week 2',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(377,	'2024-01-12',	'Friday',	2,	'Week 2',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(378,	'2024-01-13',	'Saturday',	2,	'Week 2',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(379,	'2024-01-14',	'Sunday',	2,	'Week 2',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(380,	'2024-01-15',	'Monday',	3,	'Week 3',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(381,	'2024-01-16',	'Tuesday',	3,	'Week 3',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(382,	'2024-01-17',	'Wednesday',	3,	'Week 3',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(383,	'2024-01-18',	'Thursday',	3,	'Week 3',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(384,	'2024-01-19',	'Friday',	3,	'Week 3',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(385,	'2024-01-20',	'Saturday',	3,	'Week 3',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(386,	'2024-01-21',	'Sunday',	3,	'Week 3',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(387,	'2024-01-22',	'Monday',	4,	'Week 4',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(388,	'2024-01-23',	'Tuesday',	4,	'Week 4',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(389,	'2024-01-24',	'Wednesday',	4,	'Week 4',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(390,	'2024-01-25',	'Thursday',	4,	'Week 4',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(391,	'2024-01-26',	'Friday',	4,	'Week 4',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(392,	'2024-01-27',	'Saturday',	4,	'Week 4',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(393,	'2024-01-28',	'Sunday',	4,	'Week 4',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(394,	'2024-01-29',	'Monday',	5,	'Week 5',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(395,	'2024-01-30',	'Tuesday',	5,	'Week 5',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(396,	'2024-01-31',	'Wednesday',	5,	'Week 5',	'1',	'January',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(397,	'2024-02-01',	'Thursday',	5,	'Week 5',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(398,	'2024-02-02',	'Friday',	5,	'Week 5',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(399,	'2024-02-03',	'Saturday',	5,	'Week 5',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(400,	'2024-02-04',	'Sunday',	5,	'Week 5',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(401,	'2024-02-05',	'Monday',	6,	'Week 6',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(402,	'2024-02-06',	'Tuesday',	6,	'Week 6',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(403,	'2024-02-07',	'Wednesday',	6,	'Week 6',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(404,	'2024-02-08',	'Thursday',	6,	'Week 6',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(405,	'2024-02-09',	'Friday',	6,	'Week 6',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(406,	'2024-02-10',	'Saturday',	6,	'Week 6',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(407,	'2024-02-11',	'Sunday',	6,	'Week 6',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(408,	'2024-02-12',	'Monday',	7,	'Week 7',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(409,	'2024-02-13',	'Tuesday',	7,	'Week 7',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(410,	'2024-02-14',	'Wednesday',	7,	'Week 7',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(411,	'2024-02-15',	'Thursday',	7,	'Week 7',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(412,	'2024-02-16',	'Friday',	7,	'Week 7',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(413,	'2024-02-17',	'Saturday',	7,	'Week 7',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(414,	'2024-02-18',	'Sunday',	7,	'Week 7',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(415,	'2024-02-19',	'Monday',	8,	'Week 8',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(416,	'2024-02-20',	'Tuesday',	8,	'Week 8',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(417,	'2024-02-21',	'Wednesday',	8,	'Week 8',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(418,	'2024-02-22',	'Thursday',	8,	'Week 8',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(419,	'2024-02-23',	'Friday',	8,	'Week 8',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(420,	'2024-02-24',	'Saturday',	8,	'Week 8',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(421,	'2024-02-25',	'Sunday',	8,	'Week 8',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(422,	'2024-02-26',	'Monday',	9,	'Week 9',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(423,	'2024-02-27',	'Tuesday',	9,	'Week 9',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(424,	'2024-02-28',	'Wednesday',	9,	'Week 9',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(425,	'2024-02-29',	'Thursday',	9,	'Week 9',	'2',	'February',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(426,	'2024-03-01',	'Friday',	9,	'Week 9',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(427,	'2024-03-02',	'Saturday',	9,	'Week 9',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(428,	'2024-03-03',	'Sunday',	9,	'Week 9',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(429,	'2024-03-04',	'Monday',	10,	'Week 10',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(430,	'2024-03-05',	'Tuesday',	10,	'Week 10',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(431,	'2024-03-06',	'Wednesday',	10,	'Week 10',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(432,	'2024-03-07',	'Thursday',	10,	'Week 10',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(433,	'2024-03-08',	'Friday',	10,	'Week 10',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(434,	'2024-03-09',	'Saturday',	10,	'Week 10',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(435,	'2024-03-10',	'Sunday',	10,	'Week 10',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(436,	'2024-03-11',	'Monday',	11,	'Week 11',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(437,	'2024-03-12',	'Tuesday',	11,	'Week 11',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(438,	'2024-03-13',	'Wednesday',	11,	'Week 11',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(439,	'2024-03-14',	'Thursday',	11,	'Week 11',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(440,	'2024-03-15',	'Friday',	11,	'Week 11',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(441,	'2024-03-16',	'Saturday',	11,	'Week 11',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(442,	'2024-03-17',	'Sunday',	11,	'Week 11',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(443,	'2024-03-18',	'Monday',	12,	'Week 12',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(444,	'2024-03-19',	'Tuesday',	12,	'Week 12',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(445,	'2024-03-20',	'Wednesday',	12,	'Week 12',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(446,	'2024-03-21',	'Thursday',	12,	'Week 12',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(447,	'2024-03-22',	'Friday',	12,	'Week 12',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(448,	'2024-03-23',	'Saturday',	12,	'Week 12',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(449,	'2024-03-24',	'Sunday',	12,	'Week 12',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(450,	'2024-03-25',	'Monday',	13,	'Week 13',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(451,	'2024-03-26',	'Tuesday',	13,	'Week 13',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(452,	'2024-03-27',	'Wednesday',	13,	'Week 13',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(453,	'2024-03-28',	'Thursday',	13,	'Week 13',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(454,	'2024-03-29',	'Friday',	13,	'Week 13',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(455,	'2024-03-30',	'Saturday',	13,	'Week 13',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(456,	'2024-03-31',	'Sunday',	13,	'Week 13',	'3',	'March',	'Q1',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(457,	'2024-04-01',	'Monday',	14,	'Week 14',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(458,	'2024-04-02',	'Tuesday',	14,	'Week 14',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(459,	'2024-04-03',	'Wednesday',	14,	'Week 14',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(460,	'2024-04-04',	'Thursday',	14,	'Week 14',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(461,	'2024-04-05',	'Friday',	14,	'Week 14',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(462,	'2024-04-06',	'Saturday',	14,	'Week 14',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(463,	'2024-04-07',	'Sunday',	14,	'Week 14',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(464,	'2024-04-08',	'Monday',	15,	'Week 15',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(465,	'2024-04-09',	'Tuesday',	15,	'Week 15',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(466,	'2024-04-10',	'Wednesday',	15,	'Week 15',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(467,	'2024-04-11',	'Thursday',	15,	'Week 15',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(468,	'2024-04-12',	'Friday',	15,	'Week 15',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(469,	'2024-04-13',	'Saturday',	15,	'Week 15',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(470,	'2024-04-14',	'Sunday',	15,	'Week 15',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(471,	'2024-04-15',	'Monday',	16,	'Week 16',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(472,	'2024-04-16',	'Tuesday',	16,	'Week 16',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(473,	'2024-04-17',	'Wednesday',	16,	'Week 16',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(474,	'2024-04-18',	'Thursday',	16,	'Week 16',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(475,	'2024-04-19',	'Friday',	16,	'Week 16',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(476,	'2024-04-20',	'Saturday',	16,	'Week 16',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(477,	'2024-04-21',	'Sunday',	16,	'Week 16',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(478,	'2024-04-22',	'Monday',	17,	'Week 17',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(479,	'2024-04-23',	'Tuesday',	17,	'Week 17',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(480,	'2024-04-24',	'Wednesday',	17,	'Week 17',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(481,	'2024-04-25',	'Thursday',	17,	'Week 17',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(482,	'2024-04-26',	'Friday',	17,	'Week 17',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(483,	'2024-04-27',	'Saturday',	17,	'Week 17',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(484,	'2024-04-28',	'Sunday',	17,	'Week 17',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(485,	'2024-04-29',	'Monday',	18,	'Week 18',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(486,	'2024-04-30',	'Tuesday',	18,	'Week 18',	'4',	'April',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(487,	'2024-05-01',	'Wednesday',	18,	'Week 18',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(488,	'2024-05-02',	'Thursday',	18,	'Week 18',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(489,	'2024-05-03',	'Friday',	18,	'Week 18',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(490,	'2024-05-04',	'Saturday',	18,	'Week 18',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(491,	'2024-05-05',	'Sunday',	18,	'Week 18',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(492,	'2024-05-06',	'Monday',	19,	'Week 19',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(493,	'2024-05-07',	'Tuesday',	19,	'Week 19',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(494,	'2024-05-08',	'Wednesday',	19,	'Week 19',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(495,	'2024-05-09',	'Thursday',	19,	'Week 19',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(496,	'2024-05-10',	'Friday',	19,	'Week 19',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(497,	'2024-05-11',	'Saturday',	19,	'Week 19',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(498,	'2024-05-12',	'Sunday',	19,	'Week 19',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(499,	'2024-05-13',	'Monday',	20,	'Week 20',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(500,	'2024-05-14',	'Tuesday',	20,	'Week 20',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(501,	'2024-05-15',	'Wednesday',	20,	'Week 20',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(502,	'2024-05-16',	'Thursday',	20,	'Week 20',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(503,	'2024-05-17',	'Friday',	20,	'Week 20',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(504,	'2024-05-18',	'Saturday',	20,	'Week 20',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(505,	'2024-05-19',	'Sunday',	20,	'Week 20',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(506,	'2024-05-20',	'Monday',	21,	'Week 21',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(507,	'2024-05-21',	'Tuesday',	21,	'Week 21',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(508,	'2024-05-22',	'Wednesday',	21,	'Week 21',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(509,	'2024-05-23',	'Thursday',	21,	'Week 21',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(510,	'2024-05-24',	'Friday',	21,	'Week 21',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(511,	'2024-05-25',	'Saturday',	21,	'Week 21',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(512,	'2024-05-26',	'Sunday',	21,	'Week 21',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(513,	'2024-05-27',	'Monday',	22,	'Week 22',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(514,	'2024-05-28',	'Tuesday',	22,	'Week 22',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(515,	'2024-05-29',	'Wednesday',	22,	'Week 22',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(516,	'2024-05-30',	'Thursday',	22,	'Week 22',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(517,	'2024-05-31',	'Friday',	22,	'Week 22',	'5',	'May',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(518,	'2024-06-01',	'Saturday',	22,	'Week 22',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(519,	'2024-06-02',	'Sunday',	22,	'Week 22',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(520,	'2024-06-03',	'Monday',	23,	'Week 23',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(521,	'2024-06-04',	'Tuesday',	23,	'Week 23',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(522,	'2024-06-05',	'Wednesday',	23,	'Week 23',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(523,	'2024-06-06',	'Thursday',	23,	'Week 23',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(524,	'2024-06-07',	'Friday',	23,	'Week 23',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(525,	'2024-06-08',	'Saturday',	23,	'Week 23',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(526,	'2024-06-09',	'Sunday',	23,	'Week 23',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(527,	'2024-06-10',	'Monday',	24,	'Week 24',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(528,	'2024-06-11',	'Tuesday',	24,	'Week 24',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(529,	'2024-06-12',	'Wednesday',	24,	'Week 24',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(530,	'2024-06-13',	'Thursday',	24,	'Week 24',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(531,	'2024-06-14',	'Friday',	24,	'Week 24',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(532,	'2024-06-15',	'Saturday',	24,	'Week 24',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(533,	'2024-06-16',	'Sunday',	24,	'Week 24',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(534,	'2024-06-17',	'Monday',	25,	'Week 25',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(535,	'2024-06-18',	'Tuesday',	25,	'Week 25',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(536,	'2024-06-19',	'Wednesday',	25,	'Week 25',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(537,	'2024-06-20',	'Thursday',	25,	'Week 25',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(538,	'2024-06-21',	'Friday',	25,	'Week 25',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(539,	'2024-06-22',	'Saturday',	25,	'Week 25',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(540,	'2024-06-23',	'Sunday',	25,	'Week 25',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(541,	'2024-06-24',	'Monday',	26,	'Week 26',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(542,	'2024-06-25',	'Tuesday',	26,	'Week 26',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(543,	'2024-06-26',	'Wednesday',	26,	'Week 26',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(544,	'2024-06-27',	'Thursday',	26,	'Week 26',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(545,	'2024-06-28',	'Friday',	26,	'Week 26',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(546,	'2024-06-29',	'Saturday',	26,	'Week 26',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(547,	'2024-06-30',	'Sunday',	26,	'Week 26',	'6',	'June',	'Q2',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(548,	'2024-07-01',	'Monday',	27,	'Week 27',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(549,	'2024-07-02',	'Tuesday',	27,	'Week 27',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(550,	'2024-07-03',	'Wednesday',	27,	'Week 27',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(551,	'2024-07-04',	'Thursday',	27,	'Week 27',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(552,	'2024-07-05',	'Friday',	27,	'Week 27',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(553,	'2024-07-06',	'Saturday',	27,	'Week 27',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(554,	'2024-07-07',	'Sunday',	27,	'Week 27',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(555,	'2024-07-08',	'Monday',	28,	'Week 28',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(556,	'2024-07-09',	'Tuesday',	28,	'Week 28',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(557,	'2024-07-10',	'Wednesday',	28,	'Week 28',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(558,	'2024-07-11',	'Thursday',	28,	'Week 28',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(559,	'2024-07-12',	'Friday',	28,	'Week 28',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(560,	'2024-07-13',	'Saturday',	28,	'Week 28',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(561,	'2024-07-14',	'Sunday',	28,	'Week 28',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(562,	'2024-07-15',	'Monday',	29,	'Week 29',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(563,	'2024-07-16',	'Tuesday',	29,	'Week 29',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(564,	'2024-07-17',	'Wednesday',	29,	'Week 29',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(565,	'2024-07-18',	'Thursday',	29,	'Week 29',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(566,	'2024-07-19',	'Friday',	29,	'Week 29',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(567,	'2024-07-20',	'Saturday',	29,	'Week 29',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(568,	'2024-07-21',	'Sunday',	29,	'Week 29',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(569,	'2024-07-22',	'Monday',	30,	'Week 30',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(570,	'2024-07-23',	'Tuesday',	30,	'Week 30',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(571,	'2024-07-24',	'Wednesday',	30,	'Week 30',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(572,	'2024-07-25',	'Thursday',	30,	'Week 30',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(573,	'2024-07-26',	'Friday',	30,	'Week 30',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(574,	'2024-07-27',	'Saturday',	30,	'Week 30',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(575,	'2024-07-28',	'Sunday',	30,	'Week 30',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(576,	'2024-07-29',	'Monday',	31,	'Week 31',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(577,	'2024-07-30',	'Tuesday',	31,	'Week 31',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(578,	'2024-07-31',	'Wednesday',	31,	'Week 31',	'7',	'July',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(579,	'2024-08-01',	'Thursday',	31,	'Week 31',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(580,	'2024-08-02',	'Friday',	31,	'Week 31',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(581,	'2024-08-03',	'Saturday',	31,	'Week 31',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(582,	'2024-08-04',	'Sunday',	31,	'Week 31',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(583,	'2024-08-05',	'Monday',	32,	'Week 32',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(584,	'2024-08-06',	'Tuesday',	32,	'Week 32',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(585,	'2024-08-07',	'Wednesday',	32,	'Week 32',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(586,	'2024-08-08',	'Thursday',	32,	'Week 32',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(587,	'2024-08-09',	'Friday',	32,	'Week 32',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(588,	'2024-08-10',	'Saturday',	32,	'Week 32',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(589,	'2024-08-11',	'Sunday',	32,	'Week 32',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(590,	'2024-08-12',	'Monday',	33,	'Week 33',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(591,	'2024-08-13',	'Tuesday',	33,	'Week 33',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(592,	'2024-08-14',	'Wednesday',	33,	'Week 33',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(593,	'2024-08-15',	'Thursday',	33,	'Week 33',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(594,	'2024-08-16',	'Friday',	33,	'Week 33',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(595,	'2024-08-17',	'Saturday',	33,	'Week 33',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(596,	'2024-08-18',	'Sunday',	33,	'Week 33',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(597,	'2024-08-19',	'Monday',	34,	'Week 34',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(598,	'2024-08-20',	'Tuesday',	34,	'Week 34',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(599,	'2024-08-21',	'Wednesday',	34,	'Week 34',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(600,	'2024-08-22',	'Thursday',	34,	'Week 34',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(601,	'2024-08-23',	'Friday',	34,	'Week 34',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(602,	'2024-08-24',	'Saturday',	34,	'Week 34',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(603,	'2024-08-25',	'Sunday',	34,	'Week 34',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(604,	'2024-08-26',	'Monday',	35,	'Week 35',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(605,	'2024-08-27',	'Tuesday',	35,	'Week 35',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(606,	'2024-08-28',	'Wednesday',	35,	'Week 35',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(607,	'2024-08-29',	'Thursday',	35,	'Week 35',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(608,	'2024-08-30',	'Friday',	35,	'Week 35',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(609,	'2024-08-31',	'Saturday',	35,	'Week 35',	'8',	'August',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(610,	'2024-09-01',	'Sunday',	35,	'Week 35',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(611,	'2024-09-02',	'Monday',	36,	'Week 36',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(612,	'2024-09-03',	'Tuesday',	36,	'Week 36',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(613,	'2024-09-04',	'Wednesday',	36,	'Week 36',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(614,	'2024-09-05',	'Thursday',	36,	'Week 36',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(615,	'2024-09-06',	'Friday',	36,	'Week 36',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(616,	'2024-09-07',	'Saturday',	36,	'Week 36',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(617,	'2024-09-08',	'Sunday',	36,	'Week 36',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(618,	'2024-09-09',	'Monday',	37,	'Week 37',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(619,	'2024-09-10',	'Tuesday',	37,	'Week 37',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(620,	'2024-09-11',	'Wednesday',	37,	'Week 37',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(621,	'2024-09-12',	'Thursday',	37,	'Week 37',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(622,	'2024-09-13',	'Friday',	37,	'Week 37',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(623,	'2024-09-14',	'Saturday',	37,	'Week 37',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(624,	'2024-09-15',	'Sunday',	37,	'Week 37',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(625,	'2024-09-16',	'Monday',	38,	'Week 38',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(626,	'2024-09-17',	'Tuesday',	38,	'Week 38',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(627,	'2024-09-18',	'Wednesday',	38,	'Week 38',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(628,	'2024-09-19',	'Thursday',	38,	'Week 38',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(629,	'2024-09-20',	'Friday',	38,	'Week 38',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(630,	'2024-09-21',	'Saturday',	38,	'Week 38',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(631,	'2024-09-22',	'Sunday',	38,	'Week 38',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(632,	'2024-09-23',	'Monday',	39,	'Week 39',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(633,	'2024-09-24',	'Tuesday',	39,	'Week 39',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(634,	'2024-09-25',	'Wednesday',	39,	'Week 39',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(635,	'2024-09-26',	'Thursday',	39,	'Week 39',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(636,	'2024-09-27',	'Friday',	39,	'Week 39',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(637,	'2024-09-28',	'Saturday',	39,	'Week 39',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(638,	'2024-09-29',	'Sunday',	39,	'Week 39',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(639,	'2024-09-30',	'Monday',	40,	'Week 40',	'9',	'September',	'Q3',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(640,	'2024-10-01',	'Tuesday',	40,	'Week 40',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(641,	'2024-10-02',	'Wednesday',	40,	'Week 40',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(642,	'2024-10-03',	'Thursday',	40,	'Week 40',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(643,	'2024-10-04',	'Friday',	40,	'Week 40',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(644,	'2024-10-05',	'Saturday',	40,	'Week 40',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(645,	'2024-10-06',	'Sunday',	40,	'Week 40',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(646,	'2024-10-07',	'Monday',	41,	'Week 41',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(647,	'2024-10-08',	'Tuesday',	41,	'Week 41',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(648,	'2024-10-09',	'Wednesday',	41,	'Week 41',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(649,	'2024-10-10',	'Thursday',	41,	'Week 41',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(650,	'2024-10-11',	'Friday',	41,	'Week 41',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(651,	'2024-10-12',	'Saturday',	41,	'Week 41',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(652,	'2024-10-13',	'Sunday',	41,	'Week 41',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(653,	'2024-10-14',	'Monday',	42,	'Week 42',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(654,	'2024-10-15',	'Tuesday',	42,	'Week 42',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(655,	'2024-10-16',	'Wednesday',	42,	'Week 42',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(656,	'2024-10-17',	'Thursday',	42,	'Week 42',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(657,	'2024-10-18',	'Friday',	42,	'Week 42',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(658,	'2024-10-19',	'Saturday',	42,	'Week 42',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(659,	'2024-10-20',	'Sunday',	42,	'Week 42',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(660,	'2024-10-21',	'Monday',	43,	'Week 43',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(661,	'2024-10-22',	'Tuesday',	43,	'Week 43',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(662,	'2024-10-23',	'Wednesday',	43,	'Week 43',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(663,	'2024-10-24',	'Thursday',	43,	'Week 43',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(664,	'2024-10-25',	'Friday',	43,	'Week 43',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(665,	'2024-10-26',	'Saturday',	43,	'Week 43',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(666,	'2024-10-27',	'Sunday',	43,	'Week 43',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(667,	'2024-10-28',	'Monday',	44,	'Week 44',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(668,	'2024-10-29',	'Tuesday',	44,	'Week 44',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(669,	'2024-10-30',	'Wednesday',	44,	'Week 44',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(670,	'2024-10-31',	'Thursday',	44,	'Week 44',	'10',	'October',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(671,	'2024-11-01',	'Friday',	44,	'Week 44',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(672,	'2024-11-02',	'Saturday',	44,	'Week 44',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(673,	'2024-11-03',	'Sunday',	44,	'Week 44',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(674,	'2024-11-04',	'Monday',	45,	'Week 45',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(675,	'2024-11-05',	'Tuesday',	45,	'Week 45',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(676,	'2024-11-06',	'Wednesday',	45,	'Week 45',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(677,	'2024-11-07',	'Thursday',	45,	'Week 45',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(678,	'2024-11-08',	'Friday',	45,	'Week 45',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(679,	'2024-11-09',	'Saturday',	45,	'Week 45',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(680,	'2024-11-10',	'Sunday',	45,	'Week 45',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(681,	'2024-11-11',	'Monday',	46,	'Week 46',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(682,	'2024-11-12',	'Tuesday',	46,	'Week 46',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(683,	'2024-11-13',	'Wednesday',	46,	'Week 46',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(684,	'2024-11-14',	'Thursday',	46,	'Week 46',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(685,	'2024-11-15',	'Friday',	46,	'Week 46',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(686,	'2024-11-16',	'Saturday',	46,	'Week 46',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(687,	'2024-11-17',	'Sunday',	46,	'Week 46',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(688,	'2024-11-18',	'Monday',	47,	'Week 47',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(689,	'2024-11-19',	'Tuesday',	47,	'Week 47',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(690,	'2024-11-20',	'Wednesday',	47,	'Week 47',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(691,	'2024-11-21',	'Thursday',	47,	'Week 47',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(692,	'2024-11-22',	'Friday',	47,	'Week 47',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(693,	'2024-11-23',	'Saturday',	47,	'Week 47',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(694,	'2024-11-24',	'Sunday',	47,	'Week 47',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(695,	'2024-11-25',	'Monday',	48,	'Week 48',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(696,	'2024-11-26',	'Tuesday',	48,	'Week 48',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(697,	'2024-11-27',	'Wednesday',	48,	'Week 48',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(698,	'2024-11-28',	'Thursday',	48,	'Week 48',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(699,	'2024-11-29',	'Friday',	48,	'Week 48',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(700,	'2024-11-30',	'Saturday',	48,	'Week 48',	'11',	'November',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(701,	'2024-12-01',	'Sunday',	48,	'Week 48',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(702,	'2024-12-02',	'Monday',	49,	'Week 49',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(703,	'2024-12-03',	'Tuesday',	49,	'Week 49',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(704,	'2024-12-04',	'Wednesday',	49,	'Week 49',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(705,	'2024-12-05',	'Thursday',	49,	'Week 49',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(706,	'2024-12-06',	'Friday',	49,	'Week 49',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(707,	'2024-12-07',	'Saturday',	49,	'Week 49',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(708,	'2024-12-08',	'Sunday',	49,	'Week 49',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(709,	'2024-12-09',	'Monday',	50,	'Week 50',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(710,	'2024-12-10',	'Tuesday',	50,	'Week 50',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(711,	'2024-12-11',	'Wednesday',	50,	'Week 50',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(712,	'2024-12-12',	'Thursday',	50,	'Week 50',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(713,	'2024-12-13',	'Friday',	50,	'Week 50',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(714,	'2024-12-14',	'Saturday',	50,	'Week 50',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(715,	'2024-12-15',	'Sunday',	50,	'Week 50',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(716,	'2024-12-16',	'Monday',	51,	'Week 51',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(717,	'2024-12-17',	'Tuesday',	51,	'Week 51',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(718,	'2024-12-18',	'Wednesday',	51,	'Week 51',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(719,	'2024-12-19',	'Thursday',	51,	'Week 51',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(720,	'2024-12-20',	'Friday',	51,	'Week 51',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(721,	'2024-12-21',	'Saturday',	51,	'Week 51',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(722,	'2024-12-22',	'Sunday',	51,	'Week 51',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(723,	'2024-12-23',	'Monday',	52,	'Week 52',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(724,	'2024-12-24',	'Tuesday',	52,	'Week 52',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(725,	'2024-12-25',	'Wednesday',	52,	'Week 52',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(726,	'2024-12-26',	'Thursday',	52,	'Week 52',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(727,	'2024-12-27',	'Friday',	52,	'Week 52',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(728,	'2024-12-28',	'Saturday',	52,	'Week 52',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(729,	'2024-12-29',	'Sunday',	52,	'Week 52',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(730,	'2024-12-30',	'Monday',	53,	'Week 53',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL),
(731,	'2024-12-31',	'Tuesday',	53,	'Week 53',	'12',	'December',	'Q4',	2024,	0,	'2024-02-22 12:37:11',	'2024-02-22 12:37:11',	NULL);

DROP TABLE IF EXISTS `failed_jobs`;
CREATE TABLE `failed_jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `jobs`;
CREATE TABLE `jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `queue` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `attempts` tinyint unsigned NOT NULL,
  `reserved_at` int unsigned DEFAULT NULL,
  `available_at` int unsigned NOT NULL,
  `created_at` int unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `jobs_queue_index` (`queue`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `migrations`;
CREATE TABLE `migrations` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1,	'2019_12_14_000001_create_personal_access_tokens_table',	1),
(2,	'2023_11_29_110605_create_subscription_table',	1),
(3,	'2023_12_04_074157_create_client_db_server_type_table',	1),
(4,	'2023_12_04_161240_create_subscription_item_table',	1),
(5,	'2023_12_04_181929_create_database_server_option_table',	1),
(6,	'2023_12_04_182029_create_database_table_option_table',	1),
(7,	'2023_12_04_182059_create_country_table',	1),
(8,	'2023_12_04_182121_create_state_table',	1),
(9,	'2023_12_04_182142_create_company_table',	1),
(10,	'2023_12_04_182452_create_timezone_table',	1),
(11,	'2023_12_04_182453_create_user_table',	1),
(12,	'2023_12_04_183036_create_subscription_type_table',	1),
(13,	'2023_12_04_183059_create_subscription_plan_table',	1),
(14,	'2023_12_04_183402_create_remote_server_status_table',	1),
(15,	'2023_12_04_183805_create_remote_server_table',	1),
(17,	'2023_12_05_074160_create_client_db_server_table',	1),
(20,	'2023_12_05_105557_create_client_db_schema_table',	1),
(21,	'2023_12_05_105558_create_client_db_server_table_table',	1),
(22,	'2023_12_05_105638_create_client_db_server_table_column_table',	1),
(23,	'2024_01_04_141250_create_assigned_port_table',	2),
(24,	'2023_12_05_074156_dim_date_table',	3),
(25,	'2023_12_05_081608_client_db_server_stat_table',	3),
(26,	'2023_12_05_105558_client_db_sever_table_stat_table',	3),
(27,	'2023_12_20_125152_create_object_storage_type_table',	3),
(29,	'2024_01_09_140148_create_jobs_table',	4),
(30,	'2024_01_10_083807_create_failed_jobs_table',	5),
(31,	'2024_01_15_123937_add_is_deleted_column_to_remote_server_table',	6),
(32,	'2024_01_15_124611_add_new_columns_to_client_db_server_table',	6),
(33,	'2024_01_15_140719_create_client_database_table',	6),
(34,	'2024_01_15_142535_remove_and_update_columns_from_client_db_schema_table',	7),
(35,	'2024_01_22_110400_add_and_remove_columns_to_client_db_server_table',	8),
(36,	'2024_01_22_110522_add_columns_to_client_db_server_table_column_table',	8),
(37,	'2024_01_22_120009_remove_column_from_client_db_schema_table',	8),
(38,	'2024_01_22_173044_add_column_is_deleted_to_client_database_table',	8),
(39,	'2024_01_23_145337_add_delete_at_column_to_client_database_table',	8),
(40,	'2024_01_26_120646_update_schema_id_to_database_id',	9),
(41,	'2024_01_29_125805_create_table_job_schedule_table',	9),
(42,	'2024_02_02_061055_change_schedule_time_clumns_type',	9),
(43,	'2024_02_02_062156_add_server_color_column_in_client_db_server',	9),
(44,	'2024_02_02_062507_add_db_color_column_in_client_database',	9),
(45,	'2024_02_02_062554_add_table_color_column_in_client_db_server_table',	9),
(46,	'2024_02_02_130950_create_database_job_schedule_table',	9),
(47,	'2024_02_02_132042_add_database_job_schedule_id_in_table_job_schedule',	9),
(48,	'2024_02_12_114233_create_table_process_status_table',	10),
(49,	'2024_02_12_114340_drop_table_process_status_id_column_from_client_db_server_table_table',	10),
(50,	'2024_02_12_114418_change_enum_into_foreign_key_to_client_db_server_table_table',	10),
(51,	'2024_02_12_131902_create_cloud_storage_type_table',	10),
(53,	'2024_02_12_131914_create_object_storage_type_table',	11),
(54,	'2024_02_12_134059_create_object_storage_table',	11),
(55,	'2023_12_20_131111_create_object_storage_export_history_table',	12),
(56,	'2024_02_06_084214_add_database_name_and_server_name_in_database_job_schedule',	13),
(57,	'2024_02_09_080150_change_columns_and_set_default_values',	13),
(58,	'2024_02_16_123754_create_object_storage_type_table',	14),
(59,	'2024_02_16_124836_create_client_object_storage_table',	14),
(60,	'2024_02_16_130433_create_object_storage_export_history_table',	14),
(61,	'2024_02_20_143220_add_day_name_in_dim_date_table',	15),
(62,	'2024_02_20_143226_create_populate_dim_date_sp',	16),
(63,	'2024_02_21_073113_add_and_remove_columns_in_client_db_server_table_stat',	16),
(64,	'2024_03_13_113443_change_table_job_schedule_foreign_key',	17),
(65,	'2024_03_11_140436_create_table_job_schedule_execution_log_table',	18),
(66,	'2024_03_15_100703_change_columns_name_in_table_job_schedule_execution_log_table',	19),
(67,	'2024_03_19_121250_add_schedule_encryption_key_in_table_job_schedule_execution_log_table',	19),
(68,	'2024_03_26_102333_change_default_value_of_file_export_end_at',	20);

DROP TABLE IF EXISTS `object_storage_export_history`;
CREATE TABLE `object_storage_export_history` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `client_db_server_table_id` bigint unsigned NOT NULL,
  `object_storage_type_id` tinyint unsigned NOT NULL,
  `table_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_exported_name` varchar(150) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'NA',
  `object_storage_dir_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'NA',
  `is_file_compressed` tinyint NOT NULL DEFAULT '1',
  `file_export_size` bigint unsigned NOT NULL,
  `file_stored_size` int unsigned NOT NULL DEFAULT '0',
  `file_compression_ratio` decimal(5,2) NOT NULL DEFAULT '0.00',
  `file_compression_percentage` decimal(5,2) NOT NULL DEFAULT '0.00',
  `total_row_exported` int unsigned NOT NULL DEFAULT '0',
  `file_export_started_at` datetime NOT NULL,
  `file_export_ended_at` datetime DEFAULT NULL,
  `export_status` varchar(15) COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_export_error` tinyint NOT NULL DEFAULT '0',
  `error_message` varchar(250) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_current` tinyint NOT NULL DEFAULT '1',
  `is_deleted` tinyint NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `object_storage_export_history_client_db_server_table_id_foreign` (`client_db_server_table_id`),
  KEY `object_storage_export_history_object_storage_type_id_foreign` (`object_storage_type_id`),
  CONSTRAINT `object_storage_export_history_client_db_server_table_id_foreign` FOREIGN KEY (`client_db_server_table_id`) REFERENCES `client_db_server_table` (`id`) ON DELETE CASCADE,
  CONSTRAINT `object_storage_export_history_object_storage_type_id_foreign` FOREIGN KEY (`object_storage_type_id`) REFERENCES `object_storage_type` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `object_storage_type`;
CREATE TABLE `object_storage_type` (
  `id` tinyint unsigned NOT NULL AUTO_INCREMENT,
  `object_storage_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'S3 Standard',
  `cloud_provider_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'AWS',
  `is_active` tinyint NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `object_storage_type` (`id`, `object_storage_type`, `cloud_provider_name`, `is_active`, `created_at`, `updated_at`) VALUES
(1,	'S3 Standard',	'AWS',	1,	'2024-02-26 12:34:58',	'2024-02-26 12:34:58'),
(2,	'S3 Intelligent-Tiering',	'AWS',	1,	'2024-02-26 12:34:58',	'2024-02-26 12:34:58'),
(3,	'S3 Standard-Infrequent Access',	'AWS',	1,	'2024-02-26 12:34:58',	'2024-02-26 12:34:58'),
(4,	'S3 One Zone-Infrequent Access',	'AWS',	1,	'2024-02-26 12:34:58',	'2024-02-26 12:34:58'),
(5,	'S3 Glacier',	'AWS',	1,	'2024-02-26 12:34:58',	'2024-02-26 12:34:58'),
(6,	'Google Standard Storage',	'GoogleCloud',	1,	'2024-02-26 12:34:58',	'2024-02-26 12:34:58'),
(7,	'Google Nearline Storage',	'GoogleCloud',	1,	'2024-02-26 12:34:58',	'2024-02-26 12:34:58'),
(8,	'Coldline Storage',	'GoogleCloud',	1,	'2024-02-26 12:34:58',	'2024-02-26 12:34:58'),
(9,	'Archive Storage',	'GoogleCloud',	1,	'2024-02-26 12:34:58',	'2024-02-26 12:34:58');

DROP TABLE IF EXISTS `personal_access_tokens`;
CREATE TABLE `personal_access_tokens` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `personal_access_tokens` (`id`, `tokenable_type`, `tokenable_id`, `name`, `token`, `abilities`, `last_used_at`, `expires_at`, `created_at`, `updated_at`) VALUES
(1,	'App\\Models\\User',	1,	'API Token',	'8aed8bdcae6d12030c4e2a0a6772660a0c3d71388a1a1abe6cf6e007451f0fc8',	'[\"*\"]',	'2023-12-21 16:46:16',	NULL,	'2023-12-21 16:40:20',	'2023-12-21 16:46:16'),
(2,	'App\\Models\\User',	1,	'API Token',	'231d1829e8ebef9f62a5ba2c26489c8d9e6b4886491385ecbe5757022ba7faf6',	'[\"*\"]',	'2023-12-22 13:01:19',	NULL,	'2023-12-21 16:58:36',	'2023-12-22 13:01:19'),
(3,	'App\\Models\\User',	1,	'API Token',	'b3e80c08c323c1935043b1f6863e83a66652f7f0a35fdbc696567e6e9f29bb4f',	'[\"*\"]',	NULL,	NULL,	'2023-12-29 09:55:16',	'2023-12-29 09:55:16'),
(4,	'App\\Models\\User',	1,	'API Token',	'0a062d3fe7aa649fa77a1df44234e99d701429fc8bf4402ce80f4e5600b9628d',	'[\"*\"]',	'2023-12-29 11:13:44',	NULL,	'2023-12-29 09:55:35',	'2023-12-29 11:13:44'),
(5,	'App\\Models\\User',	1,	'API Token',	'11b9920bfce8923bbcb0611a5687fd610b76fb1ff03873dccc94c4480aace7f8',	'[\"*\"]',	'2023-12-29 16:36:25',	NULL,	'2023-12-29 16:35:47',	'2023-12-29 16:36:25'),
(6,	'App\\Models\\User',	1,	'API Token',	'3f39ba7527b59ff7d2df716bcef530542caa69c85cd0204999ea46909bdbb348',	'[\"*\"]',	'2024-01-01 12:04:58',	NULL,	'2024-01-01 12:04:54',	'2024-01-01 12:04:58'),
(7,	'App\\Models\\User',	2,	'API Token',	'464ee7ee894bff1a94c4a60595e60f166d348e2411719574c054d093a0f02538',	'[\"*\"]',	'2024-01-02 10:11:54',	NULL,	'2024-01-01 12:52:28',	'2024-01-02 10:11:54'),
(8,	'App\\Models\\User',	1,	'API Token',	'0bd7df6efeb2761664edded8de57c581f8e136790284307d653c4bae47c725c3',	'[\"*\"]',	'2024-01-02 09:38:39',	NULL,	'2024-01-02 09:36:34',	'2024-01-02 09:38:39'),
(9,	'App\\Models\\User',	3,	'API Token',	'b374f89f97e48074e638b64db2a1036f551bdd7958a5f621407815ec5c289935',	'[\"*\"]',	'2024-01-02 11:01:32',	NULL,	'2024-01-02 10:50:49',	'2024-01-02 11:01:32'),
(10,	'App\\Models\\User',	4,	'API Token',	'40807de0ac4843f2bb8601da8456453a5c85c61474e214eab93ccd719e7cd792',	'[\"*\"]',	'2024-01-02 11:09:11',	NULL,	'2024-01-02 11:04:12',	'2024-01-02 11:09:11'),
(11,	'App\\Models\\User',	5,	'API Token',	'c834663de63444f7f5148150435a3e053e31c83897e7bbe04ce1baced8c21c70',	'[\"*\"]',	'2024-01-02 12:07:00',	NULL,	'2024-01-02 12:06:20',	'2024-01-02 12:07:00'),
(12,	'App\\Models\\User',	6,	'API Token',	'71a130be4c5096688fa5f8682d032cd24c9036a90ceca2d1466e1a56721a262a',	'[\"*\"]',	'2024-01-02 14:46:12',	NULL,	'2024-01-02 12:10:15',	'2024-01-02 14:46:12'),
(13,	'App\\Models\\User',	7,	'API Token',	'f5d70553d91cefc7bcbbdcd6ab5441a7de16573dea16d5df70bf165fb1591149',	'[\"*\"]',	'2024-01-09 09:15:43',	NULL,	'2024-01-02 12:13:21',	'2024-01-09 09:15:43'),
(14,	'App\\Models\\User',	6,	'API Token',	'2499d09e672552e8939d66b834e0c9f1dfc37885196b122d8c7e29d4828a70a9',	'[\"*\"]',	'2024-01-03 18:18:49',	NULL,	'2024-01-03 10:17:48',	'2024-01-03 18:18:49'),
(15,	'App\\Models\\User',	6,	'API Token',	'c2c8f99820b86366b45017c27a263f45abdb6bfa6bf149e397eae3e14ef8db20',	'[\"*\"]',	'2024-01-03 12:10:01',	NULL,	'2024-01-03 10:21:11',	'2024-01-03 12:10:01'),
(16,	'App\\Models\\User',	6,	'API Token',	'e7e76bf6646a51a872914a9e8253f18c6bb7f4b6bb46eb35e31e00b35301f6e3',	'[\"*\"]',	'2024-01-03 13:52:15',	NULL,	'2024-01-03 12:48:27',	'2024-01-03 13:52:15'),
(17,	'App\\Models\\User',	6,	'API Token',	'f7c15da39627c7eef2af26f4cf6048b4e6c77d42f2432b1d15dcac636fa80186',	'[\"*\"]',	NULL,	NULL,	'2024-01-03 14:07:46',	'2024-01-03 14:07:46'),
(18,	'App\\Models\\User',	6,	'API Token',	'59cff5f06fb83cd39ee7706a9a9aa89e6506fa161d42af36fc3adc0704bea3f1',	'[\"*\"]',	'2024-01-03 18:15:41',	NULL,	'2024-01-03 15:22:19',	'2024-01-03 18:15:41'),
(19,	'App\\Models\\User',	6,	'API Token',	'8269c7584a851c131dee9d4bcce37f644ed17ba1da9660138ff3d849cf127399',	'[\"*\"]',	'2024-01-03 19:19:44',	NULL,	'2024-01-03 19:19:41',	'2024-01-03 19:19:44'),
(20,	'App\\Models\\User',	6,	'API Token',	'dce462eeb2c6facde924ed073d94e1f277509a57039b3c6fb278ec427bdfe325',	'[\"*\"]',	'2024-01-04 17:12:56',	NULL,	'2024-01-04 08:22:47',	'2024-01-04 17:12:56'),
(21,	'App\\Models\\User',	6,	'API Token',	'b9da034bed20a5d2c811023eb8aed60de156033dd8e2d1f69c53ba04d739382b',	'[\"*\"]',	'2024-01-11 07:44:32',	NULL,	'2024-01-04 10:27:06',	'2024-01-11 07:44:32'),
(22,	'App\\Models\\User',	6,	'API Token',	'f6ddbdf6ca254e07f528ac6bf5e18076b33deca903bd72780345aefe0eaaf2ad',	'[\"*\"]',	'2024-01-04 13:08:04',	NULL,	'2024-01-04 12:42:38',	'2024-01-04 13:08:04'),
(23,	'App\\Models\\User',	8,	'API Token',	'0bb3b7e36d818d47af3eda7824394bb1b474641a94278668295b385856d1b679',	'[\"*\"]',	'2024-01-04 17:08:51',	NULL,	'2024-01-04 17:08:10',	'2024-01-04 17:08:51'),
(24,	'App\\Models\\User',	6,	'API Token',	'00ab0f46dcec43e84328aa1c3867f49914de2f251d8611f296f88d384b5a9dc6',	'[\"*\"]',	'2024-01-05 13:00:24',	NULL,	'2024-01-05 08:15:22',	'2024-01-05 13:00:24'),
(25,	'App\\Models\\User',	9,	'API Token',	'717a645e9c979c58b7f784dd31509505b706c56cb9c604f98e65fd81704c4108',	'[\"*\"]',	'2024-01-10 09:29:28',	NULL,	'2024-01-05 11:13:50',	'2024-01-10 09:29:28'),
(26,	'App\\Models\\User',	6,	'API Token',	'3edb8b80f9c9aa85e750769b4fe6e0ba70557e1640595fda2e88b13c39fa8483',	'[\"*\"]',	'2024-01-05 15:40:19',	NULL,	'2024-01-05 13:00:57',	'2024-01-05 15:40:19'),
(27,	'App\\Models\\User',	8,	'API Token',	'246e707b7b1ac144a0b1df12e7dd2f4962a1b7224cf495298bdde0b011e89ab6',	'[\"*\"]',	'2024-01-09 09:51:35',	NULL,	'2024-01-05 15:22:46',	'2024-01-09 09:51:35'),
(28,	'App\\Models\\User',	6,	'API Token',	'806c5b9f866465eb58c2db3520b627d49b5672f510bf152528646c5e4f43541b',	'[\"*\"]',	'2024-01-08 10:09:24',	NULL,	'2024-01-08 09:21:17',	'2024-01-08 10:09:24'),
(29,	'App\\Models\\User',	8,	'API Token',	'8fe21374175407aced651adc2389c997bc0f54a12b8cb091c077ac1961dabd90',	'[\"*\"]',	'2024-01-09 09:47:08',	NULL,	'2024-01-08 14:07:22',	'2024-01-09 09:47:08'),
(30,	'App\\Models\\User',	8,	'API Token',	'06c58c8c5f6eb63885fd97b9f18d4ab6cc2b713735eb446951982c9ef528f1e5',	'[\"*\"]',	'2024-01-08 14:47:02',	NULL,	'2024-01-08 14:15:18',	'2024-01-08 14:47:02'),
(31,	'App\\Models\\User',	6,	'API Token',	'872fee51c1c60d500e23efbd48ba2c18331b0d6327d49f5249cfda0d7254fd0d',	'[\"*\"]',	'2024-01-09 14:21:22',	NULL,	'2024-01-09 09:44:52',	'2024-01-09 14:21:22'),
(32,	'App\\Models\\User',	8,	'API Token',	'2fbbd4edce1456a2ce36b134b2853e86776b40324e2e7fcb5b90e1d2617d8a00',	'[\"*\"]',	'2024-01-10 10:28:21',	NULL,	'2024-01-09 09:47:07',	'2024-01-10 10:28:21'),
(34,	'App\\Models\\User',	8,	'API Token',	'3759ba51187e50561f0376f6f9c7cd19a5a6c6377ca71a863fc78317a1d03d6b',	'[\"*\"]',	'2024-01-09 15:28:26',	NULL,	'2024-01-09 15:28:26',	'2024-01-09 15:28:26'),
(35,	'App\\Models\\User',	8,	'API Token',	'4f30b828bc1b1cb129a2ed24c79189f10b4f1321dd1e7625900068f59f1af714',	'[\"*\"]',	'2024-01-09 15:33:21',	NULL,	'2024-01-09 15:32:42',	'2024-01-09 15:33:21'),
(36,	'App\\Models\\User',	8,	'API Token',	'be01156787adaa8fe07c026c296bb944b02bf9397963a985425a2a48b0f4d4b0',	'[\"*\"]',	'2024-01-09 15:33:19',	NULL,	'2024-01-09 15:33:19',	'2024-01-09 15:33:19'),
(37,	'App\\Models\\User',	8,	'API Token',	'45478816d7539db8e9acb7c4b9e74ed35207949830be3c215d8ecc7c1ab33466',	'[\"*\"]',	'2024-01-10 13:06:03',	NULL,	'2024-01-09 15:35:01',	'2024-01-10 13:06:03'),
(38,	'App\\Models\\User',	6,	'API Token',	'5087b4017ddaedf49c20b8cd983be650a19bb293f52137bda71591b69686d3b0',	'[\"*\"]',	'2024-01-10 08:44:37',	NULL,	'2024-01-10 08:31:35',	'2024-01-10 08:44:37'),
(40,	'App\\Models\\User',	6,	'API Token',	'c4400ceb7b7cb6f87038291134ed8e39f634e5a60a79302f69e13c99f9c713a3',	'[\"*\"]',	'2024-01-17 11:55:22',	NULL,	'2024-01-11 07:45:59',	'2024-01-17 11:55:22'),
(41,	'App\\Models\\User',	8,	'API Token',	'f49e8bc3d54b19dc0a3bf824d682140cb03201be73dee36e22af8eb55af95af1',	'[\"*\"]',	'2024-01-11 09:41:26',	NULL,	'2024-01-11 09:41:26',	'2024-01-11 09:41:26'),
(42,	'App\\Models\\User',	8,	'API Token',	'd1d4ad2febb45a8a483283690bd8e887d9fffb0c1b68a777d9d6732b4903f7ec',	'[\"*\"]',	'2024-01-11 17:30:10',	NULL,	'2024-01-11 09:54:50',	'2024-01-11 17:30:10'),
(43,	'App\\Models\\User',	9,	'API Token',	'8fc2b78d2f554eff40191b59d3960072dcd6c4e811870169cb2421b7c571a7c4',	'[\"*\"]',	NULL,	NULL,	'2024-01-11 11:25:08',	'2024-01-11 11:25:08'),
(44,	'App\\Models\\User',	9,	'API Token',	'b6e39e27e9b6cb530ba50a58520a945c7535be3d299bb26a1a405e589f03bb84',	'[\"*\"]',	'2024-01-22 16:58:48',	NULL,	'2024-01-11 11:35:02',	'2024-01-22 16:58:48'),
(45,	'App\\Models\\User',	8,	'API Token',	'c36215633744f42e242562736dd91eeb42b0e8465dc79525230f7d1317747486',	'[\"*\"]',	'2024-01-12 13:20:34',	NULL,	'2024-01-11 12:53:04',	'2024-01-12 13:20:34'),
(46,	'App\\Models\\User',	6,	'API Token',	'af756b37c3b5b80c7b676106cfd08d104e28560123b8442b1973425d3e28fd38',	'[\"*\"]',	'2024-01-12 12:55:31',	NULL,	'2024-01-11 17:01:51',	'2024-01-12 12:55:31'),
(48,	'App\\Models\\User',	6,	'API Token',	'a65936b1f3dd34e7168588bb1199d0aa5f500ded1a80e2588049f1e06ba43eaa',	'[\"*\"]',	'2024-01-15 12:35:10',	NULL,	'2024-01-15 12:34:58',	'2024-01-15 12:35:10'),
(49,	'App\\Models\\User',	7,	'API Token',	'eb7f23c55bb110e2fcc166a889a68cba1707a5f34e3d6e27cc315a75daf2868f',	'[\"*\"]',	NULL,	NULL,	'2024-01-15 12:44:40',	'2024-01-15 12:44:40'),
(51,	'App\\Models\\User',	8,	'API Token',	'd1fd74f07ebe3e9c5824422cd03a41a52563df27145edd0f71f2498310b9075c',	'[\"*\"]',	'2024-01-16 13:31:07',	NULL,	'2024-01-16 13:26:26',	'2024-01-16 13:31:07'),
(52,	'App\\Models\\User',	8,	'API Token',	'3f5bcf09be64cad7d6a8799f1948a4a5bd8ddf00e477de9db9d37e972a62f675',	'[\"*\"]',	'2024-01-16 13:38:29',	NULL,	'2024-01-16 13:33:15',	'2024-01-16 13:38:29'),
(54,	'App\\Models\\User',	8,	'API Token',	'1aa59bdc07181d6a002589f822341b45ff0c7983d3637f931ac065604d04e1ff',	'[\"*\"]',	'2024-01-17 13:51:16',	NULL,	'2024-01-17 13:51:00',	'2024-01-17 13:51:16'),
(55,	'App\\Models\\User',	6,	'API Token',	'2686c3429a7c6420bb873b190ecd88780df3080e7699b84092360931cb7cbd71',	'[\"*\"]',	'2024-01-18 17:42:41',	NULL,	'2024-01-18 17:06:24',	'2024-01-18 17:42:41'),
(57,	'App\\Models\\User',	6,	'API Token',	'29402ec7051712e5b023a40d8b00942055d0271af3863130ca78a38fefe736ab',	'[\"*\"]',	'2024-01-22 10:20:36',	NULL,	'2024-01-22 10:19:14',	'2024-01-22 10:20:36'),
(58,	'App\\Models\\User',	7,	'API Token',	'06b7bea3e1e8a0bbfda9387c9aaeb2fe6ea82b013abe4a73a7cdabe5b325ce92',	'[\"*\"]',	NULL,	NULL,	'2024-01-22 12:45:42',	'2024-01-22 12:45:42'),
(59,	'App\\Models\\User',	7,	'API Token',	'51d1db993c7ef8fb397432c1ba95918d587934478186c53cf9dd4056f3144ded',	'[\"*\"]',	'2024-01-22 12:46:36',	NULL,	'2024-01-22 12:46:06',	'2024-01-22 12:46:36'),
(60,	'App\\Models\\User',	7,	'API Token',	'673167be8160af1423767499fdc3dcbf83660a3bfe36a8bcc1562e2319b10cc4',	'[\"*\"]',	'2024-01-22 13:16:07',	NULL,	'2024-01-22 12:58:20',	'2024-01-22 13:16:07'),
(63,	'App\\Models\\User',	6,	'API Token',	'714cf2a47a6685f3a3addac432ee1a7ca17e2244fa0a129486367ff2aa8ce05c',	'[\"*\"]',	'2024-01-23 12:28:54',	NULL,	'2024-01-23 12:28:42',	'2024-01-23 12:28:54'),
(64,	'App\\Models\\User',	6,	'API Token',	'd704d80360b5aa76550931ad03fc2be9f23c955b5650e8ed79814b97f63d6f83',	'[\"*\"]',	'2024-01-24 12:30:36',	NULL,	'2024-01-24 10:13:04',	'2024-01-24 12:30:36'),
(68,	'App\\Models\\User',	9,	'API Token',	'4bbdaeeea8c19611150986aa5f7d7bf209afc896dac2f830734e443be58ee285',	'[\"*\"]',	'2024-01-24 10:52:19',	NULL,	'2024-01-24 10:52:08',	'2024-01-24 10:52:19'),
(70,	'App\\Models\\User',	12,	'API Token',	'709e1880959badf88325b9f7c324295e04a15071c65c4fe314ced6593297f18d',	'[\"*\"]',	'2024-01-24 11:13:49',	NULL,	'2024-01-24 11:12:41',	'2024-01-24 11:13:49'),
(74,	'App\\Models\\User',	13,	'API Token',	'f9b5e2cde109476909f494adfc5281ff7b6994f4f57bd775941ac04a479db4ca',	'[\"*\"]',	'2024-01-24 13:01:54',	NULL,	'2024-01-24 12:59:24',	'2024-01-24 13:01:54'),
(78,	'App\\Models\\User',	7,	'API Token',	'9a2375454f69f8a107dd8d5be4f20fd4ce888bcde50d956466a0d5efe8efcfe0',	'[\"*\"]',	'2024-01-25 10:26:26',	NULL,	'2024-01-25 10:25:54',	'2024-01-25 10:26:26'),
(80,	'App\\Models\\User',	8,	'API Token',	'3b11fd609d25111c9236fc3f812860447dcf072b46f45fea0d24861440481eda',	'[\"*\"]',	'2024-01-25 14:18:59',	NULL,	'2024-01-25 14:18:57',	'2024-01-25 14:18:59'),
(85,	'App\\Models\\User',	16,	'API Token',	'89bc2d0e95c25560cd99c7cfa3d3eaf1f9d427076a9b16d4a3a9c9c49076c26a',	'[\"*\"]',	NULL,	NULL,	'2024-01-29 08:50:02',	'2024-01-29 08:50:02'),
(87,	'App\\Models\\User',	19,	'API Token',	'41d76920830b1fe2f7a406f14426dc9c3c24d6cba82b60f2abea0722392c1948',	'[\"*\"]',	'2024-02-06 14:44:10',	NULL,	'2024-02-06 14:43:43',	'2024-02-06 14:44:10'),
(88,	'App\\Models\\User',	6,	'API Token',	'03a8c02cfd08fcfeb98d2b7700b788209e3234cf1b656d0a03c1b66badf069be',	'[\"*\"]',	'2024-02-15 12:18:33',	NULL,	'2024-02-14 14:54:52',	'2024-02-15 12:18:33'),
(89,	'App\\Models\\User',	16,	'API Token',	'4ee7dc6a6c8edde135ca650c76dac71e3e3314fc367d90ec30237a466f76f5db',	'[\"*\"]',	'2024-02-14 15:29:44',	NULL,	'2024-02-14 15:29:42',	'2024-02-14 15:29:44'),
(91,	'App\\Models\\User',	16,	'API Token',	'c2a13ad5cc80a088bb8ec9a266306f5b1b3c5ce3cb627b549e5609d47d7bbc7d',	'[\"*\"]',	'2024-02-15 08:20:04',	NULL,	'2024-02-15 08:15:14',	'2024-02-15 08:20:04'),
(92,	'App\\Models\\User',	8,	'API Token',	'98bab6a66bf40e89974a648067ad312715ca68c45aedb090bbcbee0625048f6b',	'[\"*\"]',	'2024-02-28 07:53:44',	NULL,	'2024-02-15 09:35:49',	'2024-02-28 07:53:44'),
(94,	'App\\Models\\User',	8,	'API Token',	'7160bf3e9784d5f13306edf9f93af1b5f416aa1fc9ec4db136e6f6bb4271b443',	'[\"*\"]',	'2024-02-15 10:25:06',	NULL,	'2024-02-15 10:18:30',	'2024-02-15 10:25:06'),
(96,	'App\\Models\\User',	8,	'API Token',	'0f310f9444e0ead92d55f959ca196d582ad32780dc4cd3cbbca9e6b88a147d5c',	'[\"*\"]',	'2024-02-15 10:31:10',	NULL,	'2024-02-15 10:30:52',	'2024-02-15 10:31:10'),
(97,	'App\\Models\\User',	8,	'API Token',	'79319b6b90c839b8cd8104b452705788cb30f383c9369a6633a0cea0491dc917',	'[\"*\"]',	'2024-02-15 10:31:16',	NULL,	'2024-02-15 10:31:15',	'2024-02-15 10:31:16'),
(98,	'App\\Models\\User',	8,	'API Token',	'b0784be1768376fa21aeb7377421b7c4f09e515d09aa6ded0e564d38742265bb',	'[\"*\"]',	'2024-02-15 12:09:04',	NULL,	'2024-02-15 10:41:08',	'2024-02-15 12:09:04'),
(99,	'App\\Models\\User',	20,	'API Token',	'a2b0dd4ae4cd10303c85ee34570a05c3d6b764b503e123f4d3cb8c59781d80a1',	'[\"*\"]',	'2024-02-16 15:04:20',	NULL,	'2024-02-16 13:45:34',	'2024-02-16 15:04:20'),
(100,	'App\\Models\\User',	8,	'API Token',	'a2e9d3548b82e4a91d53d566b0380fa99841aa1849175e7fce99aae746f1a71f',	'[\"*\"]',	'2024-03-14 12:29:36',	NULL,	'2024-02-16 14:34:26',	'2024-03-14 12:29:36'),
(101,	'App\\Models\\User',	16,	'API Token',	'e0ae7ae7776fdadd4771a71a00b596179db24df7baa22619b8a7eb038324c5ca',	'[\"*\"]',	'2024-02-20 14:58:14',	NULL,	'2024-02-16 15:25:55',	'2024-02-20 14:58:14'),
(102,	'App\\Models\\User',	16,	'API Token',	'e63175b1bc113378f4e0c6478b7a114f8a61ec90700e47c7af1f4b80a2d50e8a',	'[\"*\"]',	'2024-02-19 07:27:12',	NULL,	'2024-02-16 15:30:39',	'2024-02-19 07:27:12'),
(103,	'App\\Models\\User',	16,	'API Token',	'09628f8dc9925eafe82be3b47e0de2212df7734896c9ef63d69360ad3d2ea425',	'[\"*\"]',	'2024-02-16 18:09:05',	NULL,	'2024-02-16 18:03:45',	'2024-02-16 18:09:05'),
(105,	'App\\Models\\User',	6,	'API Token',	'97099118562036f41efea55f3dd36bb5e6c7e57b9945cfae6d95e62f77c7e7fc',	'[\"*\"]',	'2024-02-19 15:02:25',	NULL,	'2024-02-19 14:48:59',	'2024-02-19 15:02:25'),
(106,	'App\\Models\\User',	16,	'API Token',	'e8fc97656e356cfcc2a9ec931aecca76669fcc285310d98ab9798fe0fa358211',	'[\"*\"]',	'2024-02-22 09:52:42',	NULL,	'2024-02-22 09:52:33',	'2024-02-22 09:52:42'),
(107,	'App\\Models\\User',	16,	'API Token',	'e68193e417ca7918ca2d0945d1d9e261524029d6189da1415e2d3b59ea9c2985',	'[\"*\"]',	'2024-02-22 13:41:06',	NULL,	'2024-02-22 13:40:53',	'2024-02-22 13:41:06'),
(108,	'App\\Models\\User',	16,	'API Token',	'f9dda3aa67d75e270d11f001df6546d86cc50c06d30b370709102821dbd9b653',	'[\"*\"]',	'2024-02-26 12:31:02',	NULL,	'2024-02-26 09:18:54',	'2024-02-26 12:31:02'),
(109,	'App\\Models\\User',	6,	'API Token',	'428694c326d980af657d9de89398ab8373ad76f900f821a7624bdfbfcaddb137',	'[\"*\"]',	'2024-03-04 16:00:40',	NULL,	'2024-02-26 12:19:12',	'2024-03-04 16:00:40'),
(110,	'App\\Models\\User',	17,	'API Token',	'af1e950dce036ccfc4511eca530534bfe3730734fa75b6064e699c47ea76bbd4',	'[\"*\"]',	NULL,	NULL,	'2024-02-26 12:33:38',	'2024-02-26 12:33:38'),
(111,	'App\\Models\\User',	17,	'API Token',	'bd16d5e225ee0598ae94191e4fc50ac903e29e523a13d5a07baf648ba220b3b3',	'[\"*\"]',	'2024-02-26 12:37:58',	NULL,	'2024-02-26 12:34:06',	'2024-02-26 12:37:58'),
(112,	'App\\Models\\User',	16,	'API Token',	'addf1c13790064bc5e384fa6cffc29248f64643ad94473a6e95e117d848cd768',	'[\"*\"]',	'2024-02-27 14:29:01',	NULL,	'2024-02-27 14:28:59',	'2024-02-27 14:29:01'),
(114,	'App\\Models\\User',	8,	'API Token',	'73033ab3f76a3da7f4e5ef59e0378598ed0f70318c6ecc1fd9c6ad5a0757792d',	'[\"*\"]',	'2024-03-12 10:04:20',	NULL,	'2024-02-29 17:23:13',	'2024-03-12 10:04:20'),
(115,	'App\\Models\\User',	16,	'API Token',	'f6de5b6c16e3184298ecb23964ccdbf5b720a5cc25df73df553b9b3fa316b8b4',	'[\"*\"]',	'2024-02-29 17:30:08',	NULL,	'2024-02-29 17:30:02',	'2024-02-29 17:30:08'),
(116,	'App\\Models\\User',	16,	'API Token',	'e2852c3da55d21f45e3680015102c7aaf3ee0cda38dfa642e323a7a0ccbe38e6',	'[\"*\"]',	'2024-03-04 08:10:35',	NULL,	'2024-03-01 06:29:32',	'2024-03-04 08:10:35'),
(117,	'App\\Models\\User',	17,	'API Token',	'1ea745822ee36251735ad2accb7df9f4e8c55552fb64fec47ef9740dae06d8fc',	'[\"*\"]',	NULL,	NULL,	'2024-03-05 15:21:17',	'2024-03-05 15:21:17'),
(118,	'App\\Models\\User',	17,	'API Token',	'ed4ef06db2d12bf539d85bcffb63c3dea28b106289d2df6108a8e66907854351',	'[\"*\"]',	'2024-03-11 11:59:31',	NULL,	'2024-03-05 15:21:38',	'2024-03-11 11:59:31'),
(120,	'App\\Models\\User',	6,	'API Token',	'8c627bd5174deafd1d1db09beee2e1195d450c16a460d1d2cb604c559fde0eab',	'[\"*\"]',	'2024-03-08 15:17:02',	NULL,	'2024-03-08 14:56:08',	'2024-03-08 15:17:02'),
(121,	'App\\Models\\User',	16,	'API Token',	'481cc5e94de0de1a60bdf5fb4c78329b6984fd791c5c36c7afa8690f5dc72a30',	'[\"*\"]',	'2024-03-14 09:36:08',	NULL,	'2024-03-09 05:22:19',	'2024-03-14 09:36:08'),
(124,	'App\\Models\\User',	8,	'API Token',	'6e87772eb4dbc91d0f966f43fe7b636edc5292e7d68808c7c0d5e7157e3fef7f',	'[\"*\"]',	'2024-04-23 13:07:49',	NULL,	'2024-03-12 16:08:01',	'2024-04-23 13:07:49'),
(126,	'App\\Models\\User',	8,	'API Token',	'8f535216d6543eddd7043f83177721aabad026b6027323aedb70dc7d728b6e15',	'[\"*\"]',	'2024-03-13 10:52:22',	NULL,	'2024-03-13 10:49:30',	'2024-03-13 10:52:22'),
(127,	'App\\Models\\User',	8,	'API Token',	'268652e3d496e5f407f89a1d00d7eb768df569ed8eb183c3e7a5b4348e1e05a3',	'[\"*\"]',	'2024-03-13 11:46:55',	NULL,	'2024-03-13 10:54:01',	'2024-03-13 11:46:55'),
(128,	'App\\Models\\User',	8,	'API Token',	'8be9b8097d497fee9af98f385070f097f7b51761bccc5e23bf653b9110ba6739',	'[\"*\"]',	'2024-03-14 08:21:38',	NULL,	'2024-03-14 08:21:37',	'2024-03-14 08:21:38'),
(129,	'App\\Models\\User',	21,	'API Token',	'48a2b4926d9073bd9360033955a3a70132f311f3ca06eae46859a510b44e126a',	'[\"*\"]',	'2024-04-30 08:18:08',	NULL,	'2024-03-19 09:49:30',	'2024-04-30 08:18:08'),
(130,	'App\\Models\\User',	8,	'API Token',	'602584475e2f8d4ea0fea056f60d82c8e6e7f89f37fb4ecde312586fb0e28b5a',	'[\"*\"]',	'2024-03-21 10:09:33',	NULL,	'2024-03-21 07:47:06',	'2024-03-21 10:09:33'),
(131,	'App\\Models\\User',	22,	'API Token',	'e692dccb9d2dc6cc93e7cca65f4e037065d3e407f9a7b21ddc9755da865a6940',	'[\"*\"]',	'2024-04-18 22:15:24',	NULL,	'2024-04-18 22:14:54',	'2024-04-18 22:15:24'),
(132,	'App\\Models\\User',	8,	'API Token',	'c6eae38b5d31f6075aa0a35548f8c547d712a900576bb9407f37de738f79e453',	'[\"*\"]',	'2024-04-23 13:30:25',	NULL,	'2024-04-23 13:15:09',	'2024-04-23 13:30:25');

DROP TABLE IF EXISTS `remote_server`;
CREATE TABLE `remote_server` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `company_id` bigint unsigned NOT NULL,
  `hostname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `port` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `agent_uuid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `os_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `remote_server_status_id` bigint unsigned NOT NULL,
  `is_tls_required` tinyint(1) NOT NULL DEFAULT '0',
  `is_valid` tinyint(1) NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `is_deleted` tinyint NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `remote_server_name_unique` (`name`),
  UNIQUE KEY `remote_server_hostname_unique` (`hostname`),
  KEY `remote_server_company_id_foreign` (`company_id`),
  KEY `remote_server_remote_server_status_id_foreign` (`remote_server_status_id`),
  CONSTRAINT `remote_server_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `company` (`id`) ON DELETE CASCADE,
  CONSTRAINT `remote_server_remote_server_status_id_foreign` FOREIGN KEY (`remote_server_status_id`) REFERENCES `remote_server_status` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `remote_server` (`id`, `name`, `company_id`, `hostname`, `username`, `port`, `agent_uuid`, `os_type`, `remote_server_status_id`, `is_tls_required`, `is_valid`, `is_active`, `deleted_at`, `created_at`, `updated_at`, `is_deleted`) VALUES
(5,	'Test-agent',	2,	'Database Server',	'Data-Admin',	'2511',	'd9cd849e50964a28a6ba047fa9c97736',	NULL,	3,	0,	0,	1,	'2024-01-01 13:10:02',	'2024-01-01 13:07:02',	'2024-01-01 13:10:02',	0),
(6,	'TestAgent',	2,	'Database',	'Data-Admin',	'1111',	'deb1ee2733e44e5e9f70fa767aa14d1d',	NULL,	3,	0,	0,	1,	NULL,	'2024-01-01 13:16:08',	'2024-01-01 13:16:08',	0),
(7,	'Test agent',	10,	'projects.gigalabs.com',	'ubuntuu',	'123',	'8417b4142f684a94a702cfb3413e9c3a',	NULL,	3,	0,	0,	1,	'2024-01-24 11:03:11',	'2024-01-02 12:44:10',	'2024-01-24 11:03:11',	1),
(8,	'Qasim',	7,	'Ahmad',	'Fiaz',	'123',	'9ff541f5ce654431a9e46f061a472d94',	NULL,	3,	1,	0,	1,	NULL,	'2024-01-15 12:53:16',	'2024-01-15 12:53:58',	0),
(9,	'My 2nd Agent',	10,	'projects+1gigalabs.co',	'DataAdmin',	'25',	'c59ce0f11f1a45d1a04d0204059b2903',	NULL,	3,	0,	0,	1,	NULL,	'2024-01-24 11:03:30',	'2024-01-24 11:03:30',	0),
(10,	'local host',	9,	'ec2-54-161-20e5-13.compute-1.amazonaws.com',	'Fiaze',	'25',	'a93f17eb69e5418b9cb45a27e385910c',	NULL,	3,	0,	0,	1,	'2024-01-26 11:09:19',	'2024-01-26 09:39:43',	'2024-01-26 11:09:19',	1),
(11,	'f',	8,	'f',	'f',	'20',	'a2b30811a660415eb3abb69d550f3252',	NULL,	3,	0,	0,	1,	'2024-03-01 07:35:17',	'2024-03-01 07:33:51',	'2024-03-01 07:35:17',	1);

DROP TABLE IF EXISTS `remote_server_status`;
CREATE TABLE `remote_server_status` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `remote_server_status_name_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `remote_server_status` (`id`, `name`, `is_active`, `deleted_at`, `created_at`, `updated_at`) VALUES
(1,	'Completed',	1,	NULL,	'2023-12-21 17:54:42',	'2023-12-21 17:54:42'),
(2,	'Pending',	1,	NULL,	'2023-12-21 17:54:43',	'2023-12-21 17:54:43'),
(3,	'Not Connected',	1,	NULL,	'2023-12-21 17:54:44',	'2023-12-21 17:54:44');

DROP TABLE IF EXISTS `state`;
CREATE TABLE `state` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `state_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `country_id` bigint unsigned NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `state_state_name_unique` (`state_name`),
  KEY `state_country_id_foreign` (`country_id`),
  CONSTRAINT `state_country_id_foreign` FOREIGN KEY (`country_id`) REFERENCES `country` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `state` (`id`, `state_name`, `country_id`, `is_active`, `deleted_at`, `created_at`, `updated_at`) VALUES
(1,	'Alabama',	1,	1,	NULL,	'2023-12-21 13:55:49',	'2023-12-21 13:55:49'),
(2,	'Alaska',	1,	1,	NULL,	'2023-12-21 13:55:50',	'2023-12-21 13:55:50'),
(3,	'Arizona',	1,	1,	NULL,	'2023-12-21 13:55:51',	'2023-12-21 13:55:51'),
(4,	'Arkansas',	1,	1,	NULL,	'2023-12-21 13:55:52',	'2023-12-21 13:55:52'),
(5,	'California',	1,	1,	NULL,	'2023-12-21 13:55:53',	'2023-12-21 13:55:53'),
(6,	'Colorado',	1,	1,	NULL,	'2023-12-21 13:55:53',	'2023-12-21 13:55:53'),
(7,	'Connecticut',	1,	1,	NULL,	'2023-12-21 13:55:54',	'2023-12-21 13:55:54'),
(8,	'Delaware',	1,	1,	NULL,	'2023-12-21 13:55:55',	'2023-12-21 13:55:55'),
(9,	'District of Columbia',	1,	1,	NULL,	'2023-12-21 13:55:56',	'2023-12-21 13:55:56'),
(10,	'Florida',	1,	1,	NULL,	'2023-12-21 13:55:57',	'2023-12-21 13:55:57'),
(11,	'Georgia',	1,	1,	NULL,	'2023-12-21 13:55:58',	'2023-12-21 13:55:58'),
(12,	'Hawaii',	1,	1,	NULL,	'2023-12-21 13:55:59',	'2023-12-21 13:55:59'),
(13,	'Idaho',	1,	1,	NULL,	'2023-12-21 13:55:59',	'2023-12-21 13:55:59'),
(14,	'Illinois',	1,	1,	NULL,	'2023-12-21 13:56:00',	'2023-12-21 13:56:00'),
(15,	'Indiana',	1,	1,	NULL,	'2023-12-21 13:56:01',	'2023-12-21 13:56:01'),
(16,	'Iowa',	1,	1,	NULL,	'2023-12-21 13:56:02',	'2023-12-21 13:56:02'),
(17,	'Kansas',	1,	1,	NULL,	'2023-12-21 13:56:03',	'2023-12-21 13:56:03'),
(18,	'Kentucky',	1,	1,	NULL,	'2023-12-21 13:56:04',	'2023-12-21 13:56:04'),
(19,	'Louisiana',	1,	1,	NULL,	'2023-12-21 13:56:04',	'2023-12-21 13:56:04'),
(20,	'Maine',	1,	1,	NULL,	'2023-12-21 13:56:05',	'2023-12-21 13:56:05'),
(21,	'Maryland',	1,	1,	NULL,	'2023-12-21 13:56:06',	'2023-12-21 13:56:06'),
(22,	'Massachusetts',	1,	1,	NULL,	'2023-12-21 13:56:07',	'2023-12-21 13:56:07'),
(23,	'Michigan',	1,	1,	NULL,	'2023-12-21 13:56:08',	'2023-12-21 13:56:08'),
(24,	'Minnesota',	1,	1,	NULL,	'2023-12-21 13:56:09',	'2023-12-21 13:56:09'),
(25,	'Mississippi',	1,	1,	NULL,	'2023-12-21 13:56:10',	'2023-12-21 13:56:10'),
(26,	'Missouri',	1,	1,	NULL,	'2023-12-21 13:56:10',	'2023-12-21 13:56:10'),
(27,	'Montana',	1,	1,	NULL,	'2023-12-21 13:56:11',	'2023-12-21 13:56:11'),
(28,	'Nebraska',	1,	1,	NULL,	'2023-12-21 13:56:12',	'2023-12-21 13:56:12'),
(29,	'Nevada',	1,	1,	NULL,	'2023-12-21 13:56:13',	'2023-12-21 13:56:13'),
(30,	'New Hampshire',	1,	1,	NULL,	'2023-12-21 13:56:14',	'2023-12-21 13:56:14'),
(31,	'New Jersey',	1,	1,	NULL,	'2023-12-21 13:56:15',	'2023-12-21 13:56:15'),
(32,	'New Mexico',	1,	1,	NULL,	'2023-12-21 13:56:15',	'2023-12-21 13:56:15'),
(33,	'New York',	1,	1,	NULL,	'2023-12-21 13:56:16',	'2023-12-21 13:56:16'),
(34,	'North Carolina',	1,	1,	NULL,	'2023-12-21 13:56:17',	'2023-12-21 13:56:17'),
(35,	'North Dakota',	1,	1,	NULL,	'2023-12-21 13:56:18',	'2023-12-21 13:56:18'),
(36,	'Ohio',	1,	1,	NULL,	'2023-12-21 13:56:19',	'2023-12-21 13:56:19'),
(37,	'Oklahoma',	1,	1,	NULL,	'2023-12-21 13:56:20',	'2023-12-21 13:56:20'),
(38,	'Oregon',	1,	1,	NULL,	'2023-12-21 13:56:21',	'2023-12-21 13:56:21'),
(39,	'Pennsylvania',	1,	1,	NULL,	'2023-12-21 13:56:21',	'2023-12-21 13:56:21'),
(40,	'Rhode Island',	1,	1,	NULL,	'2023-12-21 13:56:22',	'2023-12-21 13:56:22'),
(41,	'South Carolina',	1,	1,	NULL,	'2023-12-21 13:56:23',	'2023-12-21 13:56:23'),
(42,	'South Dakota',	1,	1,	NULL,	'2023-12-21 13:56:24',	'2023-12-21 13:56:24'),
(43,	'Tennessee',	1,	1,	NULL,	'2023-12-21 13:56:25',	'2023-12-21 13:56:25'),
(44,	'Texas',	1,	1,	NULL,	'2023-12-21 13:56:26',	'2023-12-21 13:56:26'),
(45,	'Utah',	1,	1,	NULL,	'2023-12-21 13:56:27',	'2023-12-21 13:56:27'),
(46,	'Vermont',	1,	1,	NULL,	'2023-12-21 13:56:27',	'2023-12-21 13:56:27'),
(47,	'Virginia',	1,	1,	NULL,	'2023-12-21 13:56:28',	'2023-12-21 13:56:28'),
(48,	'Washington',	1,	1,	NULL,	'2023-12-21 13:56:29',	'2023-12-21 13:56:29'),
(49,	'West Virginia',	1,	1,	NULL,	'2023-12-21 13:56:30',	'2023-12-21 13:56:30'),
(50,	'Wisconsin',	1,	1,	NULL,	'2023-12-21 13:56:31',	'2023-12-21 13:56:31'),
(51,	'Wyoming',	1,	1,	NULL,	'2023-12-21 13:56:32',	'2023-12-21 13:56:32');

DROP TABLE IF EXISTS `subscription`;
CREATE TABLE `subscription` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `stripe_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `stripe_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `stripe_price` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `quantity` int DEFAULT NULL,
  `trial_ends_at` timestamp NULL DEFAULT NULL,
  `ends_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `subscription_stripe_id_unique` (`stripe_id`),
  KEY `subscription_user_id_stripe_status_index` (`user_id`,`stripe_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `subscription` (`id`, `user_id`, `name`, `stripe_id`, `stripe_status`, `stripe_price`, `quantity`, `trial_ends_at`, `ends_at`, `deleted_at`, `created_at`, `updated_at`) VALUES
(1,	6,	'Medium',	'sub_1OU6o1ER0DocJpyM4CX5Vki9',	'active',	'price_1NfephER0DocJpyMd8E0Yab1',	1,	NULL,	NULL,	NULL,	'2024-01-02 12:10:51',	'2024-01-02 12:10:54'),
(2,	7,	'Medium',	'sub_1OU6rcER0DocJpyMSOjqaUgr',	'active',	'price_1NfekOER0DocJpyMfFKWMm3p',	1,	NULL,	'2025-01-02 12:14:32',	NULL,	'2024-01-02 12:14:33',	'2024-01-02 12:41:29'),
(3,	7,	'Medium',	'sub_1OU7HiER0DocJpyM9cE486FV',	'active',	'price_1NfephER0DocJpyMd8E0Yab1',	1,	NULL,	'2024-02-02 12:41:30',	NULL,	'2024-01-02 12:41:31',	'2024-01-15 12:50:41'),
(4,	7,	'High',	'sub_1OYpaZER0DocJpyMBKJibGDt',	'active',	'price_1Nfei0ER0DocJpyMcHcBLUvb',	1,	NULL,	'2025-01-15 12:48:27',	NULL,	'2024-01-15 12:48:28',	'2024-01-15 12:50:40'),
(5,	7,	'High',	'sub_1OYpckER0DocJpyM3WcNdiln',	'active',	'price_1NfemzER0DocJpyMJC8Irp8T',	1,	NULL,	'2024-02-15 12:50:42',	NULL,	'2024-01-15 12:50:43',	'2024-01-19 10:57:14'),
(6,	7,	'Medium',	'sub_1OYpknER0DocJpyMI0VVqnn8',	'active',	'price_1NfephER0DocJpyMd8E0Yab1',	1,	NULL,	'2024-02-15 12:59:01',	NULL,	'2024-01-15 12:59:02',	'2024-01-19 10:57:14'),
(7,	7,	'High',	'sub_1OaFlBER0DocJpyMz5BjXEa1',	'active',	'price_1NfemzER0DocJpyMJC8Irp8T',	1,	NULL,	'2024-02-19 10:57:17',	NULL,	'2024-01-19 10:57:18',	'2024-01-19 11:12:20'),
(8,	7,	'High',	'sub_1ObMhuER0DocJpyMTDM7Q8qG',	'active',	'price_1Nfei0ER0DocJpyMcHcBLUvb',	1,	NULL,	NULL,	NULL,	'2024-01-22 12:34:31',	'2024-01-22 12:34:34'),
(9,	10,	'High',	'sub_1Oc4CNER0DocJpyMdZ0TDryM',	'active',	'price_1Nfei0ER0DocJpyMcHcBLUvb',	1,	NULL,	'2025-01-24 11:00:51',	NULL,	'2024-01-24 11:00:52',	'2024-01-24 11:02:04'),
(10,	10,	'High',	'sub_1Oc4DZER0DocJpyMYkNw9Bdu',	'active',	'price_1NfemzER0DocJpyMJC8Irp8T',	1,	NULL,	NULL,	NULL,	'2024-01-24 11:02:06',	'2024-01-24 11:02:08'),
(11,	12,	'Lite',	'sub_1Oc4OBER0DocJpyMi4UTMzTY',	'active',	'price_1Nfer4ER0DocJpyMSyEDM7Ps',	1,	NULL,	NULL,	NULL,	'2024-01-24 11:13:04',	'2024-01-24 11:13:06'),
(12,	13,	'High',	'sub_1Oc5zWER0DocJpyM2RpETLNO',	'active',	'price_1Nfei0ER0DocJpyMcHcBLUvb',	1,	NULL,	NULL,	NULL,	'2024-01-24 12:55:43',	'2024-01-24 12:55:45'),
(13,	14,	'Lite',	'sub_1OcTinER0DocJpyMr9CYn9IG',	'active',	'price_1Nfer4ER0DocJpyMSyEDM7Ps',	1,	NULL,	NULL,	NULL,	'2024-01-25 14:16:02',	'2024-01-25 14:16:04'),
(14,	9,	'Medium',	'sub_1Oclc9ER0DocJpyMdPrhV8Hz',	'active',	'price_1NfephER0DocJpyMd8E0Yab1',	1,	NULL,	NULL,	NULL,	'2024-01-26 09:22:22',	'2024-01-26 09:22:24'),
(15,	17,	'Lite',	'sub_1OeaUGER0DocJpyMITdeFyTk',	'active',	'price_1Nfer4ER0DocJpyMSyEDM7Ps',	1,	NULL,	NULL,	NULL,	'2024-01-31 09:53:45',	'2024-01-31 09:53:47'),
(16,	20,	'Lite',	'sub_1OkRjtER0DocJpyMt6L4GWIL',	'active',	'price_1Nfer4ER0DocJpyMSyEDM7Ps',	1,	NULL,	NULL,	NULL,	'2024-02-16 13:46:06',	'2024-02-16 13:46:08'),
(17,	21,	'Lite',	'sub_1OvzJCER0DocJpyMF9DIBBdY',	'active',	'price_1Nfer4ER0DocJpyMSyEDM7Ps',	1,	NULL,	'2024-04-19 09:50:14',	NULL,	'2024-03-19 09:50:15',	'2024-03-19 18:46:05'),
(18,	21,	'Medium',	'sub_1Ow7fmER0DocJpyMJAeNIY2u',	'active',	'price_1NfephER0DocJpyMd8E0Yab1',	1,	NULL,	'2024-04-19 18:46:06',	NULL,	'2024-03-19 18:46:07',	'2024-03-19 18:46:17');

DROP TABLE IF EXISTS `subscription_item`;
CREATE TABLE `subscription_item` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `subscription_id` bigint unsigned NOT NULL,
  `stripe_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `stripe_product` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `stripe_price` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `quantity` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `subscription_item_stripe_id_unique` (`stripe_id`),
  UNIQUE KEY `subscription_item_subscription_id_stripe_price_unique` (`subscription_id`,`stripe_price`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `subscription_item` (`id`, `subscription_id`, `stripe_id`, `stripe_product`, `stripe_price`, `quantity`, `created_at`, `updated_at`) VALUES
(1,	1,	'si_PIiEkcqCVSpvDh',	'prod_OSZz6yeHZACA1T',	NULL,	NULL,	'2024-01-02 12:10:51',	'2024-01-02 12:10:51'),
(2,	2,	'si_PIiIbDzuPYG266',	'prod_OSZueKIOIQSh5b',	NULL,	NULL,	'2024-01-02 12:14:34',	'2024-01-02 12:14:34'),
(3,	3,	'si_PIijiexkqLf0cn',	'prod_OSZz6yeHZACA1T',	NULL,	NULL,	'2024-01-02 12:41:32',	'2024-01-02 12:41:32'),
(4,	4,	'si_PNamweDRWdw8bm',	'prod_OSZrtzUn9FfxRN',	NULL,	NULL,	'2024-01-15 12:48:28',	'2024-01-15 12:48:28'),
(5,	5,	'si_PNaoZt9AJ3FT6Y',	'prod_OSZw3Xq06KEwZY',	NULL,	NULL,	'2024-01-15 12:50:43',	'2024-01-15 12:50:43'),
(6,	6,	'si_PNawHdAdgHEDne',	'prod_OSZz6yeHZACA1T',	NULL,	NULL,	'2024-01-15 12:59:02',	'2024-01-15 12:59:02'),
(7,	7,	'si_PP3sryfO73orUU',	'prod_OSZw3Xq06KEwZY',	NULL,	NULL,	'2024-01-19 10:57:18',	'2024-01-19 10:57:18'),
(8,	8,	'si_PQD8rPeLyPAc0l',	'prod_OSZrtzUn9FfxRN',	NULL,	NULL,	'2024-01-22 12:34:31',	'2024-01-22 12:34:31'),
(9,	9,	'si_PQw4NrCOzb3yez',	'prod_OSZrtzUn9FfxRN',	NULL,	NULL,	'2024-01-24 11:00:52',	'2024-01-24 11:00:52'),
(10,	10,	'si_PQw5IHvU41VDse',	'prod_OSZw3Xq06KEwZY',	NULL,	NULL,	'2024-01-24 11:02:06',	'2024-01-24 11:02:06'),
(11,	11,	'si_PQwGJTKb0orPbm',	'prod_OSa14Wbe812843',	NULL,	NULL,	'2024-01-24 11:13:04',	'2024-01-24 11:13:04'),
(12,	12,	'si_PQxvN3x8OB369O',	'prod_OSZrtzUn9FfxRN',	NULL,	NULL,	'2024-01-24 12:55:43',	'2024-01-24 12:55:43'),
(13,	13,	'si_PRMRpgvfOg5sCT',	'prod_OSa14Wbe812843',	NULL,	NULL,	'2024-01-25 14:16:02',	'2024-01-25 14:16:02'),
(14,	14,	'si_PRewGWFckxFcCM',	'prod_OSZz6yeHZACA1T',	NULL,	NULL,	'2024-01-26 09:22:22',	'2024-01-26 09:22:22'),
(15,	15,	'si_PTXZg4q1WxTtrh',	'prod_OSa14Wbe812843',	NULL,	NULL,	'2024-01-31 09:53:45',	'2024-01-31 09:53:45'),
(16,	16,	'si_PZav4e5ETA6HGn',	'prod_OSa14Wbe812843',	NULL,	NULL,	'2024-02-16 13:46:06',	'2024-02-16 13:46:06'),
(17,	17,	'si_PlWLM2LlQFaNZS',	'prod_OSa14Wbe812843',	NULL,	NULL,	'2024-03-19 09:50:15',	'2024-03-19 09:50:15'),
(18,	18,	'si_PlezivXGMOtxqt',	'prod_OSZz6yeHZACA1T',	NULL,	NULL,	'2024-03-19 18:46:07',	'2024-03-19 18:46:07');

DROP TABLE IF EXISTS `subscription_plan`;
CREATE TABLE `subscription_plan` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `plan_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `subscription_type_id` bigint unsigned NOT NULL,
  `plan_price` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0',
  `plan_price_effect_date` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `plan_price_end_date` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `table_plan_limit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0',
  `price_per_table` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `key_benefits` json DEFAULT NULL,
  `stripe_plan` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `subscription_plan_subscription_type_id_foreign` (`subscription_type_id`),
  CONSTRAINT `subscription_plan_subscription_type_id_foreign` FOREIGN KEY (`subscription_type_id`) REFERENCES `subscription_type` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `subscription_plan` (`id`, `plan_name`, `subscription_type_id`, `plan_price`, `plan_price_effect_date`, `plan_price_end_date`, `table_plan_limit`, `price_per_table`, `key_benefits`, `stripe_plan`, `is_active`, `deleted_at`, `created_at`, `updated_at`) VALUES
(1,	'Trial',	1,	'0',	'February 26, 2024',	'March 11, 2024',	'Up to 3 tables',	'0',	NULL,	NULL,	1,	NULL,	'2023-12-21 13:56:44',	'2024-02-26 12:34:57'),
(2,	'Lite',	2,	'$75',	'February 26, 2024',	'March 26, 2024',	'Up to 10 tables',	'$7.50 per table',	'{\"Storage Cost\": 0, \"Lite Dashboard\": 1, \"Query Archive Data\": 0, \"Project Table Growth\": 0, \"Table Size + Indexing\": 0, \"DB Storage Cost Savings\": 0}',	'price_1Nfer4ER0DocJpyMSyEDM7Ps',	1,	NULL,	'2023-12-21 13:56:44',	'2024-02-26 12:34:57'),
(3,	'Medium',	2,	'$295',	'February 26, 2024',	'March 26, 2024',	'Up to 50 tables',	'$5.90 per table',	'{\"Storage Cost\": 1, \"Lite Dashboard\": 1, \"Query Archive Data\": 1, \"Project Table Growth\": 1, \"Table Size + Indexing\": 1, \"DB Storage Cost Savings\": 1}',	'price_1NfephER0DocJpyMd8E0Yab1',	1,	NULL,	'2023-12-21 13:56:45',	'2024-02-26 12:34:57'),
(4,	'High',	2,	'$1999',	'February 26, 2024',	'March 26, 2024',	'Up to 500 tables',	'$4.00 per table',	'{\"Storage Cost\": 1, \"Lite Dashboard\": 1, \"Query Archive Data\": 1, \"Project Table Growth\": 1, \"Table Size + Indexing\": 1, \"DB Storage Cost Savings\": 1}',	'price_1NfemzER0DocJpyMJC8Irp8T',	1,	NULL,	'2023-12-21 13:56:46',	'2024-02-26 12:34:57'),
(5,	'Lite',	3,	'$900',	'February 26, 2024',	'February 26, 2025',	'Up to 10 tables',	'$7.50 per table',	'{\"Storage Cost\": 0, \"Lite Dashboard\": 1, \"Query Archive Data\": 0, \"Project Table Growth\": 0, \"Table Size + Indexing\": 0, \"DB Storage Cost Savings\": 0}',	'price_1NfelXER0DocJpyM7fHfVeZj',	1,	NULL,	'2023-12-21 13:56:47',	'2024-02-26 12:34:57'),
(6,	'Medium',	3,	'$3240',	'February 26, 2024',	'February 26, 2025',	'Up to 50 tables',	'$5.90 per table',	'{\"Storage Cost\": 1, \"Lite Dashboard\": 1, \"Query Archive Data\": 1, \"Project Table Growth\": 1, \"Table Size + Indexing\": 1, \"DB Storage Cost Savings\": 1}',	'price_1NfekOER0DocJpyMfFKWMm3p',	1,	NULL,	'2023-12-21 13:56:48',	'2024-02-26 12:34:57'),
(8,	'High',	3,	'$23988',	'February 26, 2024',	'February 26, 2025',	'Up to 500 tables',	'$4.00 per table',	'{\"Storage Cost\": 1, \"Lite Dashboard\": 1, \"Query Archive Data\": 1, \"Project Table Growth\": 1, \"Table Size + Indexing\": 1, \"DB Storage Cost Savings\": 1}',	'price_1Nfei0ER0DocJpyMcHcBLUvb',	1,	NULL,	'2023-12-21 13:56:49',	'2024-02-26 12:34:57'),
(9,	'Enterprise',	3,	'Custom',	'February 26, 2024',	'February 26, 2025',	'Unlimited number of tables',	NULL,	'{\"Storage Cost\": 1, \"Lite Dashboard\": 1, \"Query Archive Data\": 1, \"Project Table Growth\": 1, \"Table Size + Indexing\": 1, \"DB Storage Cost Savings\": 1}',	NULL,	1,	NULL,	'2023-12-21 13:56:49',	'2024-02-26 12:34:57'),
(10,	'Enterprise',	2,	'Custom',	'February 26, 2024',	'February 26, 2025',	'Unlimited number of tables',	NULL,	'{\"Storage Cost\": 1, \"Lite Dashboard\": 1, \"Query Archive Data\": 1, \"Project Table Growth\": 1, \"Table Size + Indexing\": 1, \"DB Storage Cost Savings\": 1}',	NULL,	1,	NULL,	'2023-12-21 13:56:50',	'2024-02-26 12:34:57');
