  GNU nano 6.2                                                                                                  schedule_test.sh                                                                                                            
#!/usr/bin/env python3

import json
import sys
import time

#json_string = sys.argv[1]
#print("Received JSON string:", json_string)
#try:
#    data = json.loads(json_string)
#    print("Parameters:")
#    for key, value in data.items():
#        print(f"{key}: {value}")
#except json.JSONDecodeError as e:
#    print("Error parsing JSON:", e)

def process_json(json_string):
    try:
        received_json = json.loads(json_string)
        response = {
            "status": "success",
            "statusCode": 200,
            "received_json_params": received_json
        }
        return json.dumps(response)  # Convert Python dictionary to JSON string
    except json.JSONDecodeError as e:
        response = {
            "status": "error",
            "statusCode": 400,
            "message": "Error parsing JSON: " + str(e)
        }
        return json.dumps(response)  # Convert Python dictionary to JSON string
if __name__ == "__main__":
    json_string = sys.argv[1]
    time.sleep(5)
    print(process_json(json_string))






