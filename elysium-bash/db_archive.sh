#!/bin/bash
# --------------------------------------------------------------------------------
# Copyright (c) 2023 Elysium-IO, Inc
# 
# This script is proprietary software and is protected under copyright law.
# Redistribution or modification of this software without explicit permission
# from Elysium-IO, Inc. is strictly prohibited.
#
# Title: Database Archiving Script
# Description: This script automates the process of archiving databases.
# Version: 1.0
# Author: <PERSON><PERSON>
# Created on: 11/23/2023
# --------------------------------------------------------------------------------
# Overview
# Archive a perticular table (the table must be marked : is_single_table_archive =1 in archive_table_list table)
# ./db_archive.sh -h [host] -d [database] -t [table]

# db_archive.sh
# Archive all tables in the specified database (and is_single_table_archive=0)
# ./db_archive.sh -h [host] -d [database]
 
# Script run conditions
## -e is fast fail on first error
## -u is error on unset variables 
## -E is to pass trab handling to functions
set -Eeu

# Constants
ARCHIVE_DIR="/home/<USER>/data_archive"
ARCHIVE_THREAD_LIMIT=2
S3_BUCKET="mysql-data-archive-incremental"
EMAIL_TO="<EMAIL>,<EMAIL>"
WEBHOOK_URL="*********************************************************************************"
ARCHIVAL_INFO_PATH_LOGIN="bash_localhost"
ARCHIVAL_INFO_TABLE_NAME="archive_table_list_new"
S3_TRY_MAX=10

database_host=""
database_schema=""
table_name=""
attr_arg="created_at"
nodelete=false


# Functions
log() {
	# Print string passed with a timestamp prefix
	local level="${1}"
	local message="${2}"

	# Argument to date
	## %F is full date: 2022-08-04
	## %T is time:     12:03:43
	## %Z is timezone: UTC
	local timestamp=$(date +"%F %T %Z")

	if [[ $# != 2 ]]; then
		log ERROR "log function requires 2 arguments, got: $#"
		exit 1
	fi

	# printf is a bash built in
	if [[ -n "${table_id:-}" ]]; then
		printf '%s: %s: %s: %s\n' "${timestamp}" "${level}" "${table_id}" "${message}"
	else
		printf '%s: %s: %s\n' "${timestamp}" "${level}" "${message}"
	fi
}

usage() {
	printf '%s' "Usage: $0 [-h hostname -d database -t table] "  1>&2
	exit 1
}

mysql_cmd() {
	local login_path="${1}"
	local query="${2}"

	if [[ $# != 2 ]]; then
		log ERROR "mysql_cmd function requires 2 arguments, got: $#"
		exit 1
	fi

	mysql \
		--login-path="${login_path}" \
		--skip-column-names \
		--batch <<<"${query}"
}

fail_mail() {
	local body="${1}"
	local subject="Purge Job: FAILURE: Host='${database_host:-}' Schema='${database_schema:-}' Table='${table_name:-}'"

	if [[ $# != 1 ]]; then
		log ERROR "fail_mail function requires 1 arguments, got: $#"
		exit 1
	fi

	printf '%s\n\nLog File: %s\nTable ID: %s' "${body}" "${log_file:-}" "${table_id:-}" \
		| mail \
			-s "${subject}" \
			$([[ -n "${log_file:-}" ]] && printf '%s' "-A ${log_file}") \
			"${EMAIL_TO}"
	curl \
		--silent \
		-X POST \
		-H 'Content-type: application/json' \
		--data '{"text": "*'"${subject}*"'\n```\n'"${body}"'\n```\nLog File: `'"${log_file:-}"'`\nTable ID: `'"${table_id:-}"'`"}' "${WEBHOOK_URL}" > /dev/null
}

unhandled_err() {
	if [[ $? != 0 ]]; then
		printf "%s\n" "$*" >&2;
		fail_mail "Unhandled error in: ${0}
	Line:  ${BASH_COMMAND}"
	fi
}

cap_threads() {
	local dir=${1}
	local limit=${2}
	
	local threads=$(ls "${dir}" | wc -l | tr -d ' ')

	[[ ${threads} -ge ${limit} ]]
}

# Traps
trap unhandled_err EXIT

# Script arguments
while getopts "h:d:t:c:n" opt; do
   case $opt in
     h)  database_host=$OPTARG ;;
     d)  database_schema=$OPTARG ;;
     t)  table_name=$OPTARG ;;
     c)  attr_arg=$OPTARG ;;
     n)  nodelete=true ;;
     ?)  usage ;;
   esac
done

log INFO "Starting Archive Run"

log INFO "Threads: ${ARCHIVE_THREAD_LIMIT}"
# Detect if output redirection/logging is used
if [[ ! -t 1 ]]; then
	log_file=$(lsof -p$$ -a -d 1 -w -Fn | tail -n1 | sed 's/^n//')
	log INFO "Log File:        ${log_file}"
fi

# SQL Queries
base_sql_query="
SELECT database_server,database_name,table_name,data_retention,is_active,batchsize,s3_location,is_partitioned
FROM   archival_info.${ARCHIVAL_INFO_TABLE_NAME}
WHERE  database_server         = '${database_host}'
AND    is_active               = 1"

host_sql_query="${base_sql_query}
AND    is_single_table_archive = 0;
"

schema_sql_query="${base_sql_query}
AND    database_name           = '${database_schema}'
AND    is_single_table_archive = 0;
"

table_sql_query="${base_sql_query}
AND    database_name           = '${database_schema}'
AND    table_name              = '${table_name}'
AND    is_single_table_archive = 1;
"

# Verification
if [[ -z "${database_host}" ]]; then
	log ERROR "Database Host is not set"
	usage
fi

if [[ -n "${table_name}" && -n "${database_schema}" ]]; then
	log INFO "Database Host:   ${database_host}"
	log INFO "Database Schema: ${database_schema}"
	log INFO "Database Table:  ${table_name}"
	log INFO "ARCHIVE MODE: Table"
	archive_mode="table"
	sql_query="${table_sql_query}"
elif [[ -z "${table_name}" && -n "${database_schema}" ]]; then
	log INFO "Database Host:   ${database_host}"
	log INFO "Database Schema: ${database_schema}"
	log INFO "ARCHIVE MODE: Schema"
	archive_mode="schema"
	sql_query="${schema_sql_query}"
elif [[ -z "${table_name}" && -z "${database_schema}" ]]; then
	log INFO "Database Host:   ${database_host}"
	log INFO "ARCHIVE MODE: Host"
	archive_mode="host"
	sql_query="${host_sql_query}"
else
	message="Could not detect mode with arguments: $@"
	log ERROR "${message}"
	fail_mail "${message}"
	exit 1
fi

# Method
log INFO "Getting archive information from database"
log INFO "SQL Query for host targets
${sql_query}"
mysql_results_archival_info=$(mysql_cmd "${ARCHIVAL_INFO_PATH_LOGIN}" "${sql_query}")

if [[ -z "${mysql_results_archival_info}" ]]; then
	message="Failed to get rows from archival_info table with arguments
	Database Host:   ${database_host}
	Database Schema: ${database_schema}
	Database Table:  ${table_name}"
	log ERROR "${message}"
	fail_mail "${message}"
	exit 1
fi

archival_info_row_count=$(wc -l <<<"${mysql_results_archival_info}")
archival_threads_dir=$(mktemp -d)

log INFO "Number of tables returned for processing: ${archival_info_row_count}"
while IFS=$'\t' read database_host database_schema table_name retention_in_days is_active batch_size s3_location is_partitioned; do
	while cap_threads "${archival_threads_dir}" ${ARCHIVE_THREAD_LIMIT}; do
	       sleep 2
        done	       
	row_count=$(( ${row_count:=0} + 1 ))
	(
		table_id=$(md5sum <<<"${database_host}.${database_schema}.${table_name}" | fold -w8 | head -n1)
		archive_thread_file="${archival_threads_dir}/${table_id}.thread"
		touch "${archive_thread_file}"
		log INFO "Processing row information:
		Row:         ${row_count}/${archival_info_row_count}
		Table ID:    ${table_id} 
		Host:        ${database_host}
		Schema:      ${database_schema}
		Table Name:  ${table_name}
		Retention:   ${retention_in_days} days
		Active:      ${is_active}
		Partitioned: ${is_partitioned}
		Batch size:  ${batch_size} rows
		S3 Location: ${s3_location}"

		max_date=$(date -u +"%Y-%m-%d" -d "-${retention_in_days} days") 
		
		archive_sql_query="
SELECT COUNT(1), min(id), max(id)
FROM   ${database_schema}.${table_name}
WHERE  ${attr_arg} < '${max_date}';
"
		log INFO "SQL Query for row count and ID range
	${archive_sql_query}"

		mysql_results=$(mysql_cmd "${database_host}" "${archive_sql_query}")
		# A single line is returned and the mysql delimiter is `\t` so we can just cast these in via position
		total_db_rows=$(cut -d$'\t' -f1 <<<"${mysql_results}")
		start_id=$(cut -d$'\t' -f2 <<<"${mysql_results}")
		end_id=$(cut -d$'\t' -f3 <<<"${mysql_results}")

		log INFO "Archive information:
	Total Rows: ${total_db_rows}
	Start ID:   ${start_id}
	End ID:     ${end_id}"

		# If the returned row count is 0 then we want to skip this database server as there is nothing to do
		if [[ "${total_db_rows}" = 0 ]]; then
			log INFO "No rows to archive. SKIPPING!"
			if ! $nodelete; then
				rm -f "${archive_thread_file}"
			fi
			exit
		fi

		# Checking if export is needed. Skipping if it already exists in s3
		s3_file_name="${table_name}_${max_date}_${start_id}_${end_id}.tsv"
		s3_file="s3://${S3_BUCKET}/${s3_location}/${database_schema}/${s3_file_name}"
		s3_part_file_name="${s3_file_name}.part"
		s3_part_file="s3://${S3_BUCKET}/${s3_location}/${database_schema}/${s3_part_file_name}"
		log INFO "Checking for TSV in s3"
		if aws s3 ls "${s3_file}" | grep -qE "${s3_file_name}$" > /dev/null; then
			message="TSV has been already uploaded. SKIPPING!
		TSV: ${s3_file}"
			log WARN "${message}"
			fail_mail "${message}"
			if ! $nodelete; then
				rm -f "${archive_thread_file}"
			fi
			exit
		fi

		tsv_file="${ARCHIVE_DIR}/${database_host}.${database_schema}.${table_name}_${max_date}_${start_id}_${end_id}.tsv"
		# Check if TSV exists and if it does then skip
		if [[ -f "${tsv_file}" ]]; then
			message="TSV already exists on disk, please clear it. SKIPPING!
		File: ${tsv_file}"
			log WARN "${message}"
			fail_mail "${message}"
			if ! $nodelete; then
				rm -f "${archive_thread_file}"
			fi
			exit
		fi

		log INFO "Archiving data into: ${tsv_file}"
		current_batch_id_start=${start_id}
		current_batch_id_end=0
		archive_export_sql_displayed=false
		while true; do
			# Iterate collection range by `batch_size`
			current_batch_id_end=$(( ${current_batch_id_start} + ${batch_size} ))
			# if `current_batch_id_end` is greater then `end_id` then set it to `end_id` as we do not want to go past this range for _this_ run
			if [[ ${current_batch_id_end} -gt ${end_id} ]]; then
				current_batch_id_end=${end_id}
			fi

			archive_export_sql="
SELECT *
FROM   ${database_schema}.${table_name}
WHERE  id BETWEEN ${current_batch_id_start} AND ${current_batch_id_end}
AND    ${attr_arg} < '${max_date}';
"
			# Display SQL for row export only once to not be noisy
			if [[ "${archive_export_sql_displayed}" = false ]]; then
				log INFO "SQL Query for row export
THIS IS ONLY DISPLAYED ONCE
${archive_export_sql}"
				archive_export_sql_displayed=true
			fi

			log INFO "Exporting rows between '${current_batch_id_start}' and '${current_batch_id_end}'"
			mysql_cmd "${database_host}" "${archive_export_sql}" >> "${tsv_file}" 
			if [[ ${current_batch_id_end} -ge ${end_id} ]]; then
				break
			fi

			# As SQL BETWEEN is INCLUSIVE we need to add `1` or we will get duplicate rows
			current_batch_id_start=$(( ${current_batch_id_end} + 1 ))
		done

		# Validate that local and database row count matches
		log INFO "Comparing local tsv row count to returned total of database rows"
		total_local_rows=$(wc -l "${tsv_file}" | cut -d" " -f1)

		# if the local rows and database rows do not match then we do not want to upload to s3 or delete rows
		if [[ "${total_local_rows}" != "${total_db_rows}" ]]; then
			message="Local and database row count doesn't match:
	Local:    ${total_local_rows} rows
	Database: ${total_db_rows} rows"
			log WARN "${message}"
			fail_mail "${message}"
			if ! $nodelete; then
				rm -f "${archive_thread_file}"
			fi
			exit
		fi

		# Upload tsv to s3 and report if it fails
		log INFO "Uploading TSV to: ${s3_part_file}"
		if ! aws s3 cp "${tsv_file}" "${s3_part_file}" > /dev/null; then
			message="Failed to upload TSV to S3 bucket
	TSV: ${tsv_file_part}
	S3 Bucket: ${S3_BUCKET}"
			log WARN "${message}"
			fail_mail "${message}"
			aws s3 rm "${s3_part_file}" || true
			if ! $nodelete; then
				rm -f "${archive_thread_file}"
			fi
			exit
		fi
		# This whole section is annoying because the files are larger than 1GB. 
		# `aws s3 cp` uploads the files as a multi-part upload when files are over 1GB, which has some weird consequences. 
		# Once it has finished uploading, it combines it into a single file. 
		# If you request the file size too soon, it will either give you a blank response or the wrong size. 
		# So we must wait for a set time and then loop over the response to ensure it is correct.
		log INFO "Waiting 60 seconds for s3 file to settle"
		sleep 60
		
		# Get the size in bytes of the report tsv
		log INFO "Getting tsv size from s3"
		try=0
		while [[ -z "${s3_tsv_size_bytes:-}" ]]; do
			try=$(( ${try} + 1 ))
			# Once we reach the MAX break the inner loop
			if [[ ${try} -gt ${S3_TRY_MAX} ]]; then
				break
			fi
			log INFO "Try: ${try}/${S3_TRY_MAX}"
			s3_tsv_size_bytes=$(aws s3 ls "${s3_part_file}" | grep -E "${s3_part_file_name}$" | awk '{ print $3 }' )
			sleep 30
		done
		# If try is greater then MAX then remove remote s3 file and skip
		if [[ ${try} -gt ${S3_TRY_MAX} ]]; then
			message="Could not get s3 tsv size"
			log WARN "${message}"
			fail_mail "${message}"
			aws s3 rm "${s3_part_file}" || true
			if ! $nodelete; then
				rm -f "${archive_thread_file}"
			fi
			exit
		fi
		# Compare the size of the local file and the s3 file as a basic _checksum_
		log INFO "Comparing local and s3 file size for '${S3_TRY_MAX}' tries"
		log INFO "Current s3 tsv size: ${s3_tsv_size_bytes} bytes"
		local_tsv_size_bytes=$(ls -l "${tsv_file}" | cut -d' ' -f5)
		log INFO "Local tsv size: ${local_tsv_size_bytes} bytes"
		try=0
		while [[ "${local_tsv_size_bytes}" != "${s3_tsv_size_bytes}" ]]; do
			try=$(( ${try} + 1 ))
			# Once we reach the MAX break the inner loop
			if [[ ${try} -gt ${S3_TRY_MAX} ]]; then
				break
			fi
			s3_tsv_size_bytes=$(aws s3 ls "${s3_part_file}" | grep -E "${s3_part_file_name}$" | awk '{ print $3 }')
			log INFO "Try: ${try}/${S3_TRY_MAX}
	local: ${local_tsv_size_bytes} bytes
	s3:    ${s3_tsv_size_bytes} bytes"
			sleep 20
		done
		if [[ ${try} -gt ${S3_TRY_MAX} ]]; then
			message="Mismatch between local and s3 files
	local: ${local_tsv_size_bytes} bytes
	s3:    ${s3_tsv_size_bytes} bytes"
			log WARN "${message}"
			fail_mail "${message}"
			aws s3 rm "${s3_part_file}" || true
			if ! $nodelete; then
				rm -f "${archive_thread_file}"
			fi
			exit
		fi

		# Once the file has passed all the checks then remove the `.part` from the file name with a mv
		log INFO "Moving s3 tsv
	FROM: ${s3_part_file}
	TO:   ${s3_file}"
		if ! aws s3 mv "${s3_part_file}" "${s3_file}" > /dev/null; then
			message="Failed to move s3 tsv"
			log WARN "${message}"
			fail_mail "${message}"
			if ! $nodelete; then
				rm -f "${archive_thread_file}"
			fi
			exit
		fi

		# Remove local file once all the checks have passed
		log INFO "Deleting local TSV file"
		rm -f "${tsv_file}"

		#########################################################
		###################### DANGER ZONE ######################
		#########################################################
		log INFO "Deleting data : ${database_host}.${database_schema}.${table_name}"
		if [[ "${is_partitioned}" = "1" ]]; then
			standby_table_name="${table_name}_standby"
			staged_table_name="${table_name}_staged"
			# Make sure there is something to do before swapping anything around
			log INFO "Partitions are enabled. Will use PARTITION DROPing"
			log INFO "Get partitions older then: ${retention_in_days} days"
			get_partitions_sql_query="
SELECT PARTITION_NAME
FROM information_schema.partitions
WHERE TABLE_SCHEMA             = '${database_schema}'
AND   TABLE_NAME               = '${table_name}'
AND   PARTITION_DESCRIPTION   <= UNIX_TIMESTAMP('${max_date}') 
AND   PARTITION_NAME        LIKE 'p________';
"
			log INFO "SQL query for partition names
${get_partitions_sql_query}"
			partitions_to_delete=$(mysql_cmd "${database_host}" "${get_partitions_sql_query}" | tr '\n' ',' | sed 's/,$//')
			if [[ -z "${partitions_to_delete}" ]]; then
				log INFO "No paritions to delete. SKIPPING!"
				if ! $nodelete; then
					rm -f "${archive_thread_file}"
				fi
				exit
			fi
			# Make sure that the standby table exists
			log INFO "Ensuring standby table exists"
			standby_table_sql="
CREATE TABLE IF NOT EXISTS ${database_schema}.${standby_table_name}
LIKE ${database_schema}.${table_name} 
"
			log INFO "SQL command for standby table:
${standby_table_sql}"
			if ! mysql_cmd "${database_host}" "${standby_table_sql}"; then
				message="Failed to enforce standby table"
				log WARN "${message}"
				fail_mail "${message}"
				if ! $nodelete; then
					rm -f "${archive_thread_file}"
				fi
				exit
			fi
			log INFO "Get the current MAX ID of the active table"
			max_id_sql_query="
SELECT MAX(id)
FROM ${database_schema}.${table_name};
"
			log INFO "SQL query to get the MAX id:
${max_id_sql_query}"
			current_max_id=$(mysql_cmd "${database_host}" "${max_id_sql_query}")
			new_id_position=$(( ${current_max_id} + 10000 ))

			log INFO "Change the standby tables id position"
			change_id_position_sql="
ALTER TABLE ${database_schema}.${standby_table_name} 
AUTO_INCREMENT=${new_id_position};
"
			log INFO "SQL command for new id position:
${change_id_position_sql}"
			if ! mysql_cmd "${database_host}" "${change_id_position_sql}"; then
				message="Failed to change the id position"
				log WARN "${message}"
				fail_mail "${message}"
				if ! $nodelete; then
					rm -f "${archive_thread_file}"
				fi
				exit
			fi

			log INFO "Swapping tables"
			rename_tables_sql="
RENAME TABLE
${database_schema}.${table_name} TO ${database_schema}.${staged_table_name},
${database_schema}.${standby_table_name} TO ${database_schema}.${table_name},
${database_schema}.${staged_table_name} TO ${database_schema}.${standby_table_name};
"
			log INFO "SQL command for table renaming
${rename_tables_sql}"
			 if ! mysql_cmd "${database_host}" "${rename_tables_sql}"; then
				message="Failed to rename tables. INVESTIGATE NOW!"
				log CRIT "${message}"
				fail_mail "${message}"
				if ! $nodelete; then
					rm -f "${archive_thread_file}"
				fi
				exit
			fi
				
			delete_partitions_sql="
ALTER TABLE     ${database_schema}.${standby_table_name}
DROP  PARTITION ${partitions_to_delete};
"
			log INFO "SQL command for partition delete
	${delete_partitions_sql}"
			if mysql_cmd "${database_host}" "${delete_partitions_sql}"; then
				log INFO "Partitions have been deleted"
			else
				message="Failed to delete partitions"
				log FAIL "${message}"
				fail_mail "${message}"
				if ! $nodelete; then
					rm -f "${archive_thread_file}"
				fi
				exit
			fi
			log INFO "Get the current MAX ID of the active table"
			log INFO "SQL query to get the MAX id:
${max_id_sql_query}"

			current_max_id=$(mysql_cmd "${database_host}" "${max_id_sql_query}")
			if [[ "${current_max_id}" == 'NULL' ]]; then
				current_max_id=${new_id_position}
			fi
			new_id_position=$(( ${current_max_id} + 10000 ))

			log INFO "Change the standby tables id position"
			log INFO "SQL command for new id position:
${change_id_position_sql}"

			if ! mysql_cmd "${database_host}" "${change_id_position_sql}"; then
				message="Failed to change the id position"
				log WARN "${message}"
				fail_mail "${message}"
				if ! $nodelete; then
					rm -f "${archive_thread_file}"
				fi
				exit
			fi

			log INFO "Swapping tables"
			log INFO "SQL command for table renaming:
${rename_tables_sql}"
			 if ! mysql_cmd "${database_host}" "${rename_tables_sql}"; then
				message="Failed to rename tables. INVESTIGATE NOW!"
				log CRIT "${message}"
				fail_mail "${message}"
				if ! $nodelete; then
					rm -f "${archive_thread_file}"
				fi
				exit
			fi
			log INFO "Fill data from standby table to active table"
			fill_active_sql="
INSERT INTO ${database_schema}.${table_name}
SELECT *
FROM ${database_schema}.${standby_table_name};
"
			log INFO "SQL command to fill data from standby:
${fill_active_sql}"

			if ! mysql_cmd "${database_host}" "${fill_active_sql}"; then
				message "Failed to INSERT data from standby table to active"
				log WARN "${message}"
				fail_mail "${message}"
				if ! $nodelete; then
					rm -f "${archive_thread_file}"
				fi
				exit
			fi

			log INFO "Truncating standby table"
			truncate_standby_sql="
TRUNCATE TABLE ${database_schema}.${standby_table_name};
"
			log INFO "SQL command for table truncating:
${truncate_standby_sql}"

			if ! mysql_cmd "${database_host}" "${truncate_standby_sql}"; then
				message "Failed to TRUNCATE data from standby. INVESTIGATE NOW!"
				log CRIT "${message}"
				fail_mail "${message}"
				if ! $nodelete; then
					rm -f "${archive_thread_file}"
				fi
				exit
			fi

		# END OF PARTITION
		else
			current_batch_id_start=${start_id}
			current_batch_id_end=0
			delete_sql_displayed=false
			while true; do
				# Iterate collection range by `batch_size`
				current_batch_id_end=$(( ${current_batch_id_start} + ${batch_size} ))
				# if `current_batch_id_end` is greater then `end_id` then set it to `end_id` as we do not want to go past this range for _this_ run
				if [[ ${current_batch_id_end} -gt ${end_id} ]]; then
					current_batch_id_end=${end_id}
				fi

				# Left as a SELECT on purpose until we are ready
				delete_sql="
DELETE
FROM   ${database_schema}.${table_name}
WHERE  id BETWEEN ${current_batch_id_start} AND ${current_batch_id_end}
AND    ${attr_arg} < '${max_date}';
"
				# Display SQL for row delete only once to not be noisy
				if [[ "${delete_sql_displayed}" = false ]]; then
					log INFO "SQL command for row deletion
THIS IS ONLY DISPLAYED ONCE
${delete_sql}"
					delete_sql_displayed=true
				fi

				log INFO "Deleting rows between '${current_batch_id_start}' and '${current_batch_id_end}'"
				mysql_cmd "${database_host}" "${delete_sql}"
				if [[ ${current_batch_id_end} -ge ${end_id} ]]; then
					break
				fi

				# As SQL BETWEEN is INCLUSIVE we need to add `1` or we will get duplicate rows
				current_batch_id_start=$(( ${current_batch_id_end} + 1 ))
			done

			log INFO "Confirming deletion of rows"
			mysql_results=$(mysql_cmd "${database_host}" "${archive_sql_query}")
			total_db_rows=$(cut -d$'\t' -f1 <<<"${mysql_results}")

			if [[ "${total_db_rows}" = 0 ]]; then
				log INFO "Successful deletion of rows in: ${database_host}.${database_schema}.${table_name}"
			else
				message="Failed to delete rows in: ${database_host}.${database_schema}.${table_name}
	Rows reported: ${total_db_rows}"
				# Not sure if this should be a WARN or ERROR need to confirm
				log WARN "${message}"
				fail_mail "${message}"
				if ! $nodelete; then
					rm -f "${archive_thread_file}"
				fi
				exit
			fi
		fi
		if ! $nodelete; then
			rm -f "${archive_thread_file}"
		fi
	) &
	# Script needs time to create the thread file so we do not rush though
	sleep 2 
done <<<"${mysql_results_archival_info}"
wait

archival_time=$(date -d@"${SECONDS}" -u +"%H Hours %M Minutes %S Seconds")
log INFO "Processing took: ${archival_time}"
log INFO "Finished Archive Run"
