# Database Archiver 
 
This repository contains the means to archive database tables to an AWS S3 bucket. 
 
## Overview 
 
1. Read the tables to be archived from the `archive_info`
   - Tables to be archived need to have `is_active = 1` 
1. Loop over the returned tables with the defined number of threads 
1. Export rows that are older than the retention period 
1. Upload exported data to S3 
1. Delete the exported rows from the table 
   - If the table is not partitioned, then the delete will loop, deleting a batch size at a time 
   - If the table is partitioned, then the table is _swapped_ with the standby table, and the partitions that contained the rows are dropped 
 
## Assumptions 
 
- AWS CLI is configured and working on the host 
- Database hosts have a `MySQL` `login-path` configured 
- Everything runs under the `ubuntu` user 
 
## Configuration 
 
- The script is cloned to the `/opt/elysium-archiver/` folder 
- Logs are saved to `/home/<USER>/logs` 
   - Logs are handled by `stdout` and `stderr` redirection in the `crontab` 
- In-transit TSVs are saved to `/home/<USER>/data_archive` 
   - This is configurable by the constant `ARCHIVE_DIR` 
   - These files need to be cleaned, or they will fill up the disk 
- The script is scheduled via the `ubuntu`s users `crontab` 
   - When updating the `crontab`, please update the copy in this repo 
- Threads are controlled by the constant `ARCHIVE_THREAD_LIMIT` 
- Email alerts are controlled by the constant `EMAIL_TO` 
   - It takes a `,` separated list of emails 
- Slack alerts are controlled by the constant `WEBHOOK_URL`
