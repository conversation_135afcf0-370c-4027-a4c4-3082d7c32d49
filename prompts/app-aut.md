# Authentication Mechanism in Elysium

The Elysium system uses Laravel Sanctum for authentication between the frontend and backend. Here's how it works:

1. **Authentication Flow**:
   - User logs in via the `/auth/login` endpoint (implemented in `AuthController.php`)
   - Upon successful login, the backend generates a Sanctum token and returns it to the frontend
   - Frontend stores this token in cookies using the `handleCookies` utility

2. **Token Usage**:
   - Frontend attaches the token to subsequent API requests as a Bearer token in the Authorization header:
     ```
     Authorization: Bearer {token}
     ```
   - This is implemented in the `baseRequest` function in `dashboard.js` and other service files

3. **Backend Configuration**:
   - Sanctum is configured in `config/sanctum.php`
   - The `EnsureFrontendRequestsAreStateful` middleware is added to the API middleware group in `app/Http/Kernel.php`
   - CORS is configured to support credentials with `'supports_credentials' => true` in `config/cors.php`

4. **Authentication Verification**:
   - The frontend's `Auth.jsx` component verifies authentication by making a request to `/auth/user`
   - Protected routes in the backend use the `auth:sanctum` middleware to ensure requests are authenticated

This setup provides secure, token-based authentication for the SPA while maintaining stateful sessions.