# Database Archiving Process in Elysium-Bash

The database archiving process in the `elysium-bash` component works as follows:

1. **Configuration and Setup**
   - The script reads configuration from constants like `ARCHIVE_DIR`, `S3_BUCKET`, and `ARCHIVE_THREAD_LIMIT`
   - It uses MySQL login paths for database authentication
   - Logs are saved to `/home/<USER>/logs` via stdout/stderr redirection

2. **Table Selection**
   - Reads tables to be archived from the `archival_info` database
   - Only processes tables with `is_active = 1`
   - Supports three modes:
     - Host-level archiving (all databases on a host)
     - Schema-level archiving (all tables in a database)
     - Single table archiving

3. **Multi-threaded Processing**
   - Processes tables in parallel up to `ARCHIVE_THREAD_LIMIT` threads
   - Uses thread files in a temporary directory to track running processes

4. **Data Export**
   - Identifies rows older than the retention period using date comparison
   - Exports data in batches (controlled by `batch_size` parameter)
   - Saves data as TSV files to `/home/<USER>/data_archive`

5. **S3 Upload**
   - Uploads TSV files to AWS S3 with retry logic
   - Uses a `.part` suffix during upload, then renames when complete
   - Verifies file size after upload to ensure integrity

6. **Data Deletion**
   - For non-partitioned tables: Deletes data in batches
   - For partitioned tables: Uses table swapping technique
     - Creates standby table
     - Drops partitions containing old data
     - Swaps tables to minimize downtime

7. **Monitoring and Alerts**
   - Sends email alerts via `EMAIL_TO` configuration
   - Posts Slack notifications via webhook
   - Logs detailed information about each step

This process ensures efficient archiving with minimal impact on production databases while maintaining data integrity throughout the export and deletion process.