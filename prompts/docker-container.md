create a docker containers for running the frontend and backend apps of elysium project plus a db.

needed containers are 
1- backend container, hosting the backend app with volume mounting the laravel project, on container start should run php artisan serve to run the laravel server from mounted folder and listen to any file changes 

2- frontend container, hosting the react app with volume mounting the react project, on container start should run npm start to run the ract server from mounted folder and listen to any file changes 

3- mysql db container, with volume mounting the mysql data directory, on container start should run mysql server.

4- tools container hosting phpadminer 

5- all continers should be in 1 network, db config need to be configured to allow connection from all containers in the network.

6- use docker-compose to configure all containers and network.