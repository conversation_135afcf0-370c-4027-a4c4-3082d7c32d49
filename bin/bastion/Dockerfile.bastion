FROM ubuntu:22.04

# Install necessary packages
RUN apt-get update && \
    apt-get install -y \
    curl \
    sudo \
    openssh-server \
    mysql-server \
    nano && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# RUN apt-get install -y 

# Create ubuntu user with home directory and add to sudoers
RUN useradd -m -s /bin/bash ubuntu && \
    echo 'ubuntu ALL=(ALL) NOPASSWD:ALL' >> /etc/sudoers && \
    mkdir -p /home/<USER>/.ssh && \
    chown -R ubuntu:ubuntu /home/<USER>/.ssh && \
    chmod 700 /home/<USER>/.ssh

# Configure SSH
RUN mkdir /run/sshd && \
    sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin no/' /etc/ssh/sshd_config && \
    sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config

# Copy your public key (optional - can also mount at runtime)
COPY id_rsa.pub /home/<USER>/.ssh/authorized_keys
RUN chmod 600 /home/<USER>/.ssh/authorized_keys


RUN chmod 700 /home/<USER>/.ssh && chmod 600 /home/<USER>/.ssh/authorized_keys && chown -R ubuntu:ubuntu /home/<USER>/.ssh


COPY ssh_config /etc/ssh/sshd_config

# Entrypoint script
COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# COPY franck-ssh-host-elysium.pem /home/<USER>/franck-ssh-host-elysium.pem
# RUN chmod 400 /home/<USER>/franck-ssh-host-elysium.pem

# COPY rds_ssh.sh /home/<USER>/rds_ssh.sh
# RUN chmod +x /home/<USER>/rds_ssh.sh

EXPOSE 22 3306

WORKDIR /home/<USER>

ENTRYPOINT ["/entrypoint.sh"]