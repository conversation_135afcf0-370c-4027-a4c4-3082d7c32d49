[{"database": "elysium", "engine": "InnoDB", "total_rows": "3090", "total_db": "3506176", "data": "2490368", "index": "1015808", "table_data": [{"table_name": "assigned_port", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "19", "total_current_data_length": "16384", "total_current_index_length": "0", "avg_row_length": "862", "max_data_length": "0", "data_free": "0", "auto_increment": "53", "create_time": "2025-04-24 20:21:57", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "created_at", "ordinal_position": "3", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "port", "ordinal_position": "2", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "5", "column_type": "<PERSON><PERSON><PERSON>(5)", "column_key": "", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "4", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}, {"table_name": "client_database", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "6", "total_current_data_length": "16384", "total_current_index_length": "16384", "avg_row_length": "2730", "max_data_length": "0", "data_free": "0", "auto_increment": "7", "create_time": "2025-07-28 14:21:31", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "client_db_server_id", "ordinal_position": "2", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "color_code", "ordinal_position": "12", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "100", "column_type": "<PERSON><PERSON><PERSON>(100)", "column_key": "", "column_comment": ""}, {"column_name": "created_at", "ordinal_position": "8", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "db_name", "ordinal_position": "3", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "100", "column_type": "<PERSON><PERSON><PERSON>(100)", "column_key": "", "column_comment": ""}, {"column_name": "deleted_at", "ordinal_position": "11", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "is_deleted", "ordinal_position": "10", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint", "column_key": "", "column_comment": ""}, {"column_name": "total_current_db_data_size", "ordinal_position": "5", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "total_current_db_index_size", "ordinal_position": "6", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "total_current_db_size", "ordinal_position": "4", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "total_table", "ordinal_position": "7", "is_nullable": "NO", "data_type": "int", "character_max_length": "NULL", "column_type": "int unsigned", "column_key": "", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "9", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}, {"table_name": "client_db_schema", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "6", "total_current_data_length": "16384", "total_current_index_length": "0", "avg_row_length": "2730", "max_data_length": "0", "data_free": "0", "auto_increment": "6", "create_time": "2025-04-24 20:21:58", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "client_database_id", "ordinal_position": "5", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "created_at", "ordinal_position": "3", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "deleted_at", "ordinal_position": "2", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "is_deleted", "ordinal_position": "7", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint", "column_key": "", "column_comment": ""}, {"column_name": "schema_name", "ordinal_position": "6", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "100", "column_type": "<PERSON><PERSON><PERSON>(100)", "column_key": "", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "4", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}, {"table_name": "client_db_server", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "3", "total_current_data_length": "16384", "total_current_index_length": "98304", "avg_row_length": "5461", "max_data_length": "0", "data_free": "0", "auto_increment": "6", "create_time": "2025-06-15 11:34:14", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "client_db_server_type_id", "ordinal_position": "4", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "client_db_server_uuid", "ordinal_position": "8", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "UNI", "column_comment": ""}, {"column_name": "color_code", "ordinal_position": "21", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "100", "column_type": "<PERSON><PERSON><PERSON>(100)", "column_key": "", "column_comment": ""}, {"column_name": "company_id", "ordinal_position": "2", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "created_at", "ordinal_position": "17", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "db_server_alias_name", "ordinal_position": "7", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "UNI", "column_comment": ""}, {"column_name": "db_server_name", "ordinal_position": "6", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "deleted_at", "ordinal_position": "16", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "hostname", "ordinal_position": "9", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "is_active", "ordinal_position": "14", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint(1)", "column_key": "", "column_comment": ""}, {"column_name": "is_deleted", "ordinal_position": "15", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint(1)", "column_key": "", "column_comment": ""}, {"column_name": "login_path_name", "ordinal_position": "13", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "password", "ordinal_position": "11", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "port", "ordinal_position": "12", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "remote_server_id", "ordinal_position": "3", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "remote_server_status_id", "ordinal_position": "5", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "timezone", "ordinal_position": "19", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "10", "column_type": "<PERSON><PERSON><PERSON>(10)", "column_key": "", "column_comment": ""}, {"column_name": "total_current_db_storage_setup_size", "ordinal_position": "20", "is_nullable": "NO", "data_type": "int", "character_max_length": "NULL", "column_type": "int unsigned", "column_key": "Size in GB. Accessible via AWS CLI", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "18", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "username", "ordinal_position": "10", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}]}, {"table_name": "client_db_server_stat", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "24", "total_current_data_length": "16384", "total_current_index_length": "49152", "avg_row_length": "682", "max_data_length": "0", "data_free": "0", "auto_increment": "41", "create_time": "2025-04-24 20:21:58", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "client_db_server_id", "ordinal_position": "3", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "company_id", "ordinal_position": "4", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "created_at", "ordinal_position": "17", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "db_name", "ordinal_position": "5", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "64", "column_type": "<PERSON><PERSON><PERSON>(64)", "column_key": "", "column_comment": ""}, {"column_name": "deleted_at", "ordinal_position": "19", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "dim_date_id", "ordinal_position": "2", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "is_deleted", "ordinal_position": "16", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint(1)", "column_key": "", "column_comment": ""}, {"column_name": "stat_date", "ordinal_position": "6", "is_nullable": "NO", "data_type": "date", "character_max_length": "NULL", "column_type": "date", "column_key": "", "column_comment": ""}, {"column_name": "total_db_data_size_archived", "ordinal_position": "13", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "total_db_index", "ordinal_position": "11", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "total_db_object_storage_used", "ordinal_position": "14", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "total_db_rows", "ordinal_position": "7", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "Total table rows on that day", "column_comment": ""}, {"column_name": "total_db_rows_archived", "ordinal_position": "8", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "total_db_size", "ordinal_position": "10", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "total_db_storage_setup", "ordinal_position": "9", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "total_db_storage_setup_cost", "ordinal_position": "12", "is_nullable": "NO", "data_type": "decimal", "character_max_length": "NULL", "column_type": "decimal(8,2) unsigned", "column_key": "", "column_comment": ""}, {"column_name": "total_table_storage_cost_saving", "ordinal_position": "15", "is_nullable": "NO", "data_type": "decimal", "character_max_length": "NULL", "column_type": "decimal(6,2) unsigned", "column_key": "", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "18", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}, {"table_name": "client_db_server_table", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "70", "total_current_data_length": "49152", "total_current_index_length": "32768", "avg_row_length": "702", "max_data_length": "0", "data_free": "0", "auto_increment": "94", "create_time": "2025-07-28 14:24:51", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "avg_row_length", "ordinal_position": "13", "is_nullable": "YES", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "check_time", "ordinal_position": "16", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "checksum", "ordinal_position": "18", "is_nullable": "YES", "data_type": "int", "character_max_length": "NULL", "column_type": "int unsigned", "column_key": "", "column_comment": ""}, {"column_name": "client_database_id", "ordinal_position": "25", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "client_db_schema_id", "ordinal_position": "2", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "client_db_server_id", "ordinal_position": "26", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "client_db_server_table_uuid", "ordinal_position": "3", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "36", "column_type": "<PERSON><PERSON><PERSON>(36)", "column_key": "", "column_comment": ""}, {"column_name": "color_code", "ordinal_position": "28", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "100", "column_type": "<PERSON><PERSON><PERSON>(100)", "column_key": "", "column_comment": ""}, {"column_name": "create_options", "ordinal_position": "19", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "64", "column_type": "<PERSON><PERSON><PERSON>(64)", "column_key": "", "column_comment": ""}, {"column_name": "created_at", "ordinal_position": "23", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "data_free", "ordinal_position": "15", "is_nullable": "YES", "data_type": "int", "character_max_length": "NULL", "column_type": "int unsigned", "column_key": "", "column_comment": ""}, {"column_name": "deleted_at", "ordinal_position": "22", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "engine", "ordinal_position": "8", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "100", "column_type": "<PERSON><PERSON><PERSON>(100)", "column_key": "", "column_comment": ""}, {"column_name": "has_reference_integrity", "ordinal_position": "27", "is_nullable": "NO", "data_type": "int", "character_max_length": "NULL", "column_type": "int unsigned", "column_key": "The table has referential integrity, which could affect archiving.", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "is_dropped", "ordinal_position": "30", "is_nullable": "YES", "data_type": "int", "character_max_length": "NULL", "column_type": "int unsigned", "column_key": "", "column_comment": ""}, {"column_name": "max_data_length", "ordinal_position": "14", "is_nullable": "YES", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "row_format", "ordinal_position": "10", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "10", "column_type": "<PERSON><PERSON><PERSON>(10)", "column_key": "", "column_comment": ""}, {"column_name": "table_collation", "ordinal_position": "17", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "32", "column_type": "<PERSON><PERSON><PERSON>(32)", "column_key": "", "column_comment": ""}, {"column_name": "table_comment", "ordinal_position": "20", "is_nullable": "YES", "data_type": "text", "character_max_length": "65535", "column_type": "text", "column_key": "", "column_comment": ""}, {"column_name": "table_database_name", "ordinal_position": "5", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "100", "column_type": "<PERSON><PERSON><PERSON>(100)", "column_key": "", "column_comment": ""}, {"column_name": "table_name", "ordinal_position": "6", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "100", "column_type": "<PERSON><PERSON><PERSON>(100)", "column_key": "", "column_comment": ""}, {"column_name": "table_process_status_id", "ordinal_position": "29", "is_nullable": "YES", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "table_schema_name", "ordinal_position": "4", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "64", "column_type": "<PERSON><PERSON><PERSON>(64)", "column_key": "", "column_comment": ""}, {"column_name": "table_type", "ordinal_position": "7", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "100", "column_type": "<PERSON><PERSON><PERSON>(100)", "column_key": "", "column_comment": ""}, {"column_name": "timezone", "ordinal_position": "21", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "10", "column_type": "<PERSON><PERSON><PERSON>(10)", "column_key": "", "column_comment": ""}, {"column_name": "total_current_data_length", "ordinal_position": "12", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "total_current_table_rows", "ordinal_position": "11", "is_nullable": "NO", "data_type": "int", "character_max_length": "NULL", "column_type": "int unsigned", "column_key": "", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "24", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "version", "ordinal_position": "9", "is_nullable": "YES", "data_type": "int", "character_max_length": "NULL", "column_type": "int unsigned", "column_key": "", "column_comment": ""}]}, {"table_name": "client_db_server_table_column", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "558", "total_current_data_length": "98304", "total_current_index_length": "16384", "avg_row_length": "176", "max_data_length": "0", "data_free": "0", "auto_increment": "690", "create_time": "2025-08-07 12:15:53", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "character_max_length", "ordinal_position": "10", "is_nullable": "NO", "data_type": "int", "character_max_length": "NULL", "column_type": "int unsigned", "column_key": "", "column_comment": ""}, {"column_name": "client_db_server_table_id", "ordinal_position": "2", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "column_comment", "ordinal_position": "13", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "250", "column_type": "<PERSON><PERSON><PERSON>(250)", "column_key": "", "column_comment": ""}, {"column_name": "column_key", "ordinal_position": "12", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "100", "column_type": "<PERSON><PERSON><PERSON>(100)", "column_key": "", "column_comment": ""}, {"column_name": "column_name", "ordinal_position": "6", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "64", "column_type": "<PERSON><PERSON><PERSON>(64)", "column_key": "", "column_comment": ""}, {"column_name": "column_type", "ordinal_position": "11", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "100", "column_type": "<PERSON><PERSON><PERSON>(100)", "column_key": "", "column_comment": ""}, {"column_name": "created_at", "ordinal_position": "14", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "data_type", "ordinal_position": "9", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "64", "column_type": "<PERSON><PERSON><PERSON>(64)", "column_key": "", "column_comment": ""}, {"column_name": "deleted_at", "ordinal_position": "17", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "is_deleted", "ordinal_position": "16", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint", "column_key": "", "column_comment": ""}, {"column_name": "is_nullable", "ordinal_position": "8", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "3", "column_type": "<PERSON><PERSON><PERSON>(3)", "column_key": "", "column_comment": ""}, {"column_name": "ordinal_position", "ordinal_position": "7", "is_nullable": "NO", "data_type": "int", "character_max_length": "NULL", "column_type": "int unsigned", "column_key": "", "column_comment": ""}, {"column_name": "table_database_name", "ordinal_position": "4", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "100", "column_type": "<PERSON><PERSON><PERSON>(100)", "column_key": "", "column_comment": ""}, {"column_name": "table_name", "ordinal_position": "5", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "64", "column_type": "<PERSON><PERSON><PERSON>(64)", "column_key": "", "column_comment": ""}, {"column_name": "table_schema_name", "ordinal_position": "3", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "64", "column_type": "<PERSON><PERSON><PERSON>(64)", "column_key": "", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "15", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}, {"table_name": "client_db_server_table_stat", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "25", "total_current_data_length": "16384", "total_current_index_length": "81920", "avg_row_length": "655", "max_data_length": "0", "data_free": "0", "auto_increment": "45", "create_time": "2025-04-24 20:21:58", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "client_database_id", "ordinal_position": "20", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "client_db_server_id", "ordinal_position": "2", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "client_db_server_table_id", "ordinal_position": "3", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "company_id", "ordinal_position": "5", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "created_at", "ordinal_position": "17", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "deleted_at", "ordinal_position": "19", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "dim_date_id", "ordinal_position": "4", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "is_deleted", "ordinal_position": "16", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint", "column_key": "", "column_comment": ""}, {"column_name": "stat_date", "ordinal_position": "6", "is_nullable": "NO", "data_type": "date", "character_max_length": "NULL", "column_type": "date", "column_key": "", "column_comment": ""}, {"column_name": "total_db_storage_setup", "ordinal_position": "9", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "total_db_storage_setup_cost", "ordinal_position": "12", "is_nullable": "NO", "data_type": "decimal", "character_max_length": "NULL", "column_type": "decimal(8,2) unsigned", "column_key": "", "column_comment": ""}, {"column_name": "total_table_data_size_archived", "ordinal_position": "13", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "total_table_index", "ordinal_position": "11", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "total_table_object_storage_used", "ordinal_position": "14", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "total_table_rows", "ordinal_position": "7", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "total_table_rows_archived", "ordinal_position": "8", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "total_table_size", "ordinal_position": "10", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "total_table_storage_cost_saving", "ordinal_position": "15", "is_nullable": "NO", "data_type": "decimal", "character_max_length": "NULL", "column_type": "decimal(6,2) unsigned", "column_key": "", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "18", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}, {"table_name": "client_db_server_type", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "0", "total_current_data_length": "16384", "total_current_index_length": "16384", "avg_row_length": "0", "max_data_length": "0", "data_free": "0", "auto_increment": "2", "create_time": "2025-04-24 20:21:58", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "created_at", "ordinal_position": "5", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "deleted_at", "ordinal_position": "4", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "is_active", "ordinal_position": "3", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint(1)", "column_key": "", "column_comment": ""}, {"column_name": "name", "ordinal_position": "2", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "50", "column_type": "<PERSON><PERSON><PERSON>(50)", "column_key": "UNI", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "6", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}, {"table_name": "client_object_storage", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "0", "total_current_data_length": "16384", "total_current_index_length": "32768", "avg_row_length": "0", "max_data_length": "0", "data_free": "0", "auto_increment": "1", "create_time": "2025-04-24 20:21:58", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "bucket_name", "ordinal_position": "2", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "100", "column_type": "<PERSON><PERSON><PERSON>(100)", "column_key": "", "column_comment": ""}, {"column_name": "company_id", "ordinal_position": "4", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "created_at", "ordinal_position": "5", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "object_storage_type_id", "ordinal_position": "3", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "6", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}, {"table_name": "company", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "1", "total_current_data_length": "16384", "total_current_index_length": "81920", "avg_row_length": "16384", "max_data_length": "0", "data_free": "0", "auto_increment": "30", "create_time": "2025-04-24 20:21:58", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "company_address", "ordinal_position": "3", "is_nullable": "NO", "data_type": "text", "character_max_length": "65535", "column_type": "text", "column_key": "", "column_comment": ""}, {"column_name": "company_address2", "ordinal_position": "4", "is_nullable": "YES", "data_type": "text", "character_max_length": "65535", "column_type": "text", "column_key": "", "column_comment": ""}, {"column_name": "company_city", "ordinal_position": "5", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "company_name", "ordinal_position": "2", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "UNI", "column_comment": ""}, {"column_name": "company_postalcode", "ordinal_position": "6", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "country_id", "ordinal_position": "7", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "created_at", "ordinal_position": "13", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "database_server_option_id", "ordinal_position": "9", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "database_table_option_id", "ordinal_position": "10", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "deleted_at", "ordinal_position": "12", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "is_active", "ordinal_position": "11", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint(1)", "column_key": "", "column_comment": ""}, {"column_name": "state_id", "ordinal_position": "8", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "14", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}, {"table_name": "country", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "0", "total_current_data_length": "16384", "total_current_index_length": "16384", "avg_row_length": "0", "max_data_length": "0", "data_free": "0", "auto_increment": "1", "create_time": "2025-04-24 20:21:59", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "country_name", "ordinal_position": "2", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "UNI", "column_comment": ""}, {"column_name": "created_at", "ordinal_position": "5", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "deleted_at", "ordinal_position": "4", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "is_active", "ordinal_position": "3", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint(1)", "column_key": "", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "6", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}, {"table_name": "database_job_schedule", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "2", "total_current_data_length": "16384", "total_current_index_length": "32768", "avg_row_length": "8192", "max_data_length": "0", "data_free": "0", "auto_increment": "9", "create_time": "2025-04-24 20:21:59", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "client_database_id", "ordinal_position": "5", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "client_db_server_id", "ordinal_position": "2", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "created_at", "ordinal_position": "7", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "database_name", "ordinal_position": "4", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "deleted_at", "ordinal_position": "6", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "server_name", "ordinal_position": "3", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "8", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}, {"table_name": "database_server_option", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "5", "total_current_data_length": "16384", "total_current_index_length": "16384", "avg_row_length": "3276", "max_data_length": "0", "data_free": "0", "auto_increment": "5", "create_time": "2025-04-24 20:21:59", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "created_at", "ordinal_position": "5", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "deleted_at", "ordinal_position": "4", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "is_active", "ordinal_position": "3", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint(1)", "column_key": "", "column_comment": ""}, {"column_name": "name", "ordinal_position": "2", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "UNI", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "6", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}, {"table_name": "database_table_option", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "5", "total_current_data_length": "16384", "total_current_index_length": "16384", "avg_row_length": "3276", "max_data_length": "0", "data_free": "0", "auto_increment": "5", "create_time": "2025-04-24 20:21:59", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "created_at", "ordinal_position": "5", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "deleted_at", "ordinal_position": "4", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "is_active", "ordinal_position": "3", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint(1)", "column_key": "", "column_comment": ""}, {"column_name": "name", "ordinal_position": "2", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "UNI", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "6", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}, {"table_name": "dim_date", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "1461", "total_current_data_length": "147456", "total_current_index_length": "49152", "avg_row_length": "100", "max_data_length": "0", "data_free": "0", "auto_increment": "1466", "create_time": "2025-04-24 20:21:59", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "created_at", "ordinal_position": "11", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "date", "ordinal_position": "2", "is_nullable": "NO", "data_type": "date", "character_max_length": "NULL", "column_type": "date", "column_key": "UNI", "column_comment": ""}, {"column_name": "day_name", "ordinal_position": "3", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "deleted_at", "ordinal_position": "13", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "is_deleted", "ordinal_position": "10", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint", "column_key": "", "column_comment": ""}, {"column_name": "month_name", "ordinal_position": "7", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "25", "column_type": "<PERSON><PERSON><PERSON>(25)", "column_key": "", "column_comment": ""}, {"column_name": "month_number", "ordinal_position": "6", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "15", "column_type": "<PERSON><PERSON><PERSON>(15)", "column_key": "", "column_comment": ""}, {"column_name": "quarter", "ordinal_position": "8", "is_nullable": "NO", "data_type": "char", "character_max_length": "2", "column_type": "char(2)", "column_key": "", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "12", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "week_name", "ordinal_position": "5", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "25", "column_type": "<PERSON><PERSON><PERSON>(25)", "column_key": "", "column_comment": ""}, {"column_name": "week_number", "ordinal_position": "4", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint", "column_key": "", "column_comment": ""}, {"column_name": "year", "ordinal_position": "9", "is_nullable": "NO", "data_type": "smallint", "character_max_length": "NULL", "column_type": "smallint", "column_key": "", "column_comment": ""}]}, {"table_name": "failed_jobs", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "0", "total_current_data_length": "16384", "total_current_index_length": "16384", "avg_row_length": "0", "max_data_length": "0", "data_free": "0", "auto_increment": "NULL", "create_time": "2025-04-24 20:21:59", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "connection", "ordinal_position": "3", "is_nullable": "NO", "data_type": "text", "character_max_length": "65535", "column_type": "text", "column_key": "", "column_comment": ""}, {"column_name": "exception", "ordinal_position": "6", "is_nullable": "NO", "data_type": "longtext", "character_max_length": "4294967295", "column_type": "longtext", "column_key": "", "column_comment": ""}, {"column_name": "failed_at", "ordinal_position": "7", "is_nullable": "NO", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "payload", "ordinal_position": "5", "is_nullable": "NO", "data_type": "longtext", "character_max_length": "4294967295", "column_type": "longtext", "column_key": "", "column_comment": ""}, {"column_name": "queue", "ordinal_position": "4", "is_nullable": "NO", "data_type": "text", "character_max_length": "65535", "column_type": "text", "column_key": "", "column_comment": ""}, {"column_name": "uuid", "ordinal_position": "2", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "UNI", "column_comment": ""}]}, {"table_name": "jobs", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "78", "total_current_data_length": "98304", "total_current_index_length": "16384", "avg_row_length": "1260", "max_data_length": "0", "data_free": "0", "auto_increment": "81", "create_time": "2025-04-24 20:21:59", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "attempts", "ordinal_position": "4", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "available_at", "ordinal_position": "6", "is_nullable": "NO", "data_type": "int", "character_max_length": "NULL", "column_type": "int unsigned", "column_key": "", "column_comment": ""}, {"column_name": "created_at", "ordinal_position": "7", "is_nullable": "NO", "data_type": "int", "character_max_length": "NULL", "column_type": "int unsigned", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "payload", "ordinal_position": "3", "is_nullable": "NO", "data_type": "longtext", "character_max_length": "4294967295", "column_type": "longtext", "column_key": "", "column_comment": ""}, {"column_name": "queue", "ordinal_position": "2", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "MUL", "column_comment": ""}, {"column_name": "reserved_at", "ordinal_position": "5", "is_nullable": "YES", "data_type": "int", "character_max_length": "NULL", "column_type": "int unsigned", "column_key": "", "column_comment": ""}]}, {"table_name": "migrations", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "63", "total_current_data_length": "16384", "total_current_index_length": "0", "avg_row_length": "260", "max_data_length": "0", "data_free": "0", "auto_increment": "70", "create_time": "2025-04-24 20:21:59", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "batch", "ordinal_position": "3", "is_nullable": "NO", "data_type": "int", "character_max_length": "NULL", "column_type": "int", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "int", "character_max_length": "NULL", "column_type": "int unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "migration", "ordinal_position": "2", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}]}, {"table_name": "object_storage_export_history", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "32", "total_current_data_length": "16384", "total_current_index_length": "32768", "avg_row_length": "512", "max_data_length": "0", "data_free": "0", "auto_increment": "45", "create_time": "2025-06-15 14:43:31", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "client_db_server_table_id", "ordinal_position": "2", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "created_at", "ordinal_position": "21", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "deleted_at", "ordinal_position": "23", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "error_message", "ordinal_position": "18", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "250", "column_type": "<PERSON><PERSON><PERSON>(250)", "column_key": "", "column_comment": ""}, {"column_name": "export_status", "ordinal_position": "15", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "15", "column_type": "<PERSON><PERSON><PERSON>(15)", "column_key": "", "column_comment": ""}, {"column_name": "file_compression_percentage", "ordinal_position": "11", "is_nullable": "NO", "data_type": "decimal", "character_max_length": "NULL", "column_type": "decimal(5,2)", "column_key": "", "column_comment": ""}, {"column_name": "file_compression_ratio", "ordinal_position": "10", "is_nullable": "NO", "data_type": "decimal", "character_max_length": "NULL", "column_type": "decimal(5,2)", "column_key": "", "column_comment": ""}, {"column_name": "file_export_ended_at", "ordinal_position": "14", "is_nullable": "YES", "data_type": "datetime", "character_max_length": "NULL", "column_type": "datetime", "column_key": "", "column_comment": ""}, {"column_name": "file_export_size", "ordinal_position": "8", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "file_export_started_at", "ordinal_position": "13", "is_nullable": "NO", "data_type": "datetime", "character_max_length": "NULL", "column_type": "datetime", "column_key": "", "column_comment": ""}, {"column_name": "file_exported_name", "ordinal_position": "5", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "150", "column_type": "<PERSON><PERSON><PERSON>(150)", "column_key": "", "column_comment": ""}, {"column_name": "file_stored_size", "ordinal_position": "9", "is_nullable": "NO", "data_type": "int", "character_max_length": "NULL", "column_type": "int unsigned", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "is_current", "ordinal_position": "19", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint", "column_key": "", "column_comment": ""}, {"column_name": "is_deleted", "ordinal_position": "20", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint", "column_key": "", "column_comment": ""}, {"column_name": "is_export_error", "ordinal_position": "17", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint", "column_key": "", "column_comment": ""}, {"column_name": "is_file_compressed", "ordinal_position": "7", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint", "column_key": "", "column_comment": ""}, {"column_name": "last_exported_id", "ordinal_position": "16", "is_nullable": "NO", "data_type": "int", "character_max_length": "NULL", "column_type": "int", "column_key": "", "column_comment": ""}, {"column_name": "object_storage_dir_name", "ordinal_position": "6", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "100", "column_type": "<PERSON><PERSON><PERSON>(100)", "column_key": "", "column_comment": ""}, {"column_name": "object_storage_type_id", "ordinal_position": "3", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "table_name", "ordinal_position": "4", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "100", "column_type": "<PERSON><PERSON><PERSON>(100)", "column_key": "", "column_comment": ""}, {"column_name": "total_row_exported", "ordinal_position": "12", "is_nullable": "NO", "data_type": "int", "character_max_length": "NULL", "column_type": "int unsigned", "column_key": "", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "22", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}, {"table_name": "object_storage_type", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "9", "total_current_data_length": "16384", "total_current_index_length": "0", "avg_row_length": "1820", "max_data_length": "0", "data_free": "0", "auto_increment": "9", "create_time": "2025-04-24 20:21:59", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "cloud_provider_name", "ordinal_position": "3", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "100", "column_type": "<PERSON><PERSON><PERSON>(100)", "column_key": "", "column_comment": ""}, {"column_name": "created_at", "ordinal_position": "5", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "is_active", "ordinal_position": "4", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint", "column_key": "", "column_comment": ""}, {"column_name": "object_storage_type", "ordinal_position": "2", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "50", "column_type": "<PERSON><PERSON><PERSON>(50)", "column_key": "", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "6", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}, {"table_name": "personal_access_tokens", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "99", "total_current_data_length": "16384", "total_current_index_length": "32768", "avg_row_length": "165", "max_data_length": "0", "data_free": "0", "auto_increment": "183", "create_time": "2025-04-24 20:22:00", "update_time": "2025-08-07 10:57:13", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "abilities", "ordinal_position": "6", "is_nullable": "YES", "data_type": "text", "character_max_length": "65535", "column_type": "text", "column_key": "", "column_comment": ""}, {"column_name": "created_at", "ordinal_position": "9", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "expires_at", "ordinal_position": "8", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "last_used_at", "ordinal_position": "7", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "name", "ordinal_position": "4", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "token", "ordinal_position": "5", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "64", "column_type": "<PERSON><PERSON><PERSON>(64)", "column_key": "UNI", "column_comment": ""}, {"column_name": "tokenable_id", "ordinal_position": "3", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "tokenable_type", "ordinal_position": "2", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "MUL", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "10", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}, {"table_name": "remote_server", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "6", "total_current_data_length": "16384", "total_current_index_length": "65536", "avg_row_length": "2730", "max_data_length": "0", "data_free": "0", "auto_increment": "20", "create_time": "2025-04-24 20:22:00", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "agent_uuid", "ordinal_position": "7", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "company_id", "ordinal_position": "3", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "created_at", "ordinal_position": "14", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "deleted_at", "ordinal_position": "13", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "hostname", "ordinal_position": "4", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "UNI", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "is_active", "ordinal_position": "12", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint(1)", "column_key": "", "column_comment": ""}, {"column_name": "is_deleted", "ordinal_position": "16", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint", "column_key": "", "column_comment": ""}, {"column_name": "is_tls_required", "ordinal_position": "10", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint(1)", "column_key": "", "column_comment": ""}, {"column_name": "is_valid", "ordinal_position": "11", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint(1)", "column_key": "", "column_comment": ""}, {"column_name": "name", "ordinal_position": "2", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "UNI", "column_comment": ""}, {"column_name": "os_type", "ordinal_position": "8", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "port", "ordinal_position": "6", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "remote_server_status_id", "ordinal_position": "9", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "15", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "username", "ordinal_position": "5", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}]}, {"table_name": "remote_server_status", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "3", "total_current_data_length": "16384", "total_current_index_length": "16384", "avg_row_length": "5461", "max_data_length": "0", "data_free": "0", "auto_increment": "4", "create_time": "2025-04-24 20:22:00", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "created_at", "ordinal_position": "5", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "deleted_at", "ordinal_position": "4", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "is_active", "ordinal_position": "3", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint(1)", "column_key": "", "column_comment": ""}, {"column_name": "name", "ordinal_position": "2", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "50", "column_type": "<PERSON><PERSON><PERSON>(50)", "column_key": "UNI", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "6", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}, {"table_name": "state", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "51", "total_current_data_length": "16384", "total_current_index_length": "32768", "avg_row_length": "321", "max_data_length": "0", "data_free": "0", "auto_increment": "51", "create_time": "2025-04-24 20:22:00", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "country_id", "ordinal_position": "3", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "created_at", "ordinal_position": "6", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "deleted_at", "ordinal_position": "5", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "is_active", "ordinal_position": "4", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint(1)", "column_key": "", "column_comment": ""}, {"column_name": "state_name", "ordinal_position": "2", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "UNI", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "7", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}, {"table_name": "subscription", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "18", "total_current_data_length": "16384", "total_current_index_length": "32768", "avg_row_length": "910", "max_data_length": "0", "data_free": "0", "auto_increment": "18", "create_time": "2025-04-24 20:22:00", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "created_at", "ordinal_position": "11", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "deleted_at", "ordinal_position": "10", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "ends_at", "ordinal_position": "9", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "name", "ordinal_position": "3", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "quantity", "ordinal_position": "7", "is_nullable": "YES", "data_type": "int", "character_max_length": "NULL", "column_type": "int", "column_key": "", "column_comment": ""}, {"column_name": "stripe_id", "ordinal_position": "4", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "UNI", "column_comment": ""}, {"column_name": "stripe_price", "ordinal_position": "6", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "stripe_status", "ordinal_position": "5", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "trial_ends_at", "ordinal_position": "8", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "12", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "user_id", "ordinal_position": "2", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}]}, {"table_name": "subscription_item", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "18", "total_current_data_length": "16384", "total_current_index_length": "32768", "avg_row_length": "910", "max_data_length": "0", "data_free": "0", "auto_increment": "18", "create_time": "2025-04-24 20:22:00", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "created_at", "ordinal_position": "7", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "quantity", "ordinal_position": "6", "is_nullable": "YES", "data_type": "int", "character_max_length": "NULL", "column_type": "int", "column_key": "", "column_comment": ""}, {"column_name": "stripe_id", "ordinal_position": "3", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "UNI", "column_comment": ""}, {"column_name": "stripe_price", "ordinal_position": "5", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "stripe_product", "ordinal_position": "4", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "subscription_id", "ordinal_position": "2", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "8", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}, {"table_name": "subscription_plan", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "9", "total_current_data_length": "16384", "total_current_index_length": "16384", "avg_row_length": "1820", "max_data_length": "0", "data_free": "0", "auto_increment": "10", "create_time": "2025-04-24 20:22:00", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "created_at", "ordinal_position": "13", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "deleted_at", "ordinal_position": "12", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "is_active", "ordinal_position": "11", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint(1)", "column_key": "", "column_comment": ""}, {"column_name": "key_benefits", "ordinal_position": "9", "is_nullable": "YES", "data_type": "json", "character_max_length": "NULL", "column_type": "json", "column_key": "", "column_comment": ""}, {"column_name": "plan_name", "ordinal_position": "2", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "plan_price", "ordinal_position": "4", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "plan_price_effect_date", "ordinal_position": "5", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "plan_price_end_date", "ordinal_position": "6", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "price_per_table", "ordinal_position": "8", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "stripe_plan", "ordinal_position": "10", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "50", "column_type": "<PERSON><PERSON><PERSON>(50)", "column_key": "", "column_comment": ""}, {"column_name": "subscription_type_id", "ordinal_position": "3", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "table_plan_limit", "ordinal_position": "7", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "14", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}, {"table_name": "subscription_type", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "0", "total_current_data_length": "16384", "total_current_index_length": "0", "avg_row_length": "0", "max_data_length": "0", "data_free": "0", "auto_increment": "NULL", "create_time": "2025-04-24 20:09:45", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "created_at", "ordinal_position": "5", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "deleted_at", "ordinal_position": "4", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "is_active", "ordinal_position": "3", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint(1)", "column_key": "", "column_comment": ""}, {"column_name": "name", "ordinal_position": "2", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "UNI", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "6", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}, {"table_name": "table_job_schedule", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "9", "total_current_data_length": "16384", "total_current_index_length": "32768", "avg_row_length": "1820", "max_data_length": "0", "data_free": "0", "auto_increment": "18", "create_time": "2025-04-24 20:09:56", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "agent_uuid", "ordinal_position": "4", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "250", "column_type": "<PERSON><PERSON><PERSON>(250)", "column_key": "", "column_comment": ""}, {"column_name": "archive_day_of_week", "ordinal_position": "16", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "archive_start_at_utc", "ordinal_position": "18", "is_nullable": "YES", "data_type": "time", "character_max_length": "NULL", "column_type": "time", "column_key": "", "column_comment": ""}, {"column_name": "archive_untill_date_utc", "ordinal_position": "17", "is_nullable": "YES", "data_type": "time", "character_max_length": "NULL", "column_type": "time", "column_key": "", "column_comment": ""}, {"column_name": "batch_size", "ordinal_position": "13", "is_nullable": "NO", "data_type": "int", "character_max_length": "NULL", "column_type": "int", "column_key": "", "column_comment": ""}, {"column_name": "client_db_server_table_id", "ordinal_position": "2", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "created_at", "ordinal_position": "25", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "data_retention_days", "ordinal_position": "10", "is_nullable": "YES", "data_type": "smallint", "character_max_length": "NULL", "column_type": "smallint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "database_host", "ordinal_position": "5", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "300", "column_type": "<PERSON><PERSON><PERSON>(300)", "column_key": "", "column_comment": ""}, {"column_name": "database_job_schedule_id", "ordinal_position": "3", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "database_name", "ordinal_position": "7", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "150", "column_type": "<PERSON><PERSON><PERSON>(150)", "column_key": "", "column_comment": ""}, {"column_name": "database_port", "ordinal_position": "6", "is_nullable": "YES", "data_type": "smallint", "character_max_length": "NULL", "column_type": "smallint", "column_key": "", "column_comment": ""}, {"column_name": "deleted_at", "ordinal_position": "24", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "is_active", "ordinal_position": "14", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint", "column_key": "", "column_comment": ""}, {"column_name": "is_current", "ordinal_position": "15", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint unsigned", "column_key": "", "column_comment": ""}, {"column_name": "is_partitioned", "ordinal_position": "12", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint", "column_key": "", "column_comment": ""}, {"column_name": "local_time_zone", "ordinal_position": "19", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "35", "column_type": "<PERSON><PERSON><PERSON>(35)", "column_key": "", "column_comment": ""}, {"column_name": "object_backup_location", "ordinal_position": "11", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "250", "column_type": "<PERSON><PERSON><PERSON>(250)", "column_key": "", "column_comment": ""}, {"column_name": "retention_index", "ordinal_position": "23", "is_nullable": "YES", "data_type": "int", "character_max_length": "NULL", "column_type": "int", "column_key": "", "column_comment": ""}, {"column_name": "s3_file", "ordinal_position": "20", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "schedule_type", "ordinal_position": "22", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "64", "column_type": "<PERSON><PERSON><PERSON>(64)", "column_key": "", "column_comment": ""}, {"column_name": "schema_name", "ordinal_position": "8", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "100", "column_type": "<PERSON><PERSON><PERSON>(100)", "column_key": "", "column_comment": ""}, {"column_name": "table_name", "ordinal_position": "9", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "250", "column_type": "<PERSON><PERSON><PERSON>(250)", "column_key": "", "column_comment": ""}, {"column_name": "total_rows", "ordinal_position": "21", "is_nullable": "NO", "data_type": "int", "character_max_length": "NULL", "column_type": "int", "column_key": "", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "26", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}, {"table_name": "table_job_schedule_agent_logs", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "9", "total_current_data_length": "16384", "total_current_index_length": "32768", "avg_row_length": "1820", "max_data_length": "0", "data_free": "0", "auto_increment": "11", "create_time": "2025-07-08 14:41:47", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "created_at", "ordinal_position": "7", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "deleted_at", "ordinal_position": "6", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "log_message", "ordinal_position": "4", "is_nullable": "NO", "data_type": "text", "character_max_length": "65535", "column_type": "text", "column_key": "", "column_comment": ""}, {"column_name": "log_type", "ordinal_position": "5", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "table_job_schedule_execution_log_id", "ordinal_position": "2", "is_nullable": "YES", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "table_job_schedule_id", "ordinal_position": "3", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "8", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}, {"table_name": "table_job_schedule_execution_log", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "37", "total_current_data_length": "1572864", "total_current_index_length": "16384", "avg_row_length": "42509", "max_data_length": "0", "data_free": "0", "auto_increment": "134", "create_time": "2025-04-24 20:09:56", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "created_at", "ordinal_position": "10", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "day_of_week", "ordinal_position": "3", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "deleted_at", "ordinal_position": "9", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "execution_datetime", "ordinal_position": "5", "is_nullable": "YES", "data_type": "datetime", "character_max_length": "NULL", "column_type": "datetime", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "request_json", "ordinal_position": "7", "is_nullable": "YES", "data_type": "json", "character_max_length": "NULL", "column_type": "json", "column_key": "", "column_comment": ""}, {"column_name": "response_json", "ordinal_position": "8", "is_nullable": "YES", "data_type": "json", "character_max_length": "NULL", "column_type": "json", "column_key": "", "column_comment": ""}, {"column_name": "schedule_enc_key", "ordinal_position": "4", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "status", "ordinal_position": "6", "is_nullable": "NO", "data_type": "enum", "character_max_length": "21", "column_type": "enum('awaiting','in_progress','ssh_connection_failed','time_window_expired','execution_successfull')", "column_key": "", "column_comment": ""}, {"column_name": "table_job_schedule_id", "ordinal_position": "2", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "11", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}, {"table_name": "table_process_status", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "4", "total_current_data_length": "16384", "total_current_index_length": "0", "avg_row_length": "4096", "max_data_length": "0", "data_free": "0", "auto_increment": "4", "create_time": "2025-04-24 20:09:54", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "created_at", "ordinal_position": "3", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "table_action_name", "ordinal_position": "2", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "35", "column_type": "<PERSON><PERSON><PERSON>(35)", "column_key": "", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "4", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}, {"table_name": "timezone", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "459", "total_current_data_length": "49152", "total_current_index_length": "16384", "avg_row_length": "107", "max_data_length": "0", "data_free": "0", "auto_increment": "1380", "create_time": "2025-04-24 20:09:45", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "created_at", "ordinal_position": "5", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "deleted_at", "ordinal_position": "4", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "is_active", "ordinal_position": "3", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint(1)", "column_key": "", "column_comment": ""}, {"column_name": "name", "ordinal_position": "2", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "UNI", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "6", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}, {"table_name": "user", "table_type": "BASE TABLE", "engine": "InnoDB", "version": "10", "row_format": "Dynamic", "total_current_table_rows": "1", "total_current_data_length": "16384", "total_current_index_length": "65536", "avg_row_length": "16384", "max_data_length": "0", "data_free": "0", "auto_increment": "31", "create_time": "2025-04-24 20:09:45", "update_time": "NULL", "check_time": "NULL", "table_collation": "utf8mb4_unicode_ci", "checksum": "NULL", "create_options": "NULL", "table_comment": "NULL", "columns": [{"column_name": "company_id", "ordinal_position": "10", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "created_at", "ordinal_position": "19", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "deleted_at", "ordinal_position": "18", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "email", "ordinal_position": "4", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "UNI", "column_comment": ""}, {"column_name": "email_verified_at", "ordinal_position": "5", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "first_name", "ordinal_position": "2", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "id", "ordinal_position": "1", "is_nullable": "NO", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "PRI", "column_comment": ""}, {"column_name": "image", "ordinal_position": "8", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "is_active", "ordinal_position": "16", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint(1)", "column_key": "", "column_comment": ""}, {"column_name": "is_trial_active", "ordinal_position": "9", "is_nullable": "NO", "data_type": "tinyint", "character_max_length": "NULL", "column_type": "tinyint(1)", "column_key": "", "column_comment": ""}, {"column_name": "last_name", "ordinal_position": "3", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "otp", "ordinal_position": "6", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "password", "ordinal_position": "7", "is_nullable": "NO", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "pm_last_four", "ordinal_position": "14", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "4", "column_type": "<PERSON><PERSON><PERSON>(4)", "column_key": "", "column_comment": ""}, {"column_name": "pm_type", "ordinal_position": "13", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "", "column_comment": ""}, {"column_name": "remember_token", "ordinal_position": "17", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "100", "column_type": "<PERSON><PERSON><PERSON>(100)", "column_key": "", "column_comment": ""}, {"column_name": "stripe_id", "ordinal_position": "12", "is_nullable": "YES", "data_type": "<PERSON><PERSON><PERSON>", "character_max_length": "255", "column_type": "<PERSON><PERSON><PERSON>(255)", "column_key": "MUL", "column_comment": ""}, {"column_name": "timezone_id", "ordinal_position": "11", "is_nullable": "YES", "data_type": "bigint", "character_max_length": "NULL", "column_type": "bigint unsigned", "column_key": "MUL", "column_comment": ""}, {"column_name": "trial_ends_at", "ordinal_position": "15", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}, {"column_name": "updated_at", "ordinal_position": "20", "is_nullable": "YES", "data_type": "timestamp", "character_max_length": "NULL", "column_type": "timestamp", "column_key": "", "column_comment": ""}]}]}]