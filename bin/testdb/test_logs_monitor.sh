#!/bin/bash

# Cron Job Setup Script for MySQL Test Data Generation
# This script sets up automated data insertion every hour
# and provides archiving simulation commands

SCRIPT_DIR="/home/<USER>/test_db"
DATA_SCRIPT="$SCRIPT_DIR/insert_test_data.sh"
LOG_DIR="/home/<USER>/test_db/logs"
ARCHIVE_DIR="/home/<USER>/test_db/mysql-archives"

# Create necessary directories
create_directories() {
    echo "Creating necessary directories..."
    sudo mkdir -p $SCRIPT_DIR
    sudo mkdir -p $LOG_DIR
    sudo mkdir -p $ARCHIVE_DIR
    
    # Set permissions
    sudo chmod 755 $SCRIPT_DIR
    sudo chmod 755 $LOG_DIR
    sudo chmod 755 $ARCHIVE_DIR
    
    # # Copy the data insertion script
    # sudo cp insert_test_data.sh $DATA_SCRIPT
    # sudo chmod +x $DATA_SCRIPT
    
    echo "Directories created successfully."
}

# Install cron job for hourly data generation
install_cron_job() {
    echo "Installing cron job for hourly data generation..."
    
    # Create a temporary cron file
    TEMP_CRON=$(mktemp)
    
    # Get current crontab (if exists)
    crontab -l 2>/dev/null > $TEMP_CRON
    
    # Add the hourly job if it doesn't exist
    if ! grep -q "mysql-test-data" $TEMP_CRON; then
        echo "# MySQL Test Data Generation - Runs every hour" >> $TEMP_CRON
        echo "0 * * * * $DATA_SCRIPT 1500 8 >> $LOG_DIR/hourly_insertion.log 2>&1" >> $TEMP_CRON
        echo "" >> $TEMP_CRON
        
        # Install the new crontab
        crontab $TEMP_CRON
        echo "Cron job installed successfully."
    else
        echo "Cron job already exists."
    fi
    
    # Clean up
    rm $TEMP_CRON
}



# Create monitoring script
create_monitoring_script() {
    echo "Creating monitoring script..."
    
    cat > $SCRIPT_DIR/monitor_data.sh << 'EOF'
#!/bin/bash

# Database Monitoring Script
# Provides real-time statistics about database growth

# DB_HOST="localhost"
# DB_PORT="3306"
DB_NAME="web_logs"
# DB_USER="root"
# DB_PASS="your_password"

login_path="elysium_test-db"

# Function to display table statistics
show_table_stats() {
    echo "=== Database Statistics ==="
    echo "Date: $(date)"
    echo "Database: $DB_NAME"
    echo ""
    
    mysql --login-path="${login_path}" -e "USE $DB_NAME;" << 'EOSQL'
SELECT 
    table_name as 'Table Name',
    table_rows as 'Row Count',
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as 'Size (MB)',
    ROUND((data_length / 1024 / 1024), 2) as 'Data (MB)',
    ROUND((index_length / 1024 / 1024), 2) as 'Index (MB)',
    engine as 'Engine'
FROM information_schema.tables 
WHERE table_schema = DATABASE()
ORDER BY (data_length + index_length) DESC;

SELECT 
    'TOTAL DATABASE' as 'Table Name',
    SUM(table_rows) as 'Row Count',
    ROUND(SUM((data_length + index_length) / 1024 / 1024), 2) as 'Size (MB)',
    ROUND(SUM(data_length / 1024 / 1024), 2) as 'Data (MB)',
    ROUND(SUM(index_length / 1024 / 1024), 2) as 'Index (MB)',
    'MyISAM' as 'Engine'
FROM information_schema.tables 
WHERE table_schema = DATABASE();
EOSQL
}

# Function to show growth rate
show_growth_rate() {
    echo ""
    echo "=== Growth Analysis ==="
    
    # Show records created in last hour
    mysql --login-path="${login_path}" -e "USE $DB_NAME;" << 'EOSQL'
SELECT 
    'user_activity_logs' as table_name,
    COUNT(*) as records_last_hour
FROM user_activity_logs 
WHERE last_update >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
UNION ALL
SELECT 
    'performance_metrics' as table_name,
    COUNT(*) as records_last_hour
FROM performance_metrics 
WHERE last_update >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
UNION ALL
SELECT 
    'transaction_records' as table_name,
    COUNT(*) as records_last_hour
FROM transaction_records 
WHERE last_update >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
UNION ALL
SELECT 
    'transaction_details' as table_name,
    COUNT(*) as records_last_hour
FROM transaction_details 
WHERE last_update >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
UNION ALL
SELECT 
    'error_logs' as table_name,
    COUNT(*) as records_last_hour
FROM error_logs 
WHERE last_update >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
UNION ALL
SELECT 
    'api_call_logs' as table_name,
    COUNT(*) as records_last_hour
FROM api_call_logs 
WHERE last_update >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
UNION ALL
SELECT 
    'processing_queue' as table_name,
    COUNT(*) as records_last_hour
FROM processing_queue 
WHERE last_update >= DATE_SUB(NOW(), INTERVAL 1 HOUR);
EOSQL
}

# Function to estimate daily growth
estimate_daily_growth() {
    echo ""
    echo "=== Daily Growth Estimation ==="
    
    # Calculate current size and estimate 24-hour growth
    local current_size=$(mysql --login-path="${login_path}" -e "USE $DB_NAME;" -e "
    SELECT ROUND(SUM((data_length + index_length) / 1024 / 1024 / 1024), 2) as size_gb
    FROM information_schema.tables 
    WHERE table_schema = '$DB_NAME'
    " -s)
    
    echo "Current database size: ${current_size} GB"
    
    # Estimate growth based on hourly insertion rate
    local estimated_daily_growth=$(echo "scale=2; $current_size * 24" | bc)
    echo "Estimated daily growth: ${estimated_daily_growth} GB (if current rate continues)"
}

# Main function
main() {
    case "${1:-stats}" in
        "stats"|"")
            show_table_stats
            ;;
        "growth")
            show_growth_rate
            ;;
        "estimate")
            estimate_daily_growth
            ;;
        "full")
            show_table_stats
            show_growth_rate
            estimate_daily_growth
            ;;
        *)
            echo "Usage: $0 [stats|growth|estimate|full]"
            echo "  stats    - Show current table statistics (default)"
            echo "  growth   - Show growth rate analysis"
            echo "  estimate - Estimate daily growth"
            echo "  full     - Show all information"
            ;;
    esac
}

# Execute main function
main "$@"
EOF

    chmod +x $SCRIPT_DIR/monitor_data.sh
    echo "Monitoring script created at $SCRIPT_DIR/monitor_data.sh"
}

# Main installation function
main() {
    echo "MySQL Test Data Environment Setup"
    echo "=================================="
    
    # Check if running as root or with sudo
    if [[ $EUID -ne 0 ]]; then
        echo "This script requires root privileges. Please run with sudo."
        exit 1
    fi
    
    # Create directories and scripts
    create_directories
    create_archive_script
    create_monitoring_script
    
    # Install cron job
    install_cron_job
    
    echo ""
    echo "Setup completed successfully!"
    echo ""
    echo "Available commands:"
    echo "  $DATA_SCRIPT [batch_size] [iterations]  - Manual data insertion"
    echo "  $SCRIPT_DIR/monitor_data.sh [option]     - Database monitoring"
    echo ""
    echo "Log files:"
    echo "  - Hourly insertion: $LOG_DIR/hourly_insertion.log"
    echo "  - Daily archiving: $LOG_DIR/archive_YYYYMMDD.log"
    echo ""
    echo "To start immediately:"
    echo "  $DATA_SCRIPT 2000 10  # Generate initial data"
    echo "  $SCRIPT_DIR/monitor_data.sh full  # Check status"
}

# Execute main function
main "$@"