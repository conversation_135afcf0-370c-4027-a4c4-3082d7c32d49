-- MySQL Testing Database Schema for Archiving System
-- Target: 10GB daily data generation for archiving tests
-- Engine: MyISAM for faster bulk inserts

CREATE DATABASE IF NOT EXISTS web_logs;
USE web_logs;

-- Table 1: User Activity Logs (Main volume generator)
DROP TABLE IF EXISTS user_activity_logs;
CREATE TABLE user_activity_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_id VARCHAR(64) NOT NULL,
    activity_type VARCHAR(50) NOT NULL,
    activity_data TEXT NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NOT NULL,
    request_url VARCHAR(500) NOT NULL,
    response_code INT NOT NULL,
    response_time_ms INT NOT NULL,
    bytes_transferred BIGINT NOT NULL,
    last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (last_update),
    INDEX idx_session_id (session_id)
) ENGINE=MyISAM;

-- Table 2: System Performance Metrics (High volume time series)
DROP TABLE IF EXISTS performance_metrics;
CREATE TABLE performance_metrics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    server_id VARCHAR(50) NOT NULL,
    metric_type VARCHAR(50) NOT NULL,
    metric_value DECIMAL(15,4) NOT NULL,
    cpu_usage DECIMAL(5,2) NOT NULL,
    memory_usage DECIMAL(5,2) NOT NULL,
    disk_usage DECIMAL(5,2) NOT NULL,
    network_in_bytes BIGINT NOT NULL,
    network_out_bytes BIGINT NOT NULL,
    additional_data JSON,
    metadata TEXT,
    last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_server_id (server_id),
    INDEX idx_recorded_at (last_update),
    INDEX idx_metric_type (metric_type)
) ENGINE=MyISAM;

-- Table 3: Transaction Records (Parent table for financial data)
DROP TABLE IF EXISTS transaction_records;
CREATE TABLE transaction_records (
    transaction_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    account_id VARCHAR(50) NOT NULL,
    transaction_type VARCHAR(30) NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    description TEXT NOT NULL,
    reference_number VARCHAR(100) NOT NULL,
    merchant_data TEXT,
    location_data VARCHAR(200),
    risk_score DECIMAL(5,2),
    processing_fees DECIMAL(10,2),
    exchange_rate DECIMAL(10,6),
    original_amount DECIMAL(15,2),
    original_currency VARCHAR(3),
    status VARCHAR(20) NOT NULL,
    last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_account_id (account_id),
    INDEX idx_created_at (last_update),
    INDEX idx_transaction_type (transaction_type)
) ENGINE=MyISAM;

-- Table 4: Transaction Details (Child table - FK relationship)
DROP TABLE IF EXISTS transaction_details;
CREATE TABLE transaction_details (
    detail_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    transaction_id BIGINT NOT NULL,
    detail_type VARCHAR(50) NOT NULL,
    detail_value TEXT NOT NULL,
    additional_info TEXT,
    verification_data TEXT,
    processing_notes TEXT,
    compliance_data TEXT,
    audit_trail TEXT,
    last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_created_at (last_update),
    INDEX idx_detail_type (detail_type)
) ENGINE=MyISAM;

-- Table 5: Application Error Logs (Large text data)
DROP TABLE IF EXISTS error_logs;
CREATE TABLE error_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    application_name VARCHAR(100) NOT NULL,
    error_level VARCHAR(20) NOT NULL,
    error_code VARCHAR(50) NOT NULL,
    error_message TEXT NOT NULL,
    stack_trace TEXT NOT NULL,
    request_data TEXT,
    response_data TEXT,
    user_context TEXT,
    system_context TEXT,
    environment_data TEXT,
    correlation_id VARCHAR(100),
    last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_application_name (application_name),
    INDEX idx_occurred_at (last_update),
    INDEX idx_error_level (error_level)
) ENGINE=MyISAM;

-- Table 6: API Call Logs (High frequency data)
DROP TABLE IF EXISTS api_call_logs;
CREATE TABLE api_call_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    api_key VARCHAR(64) NOT NULL,
    endpoint VARCHAR(200) NOT NULL,
    method VARCHAR(10) NOT NULL,
    request_headers TEXT NOT NULL,
    request_body TEXT,
    response_headers TEXT NOT NULL,
    response_body TEXT,
    status_code INT NOT NULL,
    response_time_ms INT NOT NULL,
    request_size_bytes INT NOT NULL,
    response_size_bytes INT NOT NULL,
    client_ip VARCHAR(45) NOT NULL,
    user_agent TEXT,
    rate_limit_remaining INT,
    last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_api_key (api_key),
    INDEX idx_called_at (last_update),
    INDEX idx_endpoint (endpoint)
) ENGINE=MyISAM;

-- Table 7: Data Processing Queue (Bulk processing records)
DROP TABLE IF EXISTS processing_queue;
CREATE TABLE processing_queue (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    job_id VARCHAR(100) NOT NULL,
    job_type VARCHAR(50) NOT NULL,
    priority INT NOT NULL,
    status VARCHAR(20) NOT NULL,
    input_data LONGTEXT NOT NULL,
    output_data LONGTEXT,
    error_data TEXT,
    processing_metadata TEXT,
    configuration_data TEXT,
    performance_stats TEXT,
    retry_count INT DEFAULT 0,
    max_retries INT DEFAULT 3,
    last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    INDEX idx_job_id (job_id),
    INDEX idx_created_at (last_update),
    INDEX idx_status (status),
    INDEX idx_job_type (job_type)
) ENGINE=MyISAM;

-- Create foreign key relationship (Note: MyISAM doesn't enforce FK constraints but we document the relationship)
-- ALTER TABLE transaction_details ADD CONSTRAINT fk_transaction_details_transaction_id 
-- FOREIGN KEY (transaction_id) REFERENCES transaction_records(transaction_id);

-- Create indexes for archiving queries (assuming date-based archiving)
CREATE INDEX idx_user_activity_logs_created_at ON user_activity_logs(last_update);
CREATE INDEX idx_performance_metrics_recorded_at ON performance_metrics(last_update);
CREATE INDEX idx_transaction_records_created_at ON transaction_records(last_update);
CREATE INDEX idx_transaction_details_created_at ON transaction_details(last_update);
CREATE INDEX idx_error_logs_occurred_at ON error_logs(last_update);
CREATE INDEX idx_api_call_logs_called_at ON api_call_logs(last_update);
CREATE INDEX idx_processing_queue_created_at ON processing_queue(last_update);