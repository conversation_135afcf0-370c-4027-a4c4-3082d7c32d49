#!/bin/bash

# MySQL Testing Database - Automated Data Insertion Script
# Purpose: Generate large volumes of test data for archiving system testing
# Target: ~10GB daily data generation (~420MB per hour)
# Usage: ./insert_test_data.sh [batch_size] [iterations]

# Configuration
# DB_HOST="localhost"
# DB_PORT="3306"
DB_NAME="web_logs"
# DB_USER="root"
# DB_PASS="your_password"

login_path="elysium_test-db"

# Default batch settings (adjust based on your system performance)
BATCH_SIZE=${1:-1000}
ITERATIONS=${2:-10}

# Logging
LOG_FILE="/home/<USER>/test_db/logs/mysql_data_insertion.log"
exec 1> >(tee -a "$LOG_FILE")
exec 2>&1

echo "========================================="
echo "Starting data insertion at $(date)"
echo "Batch size: $BATCH_SIZE, Iterations: $ITERATIONS"
echo "========================================="

# Function to generate random string
generate_random_string() {
    local length=$1
    tr -dc 'a-zA-Z0-9' < /dev/urandom | head -c $length
}

# Function to generate random JSON data
generate_random_json() {
    cat <<EOF
{
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "session_data": "$(generate_random_string 50)",
    "user_preferences": {
        "theme": "$(shuf -n1 -e dark light auto)",
        "language": "$(shuf -n1 -e en es fr de ja)",
        "timezone": "$(shuf -n1 -e UTC EST PST GMT)"
    },
    "metadata": "$(generate_random_string 100)"
}
EOF
}

# Function to generate large text content
generate_large_text() {
    local size=$1
    for i in $(seq 1 $((size/100))); do
        echo "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore."
    done | tr '\n' ' '
}

# Function to insert data into user_activity_logs (High volume table)
insert_user_activity_logs() {
    local batch_size=$1
    echo "Inserting $batch_size records into user_activity_logs..."
    
    # First test the connection
    if ! mysql --login-path="${login_path}" -e "USE $DB_NAME" 2>/dev/null; then
        echo "ERROR: Failed to connect to database or database doesn't exist"
        return 1
    fi

    local sql_query="
INSERT INTO user_activity_logs 
(user_id, session_id, activity_type, activity_data, ip_address, user_agent, request_url, response_code, response_time_ms, bytes_transferred, last_update)
SELECT 
    FLOOR(RAND() * 1000000) + 1,
    CONCAT('sess_', SUBSTRING(MD5(RAND()), 1, 32)),
    ELT(FLOOR(RAND() * 8) + 1, 'login', 'logout', 'page_view', 'click', 'scroll', 'search', 'purchase', 'download'),
    CONCAT('{"action":"', ELT(FLOOR(RAND() * 5) + 1, 'view', 'click', 'hover', 'scroll', 'input'), '","element":"', 
           ELT(FLOOR(RAND() * 10) + 1, 'button', 'link', 'form', 'image', 'video', 'menu', 'header', 'footer', 'sidebar', 'content'), 
           '","timestamp":"', NOW(), '","additional_data":"', REPEAT('x', FLOOR(RAND() * 500) + 100), '"}'),
    CONCAT(FLOOR(RAND() * 255) + 1, '.', FLOOR(RAND() * 255) + 1, '.', FLOOR(RAND() * 255) + 1, '.', FLOOR(RAND() * 255) + 1),
    CONCAT('Mozilla/5.0 (', 
           ELT(FLOOR(RAND() * 3) + 1, 'Windows NT 10.0; Win64; x64', 'Macintosh; Intel Mac OS X 10_15_7', 'X11; Linux x86_64'),
           ') AppleWebKit/537.36 (KHTML, like Gecko) Chrome/', FLOOR(RAND() * 20) + 90, '.0.', FLOOR(RAND() * 5000) + 1000, 
           '.', FLOOR(RAND() * 100) + 1, ' Safari/537.36'),
    CONCAT('https://example.com/', ELT(FLOOR(RAND() * 10) + 1, 'home', 'products', 'services', 'about', 'contact', 'blog', 'news', 'support', 'login', 'dashboard'),
           '?param1=', SUBSTRING(MD5(RAND()), 1, 8), '&param2=', SUBSTRING(MD5(RAND()), 1, 8)),
    ELT(FLOOR(RAND() * 6) + 1, 200, 201, 301, 302, 404, 500),
    FLOOR(RAND() * 5000) + 50,
    FLOOR(RAND() * 1000000) + 1024,
    DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 86400) SECOND)
FROM 
    (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10) t1,
    (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10) t2,
    (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10) t3
LIMIT $batch_size;
"

    # Execute with error handling
    if ! mysql --login-path="${login_path}" "$DB_NAME" -e "$sql_query"; then
        echo "ERROR: Failed to insert records into user_activity_logs"
        return 1
    fi
    
    echo "Successfully inserted $batch_size records into user_activity_logs"
}

# Function to insert data into performance_metrics
insert_performance_metrics() {
    local batch_size=$1
    echo "Inserting $batch_size records into performance_metrics..."
    
    # First test the connection
    if ! mysql --login-path="${login_path}" -e "USE $DB_NAME" 2>/dev/null; then
        echo "ERROR: Failed to connect to database or database doesn't exist"
        return 1
    fi

    local sql_query="
INSERT INTO performance_metrics 
(server_id, metric_type, metric_value, cpu_usage, memory_usage, disk_usage, network_in_bytes, network_out_bytes, additional_data, metadata, last_update)
SELECT 
    CONCAT('srv_', LPAD(FLOOR(RAND() * 100) + 1, 3, '0')),
    ELT(FLOOR(RAND() * 8) + 1, 'cpu_usage', 'memory_usage', 'disk_io', 'network_traffic', 'response_time', 'throughput', 'error_rate', 'availability'),
    ROUND(RAND() * 100, 4),
    ROUND(RAND() * 100, 2),
    ROUND(RAND() * 100, 2),
    ROUND(RAND() * 100, 2),
    FLOOR(RAND() * 1000000000) + 1024,
    FLOOR(RAND() * 1000000000) + 1024,
    CONCAT('{\"alerts\":[', 
           '\"', ELT(FLOOR(RAND() * 5) + 1, 'high_cpu', 'low_memory', 'disk_full', 'network_slow', 'service_down'), '\"',
           '],\"thresholds\":{\"cpu\":', ROUND(RAND() * 100, 2), ',\"memory\":', ROUND(RAND() * 100, 2), '},\"environment\":\"',
           ELT(FLOOR(RAND() * 3) + 1, 'production', 'staging', 'development'), '\"}'),
    CONCAT('cluster=', ELT(FLOOR(RAND() * 5) + 1, 'web', 'api', 'database', 'cache', 'queue'), 
           ' datacenter=', ELT(FLOOR(RAND() * 3) + 1, 'us-east', 'us-west', 'eu-central'),
           ' additional_info=', REPEAT('m', FLOOR(RAND() * 200) + 100)),
    DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 86400) SECOND)
FROM 
    (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10) t1,
    (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10) t2,
    (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10) t3
LIMIT $batch_size;"

    # Execute with error handling
    if ! mysql --login-path="${login_path}" "$DB_NAME" -e "$sql_query"; then
        echo "ERROR: Failed to insert records into performance_metrics"
        return 1
    fi
    
    echo "Successfully inserted $batch_size records into performance_metrics"
}
# Function to insert data into transaction_records (Parent table)
insert_transaction_records() {
    local batch_size=$1
    echo "Inserting $batch_size records into transaction_records..."
    
    # First test the connection
    if ! mysql --login-path="${login_path}" -e "USE $DB_NAME" 2>/dev/null; then
        echo "ERROR: Failed to connect to database or database doesn't exist"
        return 1
    fi

    local sql_query="
INSERT INTO transaction_records 
(account_id, transaction_type, amount, currency, description, reference_number, merchant_data, location_data, risk_score, processing_fees, exchange_rate, original_amount, original_currency, status, last_update, updated_at)
SELECT 
    CONCAT('ACC_', LPAD(FLOOR(RAND() * 1000000) + 1, 8, '0')),
    ELT(FLOOR(RAND() * 6) + 1, 'purchase', 'withdrawal', 'deposit', 'transfer', 'refund', 'fee'),
    ROUND((RAND() * 9999) + 1, 2),
    ELT(FLOOR(RAND() * 5) + 1, 'USD', 'EUR', 'GBP', 'JPY', 'CAD'),
    CONCAT('Transaction for ', 
           ELT(FLOOR(RAND() * 10) + 1, 'online purchase', 'ATM withdrawal', 'direct deposit', 'wire transfer', 'card payment', 'subscription', 'refund', 'fee payment', 'cash advance', 'balance transfer'),
           ' processed at ', NOW(), ' with additional details: ', REPEAT('d', FLOOR(RAND() * 300) + 100)),
    CONCAT('REF_', UPPER(SUBSTRING(MD5(RAND()), 1, 16))),
    CONCAT('{\"merchant_id\":\"', FLOOR(RAND() * 100000) + 1, '\",\"merchant_name\":\"', 
           ELT(FLOOR(RAND() * 10) + 1, 'Amazon', 'Walmart', 'Target', 'Starbucks', 'McDonalds', 'Shell', 'Exxon', 'Home Depot', 'Best Buy', 'Costco'),
           '\",\"category\":\"', ELT(FLOOR(RAND() * 8) + 1, 'retail', 'food', 'gas', 'entertainment', 'travel', 'healthcare', 'utilities', 'other'),
           '\",\"additional_data\":\"', REPEAT('x', FLOOR(RAND() * 200) + 50), '\"}'),
    CONCAT(ELT(FLOOR(RAND() * 50) + 1, 'New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia', 'San Antonio', 'San Diego', 'Dallas', 'San Jose'), 
           ', ', ELT(FLOOR(RAND() * 5) + 1, 'NY', 'CA', 'TX', 'FL', 'IL'), ' ', LPAD(FLOOR(RAND() * 99999) + 10000, 5, '0')),
    ROUND(RAND() * 100, 2),
    ROUND(RAND() * 10, 2),
    ROUND(RAND() * 2 + 0.5, 6),
    ROUND((RAND() * 9999) + 1, 2),
    ELT(FLOOR(RAND() * 5) + 1, 'USD', 'EUR', 'GBP', 'JPY', 'CAD'),
    ELT(FLOOR(RAND() * 4) + 1, 'completed', 'pending', 'failed', 'cancelled'),
    DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 86400) SECOND),
    DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 3600) SECOND)
FROM 
    (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10) t1,
    (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10) t2,
    (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10) t3
LIMIT $batch_size;"

    # Execute with error handling
    if ! mysql --login-path="${login_path}" "$DB_NAME" -e "$sql_query"; then
        echo "ERROR: Failed to insert records into transaction_records"
        return 1
    fi
    
    echo "Successfully inserted $batch_size records into transaction_records"
}

# Function to insert data into transaction_details (Child table)
insert_transaction_details() {
    local batch_size=$1
    echo "Inserting $batch_size records into transaction_details..."
    # First test the connection
    if ! mysql --login-path="${login_path}" -e "USE $DB_NAME" 2>/dev/null; then
        echo "ERROR: Failed to connect to database or database doesn't exist"
        return 1
    fi

    local sql_query="
INSERT INTO transaction_details 
(transaction_id, detail_type, detail_value, additional_info, verification_data, processing_notes, compliance_data, audit_trail, last_update)
SELECT 
    t.transaction_id,
    ELT(FLOOR(RAND() * 6) + 1, 'verification', 'compliance', 'risk_assessment', 'fraud_check', 'audit', 'notification'),
    CONCAT('Detail for transaction ', t.transaction_id, ' - ', 
           ELT(FLOOR(RAND() * 5) + 1, 'verified', 'pending review', 'flagged', 'approved', 'rejected'),
           ' with data: ', REPEAT('v', FLOOR(RAND() * 400) + 200)),
    CONCAT('Additional information for processing: ', REPEAT('a', FLOOR(RAND() * 300) + 100)),
    CONCAT('Verification completed at ', NOW(), ' with result: ', 
           ELT(FLOOR(RAND() * 3) + 1, 'passed', 'failed', 'manual_review'),
           ' details: ', REPEAT('r', FLOOR(RAND() * 250) + 150)),
    CONCAT('Processing notes: ', REPEAT('n', FLOOR(RAND() * 200) + 100)),
    CONCAT('Compliance check: ', ELT(FLOOR(RAND() * 4) + 1, 'KYC_passed', 'AML_cleared', 'sanctions_checked', 'risk_assessed'),
           ' data: ', REPEAT('c', FLOOR(RAND() * 300) + 200)),
    CONCAT('Audit trail: user_id=', FLOOR(RAND() * 1000) + 1, ' action=', 
           ELT(FLOOR(RAND() * 4) + 1, 'create', 'update', 'approve', 'reject'),
           ' timestamp=', NOW(), ' details=', REPEAT('t', FLOOR(RAND() * 250) + 150)),
    DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 86400) SECOND)
FROM 
    (SELECT transaction_id FROM transaction_records ORDER BY RAND() LIMIT $batch_size) t;
"

    # Execute with error handling
    if ! mysql --login-path="${login_path}" "$DB_NAME" -e "$sql_query"; then
        echo "ERROR: Failed to insert records into error_logs"
        return 1
    fi
    
    echo "Successfully inserted $batch_size records into error_logs"
}

# Function to insert data into error_logs
insert_error_logs() {
    local batch_size=$1
    echo "Inserting $batch_size records into error_logs..."
    
    # First test the connection
    if ! mysql --login-path="${login_path}" -e "USE $DB_NAME" 2>/dev/null; then
        echo "ERROR: Failed to connect to database or database doesn't exist"
        return 1
    fi
    
    # Store SQL in a variable for better readability
    local sql_query="
INSERT INTO error_logs 
(application_name, error_level, error_code, error_message, stack_trace, request_data, response_data, user_context, system_context, environment_data, correlation_id, last_update)
SELECT 
    ELT(FLOOR(RAND() * 8) + 1, 'web-frontend', 'api-gateway', 'user-service', 'payment-service', 'notification-service', 'auth-service', 'data-processor', 'backup-service'),
    ELT(FLOOR(RAND() * 5) + 1, 'ERROR', 'WARN', 'INFO', 'DEBUG', 'FATAL'),
    CONCAT('ERR_', LPAD(FLOOR(RAND() * 9999) + 1, 4, '0')),
    CONCAT('Error occurred in ', 
           ELT(FLOOR(RAND() * 10) + 1, 'database connection', 'API call', 'file processing', 'authentication', 'authorization', 'data validation', 'network communication', 'memory allocation', 'disk operation', 'external service'),
           ': ', REPEAT('e', FLOOR(RAND() * 200) + 100)),
    CONCAT('Stack trace:\n',
           'at com.example.service.', ELT(FLOOR(RAND() * 5) + 1, 'UserService', 'PaymentService', 'AuthService', 'DataService', 'NotificationService'),
           '.', ELT(FLOOR(RAND() * 5) + 1, 'processRequest', 'handlePayment', 'validateUser', 'saveData', 'sendNotification'),
           '(line ', FLOOR(RAND() * 500) + 1, ')\n',
           REPEAT('Stack trace line with detailed information about the error context and method calls\n', FLOOR(RAND() * 10) + 5)),
    CONCAT('Request: {\"method\":\"', ELT(FLOOR(RAND() * 4) + 1, 'GET', 'POST', 'PUT', 'DELETE'), 
           '\",\"url\":\"', ELT(FLOOR(RAND() * 5) + 1, '/api/users', '/api/payments', '/api/orders', '/api/auth', '/api/data'),
           '\",\"headers\":{\"content-type\":\"application/json\"},\"body\":\"', REPEAT('r', FLOOR(RAND() * 300) + 200), '\"}'),
    CONCAT('Response: {\"status\":', ELT(FLOOR(RAND() * 5) + 1, '500', '400', '401', '403', '404'),
           ',\"message\":\"', ELT(FLOOR(RAND() * 5) + 1, 'Internal server error', 'Bad request', 'Unauthorized', 'Forbidden', 'Not found'),
           '\",\"data\":\"', REPEAT('s', FLOOR(RAND() * 200) + 150), '\"}'),
    CONCAT('User context: user_id=', FLOOR(RAND() * 10000) + 1, ' session_id=', SUBSTRING(MD5(RAND()), 1, 32),
           ' role=', ELT(FLOOR(RAND() * 4) + 1, 'admin', 'user', 'guest', 'moderator'),
           ' additional_context=', REPEAT('u', FLOOR(RAND() * 150) + 100)),
    CONCAT('System context: server=', ELT(FLOOR(RAND() * 3) + 1, 'web-01', 'api-02', 'db-03'),
           ' memory_usage=', ROUND(RAND() * 100, 2), '% cpu_usage=', ROUND(RAND() * 100, 2), '%',
           ' context_data=', REPEAT('y', FLOOR(RAND() * 200) + 150)),
    CONCAT('Environment: ', ELT(FLOOR(RAND() * 3) + 1, 'production', 'staging', 'development'),
           ' version=', FLOOR(RAND() * 10) + 1, '.', FLOOR(RAND() * 10), '.', FLOOR(RAND() * 100),
           ' config_data=', REPEAT('v', FLOOR(RAND() * 250) + 200)),
    CONCAT('CORR_', UPPER(SUBSTRING(MD5(RAND()), 1, 20))),
    DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 86400) SECOND)
FROM 
    (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10) t1,
    (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10) t2,
    (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10) t3
LIMIT $batch_size;"
    
    # Execute with error handling
    if ! mysql --login-path="${login_path}" "$DB_NAME" -e "$sql_query"; then
        echo "ERROR: Failed to insert records into error_logs"
        return 1
    fi
    
    echo "Successfully inserted $batch_size records into error_logs"
}
# Function to insert data into api_call_logs
insert_api_call_logs() {
    local batch_size=$1
    echo "Inserting $batch_size records into api_call_logs..."
    
    # First test the connection
    if ! mysql --login-path="${login_path}" -e "USE $DB_NAME" 2>/dev/null; then
        echo "ERROR: Failed to connect to database or database doesn't exist"
        return 1
    fi
    
    # Store SQL in a variable for better readability and escaping
    local sql_query="
INSERT INTO api_call_logs 
(api_key, endpoint, method, request_headers, request_body, response_headers, response_body, status_code, response_time_ms, request_size_bytes, response_size_bytes, client_ip, user_agent, rate_limit_remaining, last_update)
SELECT 
    CONCAT('api_', SUBSTRING(MD5(RAND()), 1, 32)),
    CONCAT('/api/v', FLOOR(RAND() * 3) + 1, '/', 
           ELT(FLOOR(RAND() * 10) + 1, 'users', 'orders', 'products', 'payments', 'auth', 'data', 'reports', 'analytics', 'notifications', 'settings'),
           '/', FLOOR(RAND() * 1000) + 1),
    ELT(FLOOR(RAND() * 5) + 1, 'GET', 'POST', 'PUT', 'DELETE', 'PATCH'),
    CONCAT('{\"Content-Type\":\"application/json\",\"Authorization\":\"Bearer token123\",\"X-API-Key\":\"', SUBSTRING(MD5(RAND()), 1, 16), 
           '\",\"User-Agent\":\"API-Client/1.0\",\"Accept\":\"application/json\",\"X-Request-ID\":\"', SUBSTRING(MD5(RAND()), 1, 20), '\"}'),
    CASE 
        WHEN RAND() < 0.3 THEN NULL
        ELSE CONCAT('{\"data\":{\"id\":', FLOOR(RAND() * 10000) + 1, ',\"type\":\"', 
                   ELT(FLOOR(RAND() * 5) + 1, 'user', 'order', 'product', 'payment', 'report'), 
                   '\",\"attributes\":', REPEAT('\"', FLOOR(RAND() * 500) + 200), '}}')
    END,
    CONCAT('{\"Server\":\"nginx/1.18.0\",\"Content-Type\":\"application/json\",\"X-Response-Time\":\"', FLOOR(RAND() * 1000) + 10, 'ms\"',
           ',\"X-Rate-Limit-Remaining\":\"', FLOOR(RAND() * 1000) + 1, '\",\"Cache-Control\":\"no-cache\"}'),
    CONCAT('{\"success\":true,\"data\":{\"result\":\"', ELT(FLOOR(RAND() * 3) + 1, 'success', 'partial', 'pending'), 
           '\",\"message\":\"Operation completed successfully\",\"details\":\"', REPEAT('d', FLOOR(RAND() * 400) + 300), '\"}}'),
    ELT(FLOOR(RAND() * 8) + 1, 200, 201, 400, 401, 403, 404, 500, 503),
    FLOOR(RAND() * 2000) + 50,
    FLOOR(RAND() * 10000) + 100,
    FLOOR(RAND() * 50000) + 500,
    CONCAT(FLOOR(RAND() * 255) + 1, '.', FLOOR(RAND() * 255) + 1, '.', FLOOR(RAND() * 255) + 1, '.', FLOOR(RAND() * 255) + 1),
    CONCAT('API-Client/', FLOOR(RAND() * 5) + 1, '.', FLOOR(RAND() * 10), ' (', 
           ELT(FLOOR(RAND() * 3) + 1, 'Linux', 'Windows', 'Darwin'), ') RequestID/', SUBSTRING(MD5(RAND()), 1, 8)),
    FLOOR(RAND() * 1000) + 1,
    DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 86400) SECOND)
FROM 
    (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10) t1,
    (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10) t2,
    (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10) t3
LIMIT $batch_size;"
    
    # Execute with error handling
    if ! mysql --login-path="${login_path}" "$DB_NAME" -e "$sql_query"; then
        echo "ERROR: Failed to insert records into api_call_logs"
        return 1
    fi
    
    echo "Successfully inserted $batch_size records into api_call_logs"
}

# Function to insert data into processing_queue
insert_processing_queue() {
    local batch_size=$1
    echo "Inserting $batch_size records into processing_queue..."
    
# First test the connection
    if ! mysql --login-path="${login_path}" -e "USE $DB_NAME" 2>/dev/null; then
        echo "ERROR: Failed to connect to database or database doesn't exist"
        return 1
    fi
    
    # Store SQL in a variable for better readability and escaping
    local sql_query="
    INSERT INTO processing_queue 
(job_id, job_type, priority, status, input_data, output_data, error_data, processing_metadata, configuration_data, performance_stats, retry_count, max_retries, last_update, started_at, completed_at)
SELECT 
    CONCAT('JOB_', UPPER(SUBSTRING(MD5(RAND()), 1, 16))),
    ELT(FLOOR(RAND() * 8) + 1, 'data_export', 'report_generation', 'backup', 'cleanup', 'analysis', 'transformation', 'validation', 'notification'),
    FLOOR(RAND() * 10) + 1,
    ELT(FLOOR(RAND() * 5) + 1, 'pending', 'processing', 'completed', 'failed', 'cancelled'),
    CONCAT('{"source":"', ELT(FLOOR(RAND() * 5) + 1, 'database', 'file', 'api', 'stream', 'queue'), 
           '","format":"', ELT(FLOOR(RAND() * 4) + 1, 'json', 'csv', 'xml', 'binary'),
           '","parameters":{"start_date":"', DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY), 
           '","end_date":"', NOW(), '","filters":["', ELT(FLOOR(RAND() * 5) + 1, 'active', 'processed', 'pending', 'failed', 'all'), 
           '"],"options":{"compress":true,"encrypt":false}},"payload":"', REPEAT('i', FLOOR(RAND() * 1000) + 800), '"}'),
    CASE 
        WHEN RAND() < 0.4 THEN NULL
        ELSE CONCAT('{"result":"', ELT(FLOOR(RAND() * 3) + 1, 'success', 'partial', 'warning'), 
                   '","records_processed":', FLOOR(RAND() * 100000) + 1000,
                   ',"output_size":', FLOOR(RAND() * 1000000) + 10000,
                   ',"summary":"', REPEAT('o', FLOOR(RAND() * 300) + 200), '"}')
    END,
    CASE 
        WHEN RAND() < 0.7 THEN NULL
        ELSE CONCAT('{"error_code":"', ELT(FLOOR(RAND() * 5) + 1, 'TIMEOUT', 'MEMORY_LIMIT', 'DISK_FULL', 'NETWORK_ERROR', 'VALIDATION_ERROR'), 
                   '","error_message":"', REPEAT('e', FLOOR(RAND() * 200) + 100), '"}')
    END,
    CONCAT('{"worker_id":"', ELT(FLOOR(RAND() * 5) + 1, 'worker-01', 'worker-02', 'worker-03', 'worker-04', 'worker-05'), 
           '","queue_name":"', ELT(FLOOR(RAND() * 3) + 1, 'high_priority', 'normal', 'low_priority'),
           '","estimated_duration":', FLOOR(RAND() * 3600) + 60,
           ',"actual_duration":', FLOOR(RAND() * 3600) + 60,
           ',"metadata":"', REPEAT('m', FLOOR(RAND() * 150) + 100), '"}'),
    CONCAT('{"batch_size":', FLOOR(RAND() * 1000) + 100, ',"timeout":', FLOOR(RAND() * 3600) + 300,
           ',"memory_limit":"', FLOOR(RAND() * 8) + 1, 'GB","cpu_limit":',  FLOOR(RAND() * 8) + 1,
           ',"environment":"', ELT(FLOOR(RAND() * 3) + 1, 'production', 'staging', 'development'),
           '","config_data":"', REPEAT('c', FLOOR(RAND() * 200) + 150), '"}'),
    CONCAT('{"start_time":"', NOW(), '","end_time":"', DATE_ADD(NOW(), INTERVAL FLOOR(RAND() * 3600) SECOND),
           '","peak_memory_mb":', FLOOR(RAND() * 2048) + 512,
           ',"avg_cpu_percent":', ROUND(RAND() * 100, 2),
           ',"io_operations":', FLOOR(RAND() * 10000) + 1000,
           ',"performance_notes":"', REPEAT('p', FLOOR(RAND() * 200) + 100), '"}'),
    FLOOR(RAND() * 3),
    3,
    DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 86400) SECOND),
    CASE 
        WHEN RAND() < 0.3 THEN NULL
        ELSE DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 3600) SECOND)
    END,
    CASE 
        WHEN RAND() < 0.5 THEN NULL
        ELSE DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 1800) SECOND)
    END
FROM 
    (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10) t1,
    (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10) t2,
    (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10) t3
LIMIT $batch_size;
"
    
    # Execute with error handling
    if ! mysql --login-path="${login_path}" "$DB_NAME" -e "$sql_query"; then
        echo "ERROR: Failed to insert records into processing_queue"
        return 1
    fi
    
    echo "Successfully inserted $batch_size records into processing_queue"
}

# Main execution function
main() {
    local start_time=$(date +%s)
    
    # Check database connectivity
    if ! mysql --login-path="${login_path}" --skip-column-names --batch  -e "USE $DB_NAME;" 2>/dev/null; then
        echo "ERROR: Cannot connect to database. Please check credentials and database existence."
        exit 1
    fi
    
    echo "Database connection successful. Starting data insertion..."
    
    # Execute insertions for multiple iterations
    for ((i=1; i<=ITERATIONS; i++)); do
        echo "--- Iteration $i/$ITERATIONS ---"
        
        # Insert data into all tables with varying batch sizes to simulate real-world patterns
        insert_user_activity_logs $((BATCH_SIZE * 3))          # Highest volume
        insert_performance_metrics $((BATCH_SIZE * 2))         # High volume
        insert_api_call_logs $((BATCH_SIZE * 2))              # High volume
        insert_transaction_records $BATCH_SIZE                 # Medium volume
        insert_transaction_details $((BATCH_SIZE * 2))         # Child records (2x parent)
        insert_error_logs $((BATCH_SIZE / 2))                 # Lower volume but large records
        insert_processing_queue $((BATCH_SIZE / 4))           # Lowest volume but largest records
        
        echo "Iteration $i completed successfully"
        
        # Brief pause between iterations to avoid overwhelming the system
        sleep 2
    done
    
    # Display summary statistics
    echo "========================================="
    echo "Data insertion completed at $(date)"
    echo "========================================="
    
    # First test the connection
    if ! mysql --login-path="${login_path}" -e "USE $DB_NAME" 2>/dev/null; then
        echo "ERROR: Failed to connect to database or database doesn't exist"
        return 1
    fi
    
    # Store SQL in a variable for better readability and escaping
    local sql_query="
SELECT 
    'user_activity_logs' as table_name, 
    COUNT(*) as record_count,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb
FROM information_schema.tables t
JOIN (SELECT COUNT(*) as cnt FROM user_activity_logs) c
WHERE t.table_schema = '$DB_NAME' AND t.table_name = 'user_activity_logs'
UNION ALL
SELECT 
    'performance_metrics' as table_name,
    COUNT(*) as record_count,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb
FROM information_schema.tables t
JOIN (SELECT COUNT(*) as cnt FROM performance_metrics) c
WHERE t.table_schema = '$DB_NAME' AND t.table_name = 'performance_metrics'
UNION ALL
SELECT 
    'transaction_records' as table_name,
    COUNT(*) as record_count,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb
FROM information_schema.tables t
JOIN (SELECT COUNT(*) as cnt FROM transaction_records) c
WHERE t.table_schema = '$DB_NAME' AND t.table_name = 'transaction_records'
UNION ALL
SELECT 
    'transaction_details' as table_name,
    COUNT(*) as record_count,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb
FROM information_schema.tables t
JOIN (SELECT COUNT(*) as cnt FROM transaction_details) c
WHERE t.table_schema = '$DB_NAME' AND t.table_name = 'transaction_details'
UNION ALL
SELECT 
    'error_logs' as table_name,
    COUNT(*) as record_count,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb
FROM information_schema.tables t
JOIN (SELECT COUNT(*) as cnt FROM error_logs) c
WHERE t.table_schema = '$DB_NAME' AND t.table_name = 'error_logs'
UNION ALL
SELECT 
    'api_call_logs' as table_name,
    COUNT(*) as record_count,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb
FROM information_schema.tables t
JOIN (SELECT COUNT(*) as cnt FROM api_call_logs) c
WHERE t.table_schema = '$DB_NAME' AND t.table_name = 'api_call_logs'
UNION ALL
SELECT 
    'processing_queue' as table_name,
    COUNT(*) as record_count,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb
FROM information_schema.tables t
JOIN (SELECT COUNT(*) as cnt FROM processing_queue) c
WHERE t.table_schema = '$DB_NAME' AND t.table_name = 'processing_queue';

SELECT 
    'TOTAL' as table_name,
    SUM(table_rows) as record_count,
    ROUND(SUM((data_length + index_length) / 1024 / 1024), 2) as size_mb
FROM information_schema.tables 
WHERE table_schema = '$DB_NAME';
"
    # Execute with error handling
    if ! mysql --login-path="${login_path}" "$DB_NAME" -e "$sql_query"; then
        echo "ERROR: Failed to fetch statistics"
        return 1
    fi
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    echo "Total execution time: ${duration} seconds"
    echo "Log file: $LOG_FILE"
}

# Handle script arguments and execution
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [batch_size] [iterations]"
        echo "  batch_size: Number of records per batch (default: 1000)"
        echo "  iterations: Number of iterations to run (default: 10)"
        echo "  Examples:"
        echo "    $0 2000 5    # Insert 2000 records per batch, 5 iterations"
        echo "    $0 500 20    # Insert 500 records per batch, 20 iterations"
        echo "    $0           # Use defaults (1000 records, 10 iterations)"
        exit 0
        ;;
    --status)
        echo "Checking database status..."
        # mysql --login-path="${login_path}" <<EOF
#  sql_query="
# SELECT 
#     table_name,
#     table_rows,
#     ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb
# FROM information_schema.tables 
# WHERE table_schema = '$DB_NAME'
# ORDER BY (data_length + index_length) DESC;
# "

sql_query="
SELECT 
    'user_activity_logs' as table_name, 
    COUNT(*) as record_count,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb
FROM information_schema.tables t
JOIN (SELECT COUNT(*) as cnt FROM user_activity_logs) c
WHERE t.table_schema = '$DB_NAME' AND t.table_name = 'user_activity_logs'
UNION ALL
SELECT 
    'performance_metrics' as table_name,
    COUNT(*) as record_count,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb
FROM information_schema.tables t
JOIN (SELECT COUNT(*) as cnt FROM performance_metrics) c
WHERE t.table_schema = '$DB_NAME' AND t.table_name = 'performance_metrics'
UNION ALL
SELECT 
    'transaction_records' as table_name,
    COUNT(*) as record_count,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb
FROM information_schema.tables t
JOIN (SELECT COUNT(*) as cnt FROM transaction_records) c
WHERE t.table_schema = '$DB_NAME' AND t.table_name = 'transaction_records'
UNION ALL
SELECT 
    'transaction_details' as table_name,
    COUNT(*) as record_count,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb
FROM information_schema.tables t
JOIN (SELECT COUNT(*) as cnt FROM transaction_details) c
WHERE t.table_schema = '$DB_NAME' AND t.table_name = 'transaction_details'
UNION ALL
SELECT 
    'error_logs' as table_name,
    COUNT(*) as record_count,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb
FROM information_schema.tables t
JOIN (SELECT COUNT(*) as cnt FROM error_logs) c
WHERE t.table_schema = '$DB_NAME' AND t.table_name = 'error_logs'
UNION ALL
SELECT 
    'api_call_logs' as table_name,
    COUNT(*) as record_count,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb
FROM information_schema.tables t
JOIN (SELECT COUNT(*) as cnt FROM api_call_logs) c
WHERE t.table_schema = '$DB_NAME' AND t.table_name = 'api_call_logs'
UNION ALL
SELECT 
    'processing_queue' as table_name,
    COUNT(*) as record_count,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb
FROM information_schema.tables t
JOIN (SELECT COUNT(*) as cnt FROM processing_queue) c
WHERE t.table_schema = '$DB_NAME' AND t.table_name = 'processing_queue';

SELECT 
    'TOTAL' as table_name,
    SUM(table_rows) as record_count,
    ROUND(SUM((data_length + index_length) / 1024 / 1024), 2) as size_mb
FROM information_schema.tables 
WHERE table_schema = '$DB_NAME';
"
if ! mysql --login-path="${login_path}" "$DB_NAME" -e "$sql_query"; then
    echo "ERROR: Failed to fetch statistics"
    return 1
fi

        exit 0
        ;;
    *)
        main
        ;;
esac