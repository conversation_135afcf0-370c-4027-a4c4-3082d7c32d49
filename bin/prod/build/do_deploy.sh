#!/bin/bash


#maks script get argument to select which image to build and deploy
if [ $# -eq 0 ]; then
    echo "Usage: $0 <backend|frontend>"
    exit 1
fi

if [ $1 = "backend" ]; then
    echo "Building and deploying backend image"
    #build backend image
    # cd ./elysium-backend
    echo "Current directory: $(pwd)"
    
    docker build -t elysium-backend:latest -f bin/prod/Dockerfile.backend .

    # Tag the image with the registry name
    docker tag elysium-backend:latest registry.digitalocean.com/elysium-dir/elysium-backend:latest

    # Log in to the DigitalOcean Container Registry
    #docker login registry.digitalocean.com -u ahmed_dir -p ***********************************************************************

    # Push the image to the registry
    docker push registry.digitalocean.com/elysium-dir/elysium-backend:latest

    echo "Backend image pushed to DigitalOcean Container Registry"

elif [ $1 = "frontend" ]; then
    echo "Building and deploying frontend image"
    #build frontend image

    cd ./elysium-frontend
    # build recat app for production
    echo "Current directory: $(pwd)"
    #build react app
    echo "Building react app"
    npm run build:prod
    
    echo "React app build complete"

    echo "Building frontend docker image"
    echo "Current directory: $(pwd)"
    cd ../
    echo "Current directory: $(pwd)"
    #build docker image
    docker build -t elysium-frontend:latest -f bin/prod/Dockerfile.frontend .

    # Tag the image with the registry name
    docker tag elysium-frontend:latest registry.digitalocean.com/elysium-dir/elysium-frontend:latest

    # Push the image to the registry
    docker push registry.digitalocean.com/elysium-dir/elysium-frontend:latest

    echo "Frontend image pushed to DigitalOcean Container Registry"

    echo "Frontend deployed"
fi
