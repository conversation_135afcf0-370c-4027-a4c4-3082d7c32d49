#!/bin/bash

# Install OpenSSL if not already installed
apt-get update
apt-get install -y openssl

# Create directory for certificates
mkdir -p /etc/nginx/ssl

# Generate a strong private key
openssl genrsa -out /etc/nginx/ssl/nginx.key 4096

# Generate a self-signed certificate valid for 10 years
openssl req -x509 -new -nodes -key /etc/nginx/ssl/nginx.key -sha256 -days 3650 \
  -out /etc/nginx/ssl/nginx.crt \
  -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"

# Set proper permissions
chmod 400 /etc/nginx/ssl/nginx.key
chmod 444 /etc/nginx/ssl/nginx.crt

# Check for existing SSL parameters file and remove it
if [ -f /etc/nginx/conf.d/ssl-params.conf ]; then
  rm /etc/nginx/conf.d/ssl-params.conf
fi

# Update Nginx configuration to use SSL with all parameters in one place
cat > /etc/nginx/sites-available/elysium-frontend <<EOF
server {
    listen 80;
    server_name _;
    
    # Redirect all HTTP traffic to HTTPS
    return 301 https://\$host\$request_uri;
}

server {
    listen 443 ssl;
    server_name _;
    
    # SSL configuration
    ssl_certificate /etc/nginx/ssl/nginx.crt;
    ssl_certificate_key /etc/nginx/ssl/nginx.key;
    
    # SSL parameters
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 1d;
    ssl_session_tickets off;
    ssl_buffer_size 4k;
    
    # DNS resolver for backend name resolution
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options "nosniff";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'; connect-src 'self'";
    
    # DDoS protection
    client_body_timeout 10s;
    client_header_timeout 10s;
    limit_req_zone \$binary_remote_addr zone=one:10m rate=1r/s;
    limit_req zone=one burst=10 nodelay;
    
    # Proxy settings
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
    
    # Deny access to hidden files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
}
EOF

# Enable the Nginx site
ln -sf /etc/nginx/sites-available/elysium-frontend /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# Test Nginx configuration
nginx -t

# Reload Nginx to apply SSL changes
systemctl reload nginx

# Configure firewall to allow HTTPS
if command -v ufw &> /dev/null; then
    ufw allow https
    ufw status
fi

# Output completion message
echo "Self-signed SSL setup complete!"
echo "Your site is now accessible via HTTPS."
echo "Note: Browsers will show a security warning because the certificate is self-signed."
echo "For production use, consider using Let's Encrypt or a commercial SSL certificate."