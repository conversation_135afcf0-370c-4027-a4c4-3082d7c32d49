#!/bin/bash

# Update system packages
apt-get update

# Install Nginx
apt-get install -y nginx

# Install Docker dependencies
apt-get install -y apt-transport-https ca-certificates curl software-properties-common

# Add Docker's official GPG key
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | apt-key add -

# Add Docker repository
add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable"

# Update package database with Docker packages
apt-get update

# Install Docker
apt-get install -y docker-ce docker-ce-cli containerd.io

# Start and enable Docker service
systemctl start docker
systemctl enable docker

# Add current user to docker group
usermod -aG docker ubuntu

# Configure Nginx as reverse proxy for the Docker container
cat > /etc/nginx/sites-available/elysium-frontend <<EOF
server {
    listen 80;
    server_name _;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF

# Enable the Nginx site
ln -s /etc/nginx/sites-available/elysium-frontend /etc/nginx/sites-enabled/
rm /etc/nginx/sites-enabled/default

# Test Nginx configuration
nginx -t

# Restart Nginx
systemctl restart nginx

# Pull and run the frontend Docker image
docker pull registry.digitalocean.com/elysium-dir/elysium-frontend:latest
docker run -d --name elysium-frontend -p 3000:80 registry.digitalocean.com/elysium-dir/elysium-frontend:latest

# Set up auto-restart for the container
docker update --restart=always elysium-frontend

# Output completion message
echo "Server setup complete!"


# # Pull and run the backend Docker image
# docker pull registry.digitalocean.com/elysium-dir/elysium-backend:latest
# docker run -d --name elysium-backend -p 3000:80 registry.digitalocean.com/elysium-dir/elysium-backend:latest

# # Set up auto-restart for the container
# docker update --restart=always elysium-backend

# # Output completion message
# echo "Server setup complete!"


#Secutiry updates


# Install essential security packages
apt-get install -y ufw fail2ban unattended-upgrades apt-listchanges apticron logwatch

# Configure automatic security updates
cat > /etc/apt/apt.conf.d/50unattended-upgrades <<EOF
Unattended-Upgrade::Allowed-Origins {
    "\${distro_id}:\${distro_codename}";
    "\${distro_id}:\${distro_codename}-security";
    "\${distro_id}ESMApps:\${distro_codename}-apps-security";
    "\${distro_id}ESM:\${distro_codename}-infra-security";
};
Unattended-Upgrade::Remove-Unused-Kernel-Packages "true";
Unattended-Upgrade::Remove-Unused-Dependencies "true";
Unattended-Upgrade::Automatic-Reboot "true";
Unattended-Upgrade::Automatic-Reboot-Time "02:00";
EOF

cat > /etc/apt/apt.conf.d/20auto-upgrades <<EOF
APT::Periodic::Update-Package-Lists "1";
APT::Periodic::Download-Upgradeable-Packages "1";
APT::Periodic::AutocleanInterval "7";
APT::Periodic::Unattended-Upgrade "1";
EOF

# Configure fail2ban for SSH protection
cat > /etc/fail2ban/jail.local <<EOF
[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3
bantime = 3600
EOF

# Configure and start fail2ban
systemctl enable fail2ban
systemctl start fail2ban

# Configure firewall
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow http
ufw allow https
ufw enable


# Install Nginx with security modules
apt-get install -y nginx-extras