APP_NAME=Elysium
APP_ENV=development
APP_KEY=base64:b6Gko9f0aH/OCppMOyyHNa4flwJJQJ8sAeA1cZct24I=
APP_DEBUG=true
APP_URL=http://localhost
BASE_URL=https://api-dev.elysium-io.com/

LOG_CHANNEL=daily
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=private-elysium-db-mysql-blr1-78741-do-user-13005178-0.c.db.ondigitalocean.com
DB_PORT=25060
DB_DATABASE=elysium
DB_USERNAME=svc_elysium_app
DB_PASSWORD=gkv3b4X66vd9dCVZL_HM
#DB_HOST=localhost
#DB_PORT=3306
#DB_DATABASE=defaultdb
#DB_USERNAME=sakila
#DB_PASSWORD=S@kila123

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_DRIVER=smtp
MAIL_HOST=smtp.mailgun.org
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=B.KnZfj@!i_22!*u
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

SUPPORT_EMAIL="<EMAIL>"

SESSION_DOMAIN=localhost
SANCTUM_STATEFUL_DOMAINS=localhost

STRIPE_KEY=pk_test_51NNfDWER0DocJpyMLYFypxBTNPkyvnWHzIX0HMqoqLccRkTKjVzlWXu0fOEj9z6tkuleetBtF1wrS7jXHdddTAC000X2NwpZHR
STRIPE_SECRET=sk_test_51NNfDWER0DocJpyMLqwfHcIVGejpU0OsqPoTFudJPZtzB4ZGSY93LwDt1A6KwR6CZY3a9zEKYiPlQMrlXBmEHqij00NdGr6Bho