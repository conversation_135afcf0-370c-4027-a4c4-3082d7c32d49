#!/bin/bash

# Check if domain name is provided
if [ -z "$1" ]; then
  echo "Usage: $0 yourdomain.com [<EMAIL>]"
  exit 1
fi

DOMAIN=$1
EMAIL=${2:-"admin@$DOMAIN"}

# Install Certbot and Nginx plugin
apt-get update
apt-get install -y certbot python3-certbot-nginx

# Ensure Nginx is running
systemctl start nginx
systemctl enable nginx

# Update Nginx configuration to include server_name
sed -i "s/server_name _;/server_name $DOMAIN;/" /etc/nginx/sites-available/elysium-frontend

# Reload Nginx to apply the server_name change
systemctl reload nginx

# Obtain SSL certificate
certbot --nginx -d $DOMAIN --non-interactive --agree-tos -m $EMAIL --redirect

# Test Nginx configuration
nginx -t

# Reload Nginx to apply SSL changes
systemctl reload nginx

# Set up auto-renewal
echo "0 3 * * * certbot renew --quiet --nginx" | crontab -

# Configure stronger SSL parameters
cat > /etc/nginx/conf.d/ssl-params.conf <<EOF
# SSL parameters
ssl_protocols TLSv1.2 TLSv1.3;
ssl_prefer_server_ciphers on;
ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 1d;
ssl_session_tickets off;
ssl_stapling on;
ssl_stapling_verify on;
resolver ******* ******* valid=300s;
resolver_timeout 5s;
add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
add_header X-Frame-Options SAMEORIGIN;
add_header X-Content-Type-Options nosniff;
add_header X-XSS-Protection "1; mode=block";
EOF

# Test Nginx configuration again
nginx -t

# Reload Nginx to apply SSL parameters
systemctl reload nginx

# Output completion message
echo "SSL setup complete for $DOMAIN!"
echo "Your site is now accessible via HTTPS."
echo "Certificate will auto-renew via cron job."