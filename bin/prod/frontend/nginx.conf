#server {
#    listen 80;
#    server_name _;

    # Redirect all HTTP traffic to HTTPS
#    return 301 https://$host$request_uri;
#}

server {
    #listen 443 ssl;
    listen 80;
    server_name _;

    # SSL configuration
#    ssl_certificate /etc/nginx/ssl/nginx.crt;
#    ssl_certificate_key /etc/nginx/ssl/nginx.key;

    # SSL parameters
#    ssl_protocols TLSv1.2 TLSv1.3;
#    ssl_prefer_server_ciphers on;
#    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM>
#    ssl_session_cache shared:SSL:10m;
#    ssl_session_timeout 1d;
#    ssl_session_tickets off;
#    ssl_buffer_size 4k;

    # DNS resolver for backend name resolution
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;

    # Security headers
 #   add_header X-Frame-Options "SAMEORIGIN";
 #   add_header X-XSS-Protection "1; mode=block";
 #   add_header X-Content-Type-Options "nosniff";
 #   add_header Referrer-Policy "strict-origin-when-cross-origin";
 #   add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'; connect-src 'self'";

    # DDoS protection
    client_body_timeout 10s;
    client_header_timeout 10s;
    #limit_req_zone $binary_remote_addr zone=one:10m rate=1r/s;
    #limit_req zone=one burst=10 nodelay;

    # Proxy settings
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    location /api {
        proxy_pass http://elysium-backend/api;
        # For requests to /products, forward the request to the product_service upstream.
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        # Set various headers to forward to the upstream server.
    }

    location /agent-api {
        proxy_pass http://elysium-backend;
        # For requests to /products, forward the request to the product_service upstream.
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        # Set various headers to forward to the upstream server.
    }

    location /adminer {
        proxy_pass http://elysium-backend:8181;
        # For requests to /products, forward the request to the product_service upstream.
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        # Set various headers to forward to the upstream server.
    }
    # Deny access to hidden files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
}

