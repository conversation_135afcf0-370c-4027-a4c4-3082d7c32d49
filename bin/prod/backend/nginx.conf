server {
    listen 80;
    server_name localhost;
    root /var/www/public;

    index index.php index.html;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        # Critical fix: Use $document_root instead of hardcoded path
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_pass app:9000;
        include fastcgi_params;
        
        # Optional but recommended for debugging
        fastcgi_param PATH_INFO $fastcgi_path_info;
        fastcgi_param HTTP_HOST $host;
    }

    location ~ /\.ht {
        deny all;
    }
}