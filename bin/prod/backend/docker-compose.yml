version: '3.8'

services:
  app:
    image: registry.digitalocean.com/elysium-dir/elysium-backend:latest
    container_name: elysium-backend
    volumes:
      - backend.env:/var/www/.env
    networks:
      - app-network
    #execute bash command after container starts
    # entrypoint: sh -c "cd /var/www && php artisan key:generate && php artisan optimize:clear && php artisan optimize && php artisan config:clear && php-fpm"

  nginx:
    image: nginx:alpine
    container_name: nginx
    ports:
      - 80:80
      - 443:443 # If you're handling SSL here
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      # - ./ssl:/etc/nginx/ssl # If you have SSL certificates
      #- app_public:/var/www/html:ro # Mount the app's public directory to Nginx's web root
    depends_on:
      - app
    networks:
      - app-network
      - public-network
  
  adminer:
    image: adminer:latest
    ports:
      - "8181:8080"
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
  public-network:

# volumes:
#   app_public: # Named volume to share the app's public directory