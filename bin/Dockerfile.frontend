FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy app files
COPY . .

# Set environment variables
ENV REACT_APP_API_URL=http://localhost:8000/api
ENV REACT_APP_API_IMAGE_URL=http://localhost:8000/storage
ENV REACT_APP_STRIPE_KEY=your_stripe_key_here

# Expose port 3000
EXPOSE 3000

# Start React app
CMD ["npm", "start"]