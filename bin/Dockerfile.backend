FROM php:8.3-fpm-alpine

# Install system dependencies, including development packages for gd dependencies
RUN apk update && apk add --no-cache \
    git \
    curl \
    zip \
    unzip \
    libzip-dev \
    icu-dev \
    oniguruma-dev \
    libxml2-dev \
    postgresql-dev \
    libpng-dev \
    libjpeg-turbo-dev \
    cron \
    supervisor \
    && docker-php-ext-install -j$(nproc) \
    pdo pdo_mysql zip intl bcmath xml gd mbstring soap

# Clear cache (using Alpine's cleanup method instead of apt-get)
RUN rm -rf /var/cache/apk/*

# Install PHP extensions
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd zip

# Get latest Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www/html

# Set permissions for the container
RUN chown -R www-data:www-data /var/www/html
RUN chmod -R 755 /var/www/html

# RUN composer update
RUN composer update
RUN composer install --no-dev --optimize-autoloader --no-interaction --no-progress --prefer-dist

# Generate application key (you might want to handle this differently in production)
RUN php artisan key:generate
RUN php artisan optimize:clear
RUN php artisan config:clear
RUN php artisan optimize

# Setup cron job
RUN echo "* * * * * cd /var/www/html && php artisan schedule:run >> /dev/null 2>&1" > /etc/crontabs/root

# Create supervisor configuration for cron
RUN mkdir -p /etc/supervisor.d/
COPY bin/prod/supervisord.conf /etc/supervisor.d/supervisord.conf

# Expose port 8000
EXPOSE 8000

# Start supervisor (which will manage both PHP and cron)
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor.d/supervisord.conf"]
