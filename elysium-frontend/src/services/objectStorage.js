import axios from "axios";
import { baseUrl } from "../config/constants";
import { handleCookies } from '../utils/cookies'
const { post } = axios;

const getToken = () => {
  return 'Bearer ' + handleCookies.fetchCookies();
};

const getAllCloudStorageTypes = async () => {
  try {
    const response = await post(`${baseUrl}/getAllCloudProviderNames`, {}, {
      headers: {
        Authorization: getToken(),
      },
    });

    return response.data || false;
  } catch (error) {
    return error.message;
  }
}

const getAllObjectStorageTypes = async (payload) => {
  try {
    const response = await post(`${baseUrl}/getObjectStorageTypes`, payload, {
      headers: {
        Authorization: getToken(),
      },
    });

    return response.data || false;
  } catch (error) {
    return error.message;
  }
}

const storeObjectStorage = async (payload) => {
  try {
    const response = await post(`${baseUrl}/storeClientObjectStorage`, payload, {
      headers: {
        Authorization: getToken(),
      },
    });

    return response.data || false;
  } catch (error) {
    return error.message;
  }
}

const getObjectStorage = async () => {
  try {
    const response = await post(`${baseUrl}/getClientObjectStorage`, {}, {
      headers: {
        Authorization: getToken(),
      },
    });

    return response.data || false;
  } catch (error) {
    return error.message;
  }
}

export const objectStorage = {
  getAllCloudStorageTypes,
  getAllObjectStorageTypes,
  storeObjectStorage,
  getObjectStorage
}