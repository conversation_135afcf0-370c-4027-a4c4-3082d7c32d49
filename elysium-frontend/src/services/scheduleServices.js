import axios from "axios";
import { baseUrl } from "../config/constants";
import { handleCookies } from "../utils/cookies";

const baseRequest = async (endpoint, data) => {
  try {
    const token = "Bearer " + (await handleCookies.fetchCookies());
    const response = await axios.post(baseUrl + endpoint, data, {
      withCredentials: true,
      headers: {
        Authorization: token,
      },
    });
    return response.data;
  } catch (error) {
    return error;
  }
};

export const serviceCall = {
  getServers: () => baseRequest("/schedule/serversListing"),
  getDatabases: (data) =>
    baseRequest("/schedule/databasesListingByServerId", data),
  getTables: (data) => baseRequest("/schedule/tablesListingByDatabaseId", data),
  AddTable: (data) => baseRequest("/schedule/add", data),
  getScheduleServerDbTableListing: (data) =>
    baseRequest("/schedule/listing", data),
  addScheduleTime: (data) => baseRequest("/schedule/setupArchiving", data),
  getScheduleTime: (data) => baseRequest("/schedule/getArchivingById", data),
  changeScheduleArchivingStatus: (data) =>
    baseRequest("/schedule/changeArchivingStatus", data),

  deleteScheduleTable: (data) => baseRequest("/schedule/remove", data),
  undoScheduleTable: (data) => baseRequest("/schedule/restore", data),

  deleteScheduleDatabase: (data) =>
    baseRequest("/schedule/removeDatabase", data),
  undoScheduleDatabase: (data) =>
    baseRequest("/schedule/databaseRestore", data),
};
