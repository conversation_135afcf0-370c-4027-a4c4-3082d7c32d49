import axios from "axios";
import { baseUrl } from "../config/constants";
import { handleCookies } from '../utils/cookies'
const { post } = axios;

const getToken = () => {
  return 'Bearer ' + handleCookies.fetchCookies();
};

const addDBServer = async (url, payload) => {
  try {
    const response = await post(`${baseUrl}/${url}`, payload, {
      headers: {
        Authorization: getToken(),
      },
    });

    return response.data || false;
  } catch (error) {
    return error.message;
  }
}

const getAllDBServerTypes = async () => {
  try {
    const response = await post(`${baseUrl}/getAllDBServerTypes`, {}, {
      headers: {
        Authorization: getToken(),
      },
    });

    return response.data || false;
  } catch (error) {
    return error.message;
  }
}

const getAllDBServer = async (selectedValue) => {
  try {
    let url = '';
    if (typeof selectedValue === 'undefined' || selectedValue === '0') {
      url = 'getAllDBServer';
    } else {
      url = `getAllDBServer?status_id=${selectedValue}`;
    }
    const response = await post(`${baseUrl}/${url}`, {}, {
      headers: {
        Authorization: getToken(),
      },
    })
    return response.data || false;
  } catch (error) {
    return error.message;
  }
}

const deleteDBServer = async (payload) => {
  try {
    const response = await post(`${baseUrl}/deleteDBServer`, payload, {
      headers: {
        Authorization: getToken(),
      },
    });
    return response.data;
  } catch (error) {
    return error.response.data;
  }
}

const deleteDBSchema = async (payload) => {
  try {
    const response = await post(`${baseUrl}/deleteDBSchema`, payload, {
      headers: {
        Authorization: getToken(),
      },
    });
    return response.data;
  } catch (error) {
    return error.response.data;
  }
}

const getAllServerDB = async (payload) => {
  try {
    const response = await post(`${baseUrl}/getAllDatabasesList`, payload, {
      headers: {
        Authorization: getToken(),
      },
    });
    return response.data || false;
  } catch (error) {
    return error.message;
  }
}

const getAllDatabasesTablesList = async (payload) => {
  try {
    const response = await post(`${baseUrl}/getAllDatabasesTablesList`, payload, {
      headers: {
        Authorization: getToken(),
      },
    });
    return response.data || false;
  } catch (error) {
    return error.message;
  }
}

const testDBServerConnection = async (id) => {

  try {
      const response = await post(`${baseUrl}/testDBServerConnection/${id}`, {}, {
        headers: {
          Authorization: getToken(),
        },
      });
      return response.data;
    } catch (error) {
      return error.response.data;
    }
};

const runDBNetworkDiagnostics = async (id) => {

  try {
      const response = await post(`${baseUrl}/runDBNetworkDiagnostics/${id}`, {}, {
        headers: {
          Authorization: getToken(),
        },
      });
      return response.data;
    } catch (error) {
      return error.response.data;
    }

  
};

export const dbServer = {
  addDBServer,
  getAllDBServerTypes,
  getAllDBServer,
  deleteDBServer,
  getAllServerDB,
  deleteDBSchema,
  getAllDatabasesTablesList,
  testDBServerConnection,
  runDBNetworkDiagnostics
}
