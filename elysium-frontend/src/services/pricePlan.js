
import axios from "axios";
import { baseUrl } from "../config/constants";
import { handleCookies } from '../utils/cookies'

const { post } = axios;

export const serviceCall = {
  postSubscription,
  getAllSubscriptions
}

async function postSubscription() {

  try {
    const token = 'Bearer ' + handleCookies.fetchCookies()
    const response = await post(`${baseUrl}/trialSubscription`, {}, {
      headers: {
        Authorization: token,
      },
    });

    return response.data || false;
  } catch (error) {
    console.error(error);
    return false;
  }
}

async function getAllSubscriptions(data) {
  const token = 'Bearer ' + handleCookies.fetchCookies();
  try {
    const response = await post(`${baseUrl}/filterSubscriptionPlans`, data, {
      withCredentials: true,
      headers: {
        Authorization: token,
      },
    });

    return response.data || false;
  } catch (error) {
    console.error(error);
    return false;
  }
}
