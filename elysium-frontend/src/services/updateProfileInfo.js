import axios from "axios";
import { baseUrl } from "../config/constants";
import { handleCookies } from '../utils/cookies'
const { post, get } = axios;

const getToken = () => {
  return 'Bearer ' + handleCookies.fetchCookies();
};

const userInfo = async () => {
  try {
    const response = await post(`${baseUrl}/auth/user`, {}, {
      withCredentials: true,
      headers: {
        Authorization: getToken(),
      },
    });

    return response.data || false;
  } catch (error) {
    return error.message;
  }
}

const getTimezone = async () => {
  try {
    const response = await get(`${baseUrl}/timezones`)
    return response.data
  } catch (error) {
    return error.message;
  }
}

const updateProfileData = async (url, data) => {
  try {
    const response = await post(`${baseUrl}${url}`, data, {
      headers: {
        Authorization: getToken(),
      },
    });
    return response.data;
  } catch (error) {
    console.error(error);
    return error.message;
  }
}

const updatePassword = async (url, data) => {
  try {
    const response = await post(`${baseUrl}${url}`, data, {
      headers: {
        Authorization: getToken(),
      },
    });
    return response.data;
  } catch (error) {
    return error.response.data.message;
  }
}

const userCurrentPlan = async () => {
  try {
    const response = await post(`${baseUrl}/getActiveSubscription`, {}, {
      withCredentials: true,
      headers: {
        Authorization: getToken(),
      },
    });
    return response.data;
  } catch (error) {
    return error.response.data.message;
  }
}

const userCardInfo = async () => {
  try {
    const response = await post(`${baseUrl}/getPaymentMethods`, {}, {
      withCredentials: true,
      headers: {
        Authorization: getToken(),
      },
    });
    return response.data;
  } catch (error) {
    return error.response.data.message;
  }
}

const getInvoices = async (url) => {
  try {
    const response = await post(`${baseUrl}/getInvoices`, {}, {
      withCredentials: true,
      headers: {
        Authorization: getToken(),
      },
    });
    return response.data;
  } catch (error) {
    return error.response.data.message;
  }
}

const upgradePlan = async (plan) => {
  try {
    const response = await post(`${baseUrl}/updateSubscriptionPlan`, { plan }, {
      withCredentials: true,
      headers: {
        Authorization: getToken(),
      },
    });
    return response.data;
  } catch (error) {
    return error.response.data.message;
  }
}

const cancelSubscription = async () => {
  try {
    const response = await post(`${baseUrl}/cancelSubscriptionPlan`, {}, {
      withCredentials: true,
      headers: {
        Authorization: getToken(),
      },
    });
    return response.data;
  } catch (error) {
    return error.response.data.message;
  }
}

const signOut = async () => {
  try {
    const response = await axios.post(`${baseUrl}/auth/logout`, {}, {
      withCredentials: true,
      headers: {
        Authorization: getToken(),
      },
    });
    return response.data;
  } catch (error) {
    return error;
  }
}

export const updateUserInfo = {
  userInfo,
  getTimezone,
  updateProfileData,
  updatePassword,
  userCurrentPlan,
  userCardInfo,
  getInvoices,
  upgradePlan,
  cancelSubscription,
  signOut
};