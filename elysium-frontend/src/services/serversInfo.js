import axios from "axios";
import { baseUrl } from "../config/constants";
import { handleCookies } from '../utils/cookies'
const { post } = axios;

const getToken = () => {
  return 'Bearer ' + handleCookies.fetchCookies();
};

// Agent Table Filter Api
const agentServerFilters = async (filter) => {
  try {
    const response = await post(`${baseUrl}/getRemoteServerFilters`, { filter }, {
      headers: {
        Authorization: getToken(),
      },
    });
    return response.data;
  } catch (error) {
    return error.response.data.message;
  }
}
// const agentSeverSearch = async (inpVal) => {
//   try {
//     const response = await post(`${baseUrl}/getRemoteServerFilters`, { inpVal }, {
//       headers: {
//         Authorization: token,
//       },
//     });
//     return response.data;
//   } catch (error) {
//     return error.response.data.message;
//   }
// }

const agentSeversData = async (selectedValue) => {
  try {
    let response = '';
    if (typeof selectedValue === 'undefined' || selectedValue === '0') {
      response = await post(`${baseUrl}/getAllRemoteServer`, {}, {
        headers: {
          Authorization: getToken(),
        },
      })
    }
    else {
      response = await post(`${baseUrl}/getAllRemoteServer?status_id=${selectedValue}`, {}, {
        headers: {
          Authorization: getToken(),
        },
      })
    }
    return response.data;
  } catch (error) {
    return error.response.data.message;
  }
}

const addServer = async (url, payload) => {
  try {
    const response = await post(`${baseUrl}/${url}`, payload, {
      headers: {
        Authorization: getToken(),
      },
    });
    return response.data;
  } catch (error) {
    return error.response.data.message;
  }
}

const updateAgent = async (url, payload) => {
  try {
    const response = await post(`${baseUrl}/${url}`, payload, {
      headers: {
        Authorization: getToken(),
      },
    });
    return response.data;
  } catch (error) {
    return error.response.data.message;
  }
}
const deleteAgent = async (payload) => {
  try {
    const response = await post(`${baseUrl}/deleteRemoteServer`, payload, {
      headers: {
        Authorization: getToken(),
      },
    });
    return response.data;
  } catch (error) {
    return error.response.data;
  }
}

const testAgentConnection = async (agentId) => {
  try {
    const response = await post(`${baseUrl}/testRemoteServerConnection/${agentId}`, {}, {
      headers: {
        Authorization: getToken(),
      },
    });
    return response.data;
  } catch (error) {
    return error.response.data;
  }
}

export const serverInfo = {
  agentSeversData,
  agentServerFilters,
  addServer,
  updateAgent,
  deleteAgent,
  testAgentConnection
}
