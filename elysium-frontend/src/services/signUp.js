import axios from "axios";
import { baseUrl } from "../config/constants";
const { post, get } = axios;

export const serviceCall = {
  signUp,
  getStates,
  getCountry,
  getDatabaseTables,
  getDatabaseServer
};

async function signUp(url, data) {
  try {
    const response = await post(`${baseUrl}${url}`, data);
    return response.data;
  } catch (error) {
    console.error(error);
    return false
  }
}

async function getStates() {
  try {
    const response = await get(`${baseUrl}/states`);
    return response.data
  } catch (error) {
    console.error(error);
  }
}

async function getCountry() {
  try {
    const response = await get(`${baseUrl}/countries`)
    return response.data
  } catch (error) {
    return error.message;
  }
}

async function getDatabaseServer() {
  try {
    const response = await get(`${baseUrl}/serverOptions`)
    return response.data
  } catch (error) {
    console.error(error);
  }
}

async function getDatabaseTables() {
  try {
    const response = await get(`${baseUrl}/tableOptions`);
    return response.data
  } catch (error) {
    console.error(error);
  }
}