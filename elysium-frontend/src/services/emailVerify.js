import axios from "axios";
import { baseUrl } from "../config/constants";
import { handleCookies } from '../utils/cookies'
const { post } = axios;


export const serviceCall = {
  emailVerify,
  getOtp,
  resetPassword
};

async function emailVerify(data) {
  try {
    const response = await post(`${baseUrl}/verify_otp`, data);
    if (response && response.data.status === 'Success') {
      handleCookies.setCookies(response.data.data.token)
      return response.data
    }
    return response.data

  } catch (error) {
    console.error(error);
  }
}

async function getOtp(data) {
  try {
    const response = await post(`${baseUrl}/request_otp`, { email: data });
    if (response.status === "Success") {
      return true
    }
    return false

  } catch (error) {
    console.error(error);
    return false
  }
}

async function resetPassword(data) {
  try {
    const response = await post(`${baseUrl}/reset_password`, data);
    return response.data
  } catch (error) {
    console.error(error);
  }
}