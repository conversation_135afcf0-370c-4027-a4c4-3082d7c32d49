import React, { useEffect, useState } from "react";
import { Outlet, Navigate } from "react-router-dom";
import { handleCookies } from "./cookies";
import { baseUrl } from "../config/constants";
import { useDispatch } from "react-redux";
import {
  updateProfileImage,
  userFullName,
  userName,
  userTrialPeriod,
  userInfo,
} from "../redux/features/UserInformation";
import axios from "axios";

function Auth() {
  const dispatch = useDispatch();
  const useAuth = () => {
    const [isAuth, setIsAuth] = useState(null);
    useEffect(() => {
      const authCheck = async () => {
        var authToken = handleCookies.fetchCookies();
        if (!authToken) {
          setIsAuth(false);
        } else {
          try {
            const response = await axios.post(
              `${baseUrl}/auth/user`,
              {},
              {
                headers: {
                  Authorization: `Bearer ${authToken}`,
                },
              }
            );
            if (response.data.data && response.data.data.user) {
              const userData = response.data.data.user;
              setIsAuth(true);
              userData.image_path &&
                dispatch(updateProfileImage(userData.image_path));
              userData.first_name &&
                dispatch(
                  userName(
                    userData.first_name.charAt(0) + userData.last_name.charAt(0)
                  )
                );
              userData.first_name &&
                dispatch(
                  userFullName(userData.first_name + " " + userData.last_name)
                );

              userData.is_trial_active &&
                dispatch(userTrialPeriod(userData.trial_ends_at));
              userData.is_trial_active &&
                dispatch(userTrialPeriod(userData.trial_days_left));
              userData && dispatch(userInfo(userData));
            } else {
              setIsAuth(false);
            }
          } catch (e) {
            setIsAuth(false);
          }
        }
      };

      authCheck();
    }, []);

    return isAuth;
  };
  const isAuth = useAuth();
  if (isAuth === null) return null;

  return isAuth ? <Outlet /> : <Navigate to="/login" />;
}

export default Auth;
