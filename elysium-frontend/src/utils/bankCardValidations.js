import * as Yup from "yup";


export const bankCardDetailsSchema = Yup.object({
  cardNumber: Yup.string()
    .required("Required")
    .matches(/^\d{15,16}$/, "Must be 15 or 16 digits"),
  cvv: Yup.string()
    .required("Required")
    .matches(/^\d{3,4}$/, "Must be 3 or 4 digits"),
  expiryDate: Yup.string()
    .required("Required")
    .matches(/^(0[1-9]|1[0-2])\/\d{4}$/, "Must be in MM/YY format"),
});