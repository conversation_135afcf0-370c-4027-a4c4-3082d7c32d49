import * as Yup from 'yup';

export const updatePasswordSchema = Yup.object({
  currentPassword: Yup.string().required('Current Password is required'),
  password: Yup.string()
    .required('New Password is required')
    .min(8, 'Password must be at least 8 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])/,
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
    ),
  confirmPassword: Yup.string()
    .required('Repeat Password is required')
    .oneOf([Yup.ref('password'), null], 'Passwords must match'),
});