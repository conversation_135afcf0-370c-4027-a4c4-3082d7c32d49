import * as Yup from "yup";

export const signUpSchema = Yup.object({
  firstName: Yup.string().required("Input your first name"),
  lastName: Yup.string().required("Input your last name"),
  email: Yup.string().email().required("Input your email"),
  companyName: Yup.string().required("Input company name"),
  address: Yup.string().required("Input address"),
  stateId: Yup.number("Select State").required("Select State").typeError("Select State"),
  countryId: Yup.number('Select Country').required("Select Country").typeError("Select Country"),
  city: Yup.string().required("Input City"),
  zip: Yup.string().min(5).required("Input zip"),
  dbTable: Yup.number("Select Number of Db Tables").required("Select Number of Db Tables").typeError("Select Number of Db Tables"),
  dbServer: Yup.number("Select Number of Db server").required("Select Number of Db server").typeError("Select Number of Db server"),
  password: Yup.string().min(8, "Password must me at least 8 characters").matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])/,
    'Password must contain at least one lowercase letter, one uppercase letter, one digit, and one special character'
  ).required("Please enter your password"),
});