import React from "react";
import { Route, Routes } from "react-router-dom";
import SignIn from "./components/signIn/SignIn";
import SignUp from "./components/signup/SignUp";
import ForgotPassword from "./components/forgotPassword/ForgotPassword";
import EmailVerification from "./components/emailVerification";
import ResetPassword from "./components/resetPassword";
import PricePlan from "./components/pricePlan/PricePlan";
import Payment from "./components/payment/Payment";
import Dashboard from "./components/dashboard/Dashboard";
import Settings from "./pages/settings/Settings";
import Auth from "./utils/Auth";
import DashboardServer from "./components/dashboardServer/ServerDashboard";
import DatabaseDashboard from "./components/dashboardDatabase/DatabaseDashboard";
import TablesDashboard from "./components/dashboardTable/TablesDashboard";
import DBServers from "./pages/dbServers/DBServers";
import ServerDBList from "./pages/serverDBList/ServerDBList";
import DBTables from "./pages/dbTablesPage/DBTablesPage";
import PageNotFound from "./pages/pageNotFound/PageNotFound";
import ScheduleDashboard from "./components/scheduleDashboard/schedule";


function App() {
  return (
    <Routes>
      <Route path="/signup" element={<SignUp />} />
      <Route path="/login" element={<SignIn />} />
      <Route path="/forgotpassword" element={<ForgotPassword />} />
      <Route path="/emailverification" element={<EmailVerification />} />
      <Route path="/resetpassword" element={<ResetPassword />} />
      
      <Route element={<Auth />}>
        <Route path="/priceplan" element={<PricePlan />} />
        <Route path="/payment" element={<Payment />} />
        <Route path="/settings" element={<Settings />} />
        <Route path="/database-servers" element={<DBServers />} />
        <Route path="/database-servers/:servername" element={<ServerDBList />} />
        <Route path="/database-servers/:servername/:databasename" element={<DBTables />} />
        <Route path="/server" element={<DashboardServer />} />
        <Route path="/database" element={<DatabaseDashboard />} />
        <Route path="/tables" element={<TablesDashboard />} />
        <Route path="/schedule" element={<ScheduleDashboard />} />

        <Route path="/*" element={<Dashboard />} />

      </Route>
      <Route path="*" element={<PageNotFound />} />
    </Routes>
  );
}

export default App;
