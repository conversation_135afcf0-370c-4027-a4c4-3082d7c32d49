.chart-card-container {
  display: flex;
  min-height: 684px;
  width: 100%;
  overflow: hidden;
  padding-bottom: 0px;
  flex-direction: column;
  align-items: center;
  /* gap: 20px; */
  margin-top: 12px;
  border-radius: 8px;
  border: 1px solid var(--shades-desaturate-10, #dadde5);
  background: var(--dark-light-light, #fff);
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.1),
    0px 1px 2px 0px rgba(0, 0, 0, 0.06);
}

.cards-container {
  display: flex;
  gap: 1px;
  width: 100%;
}

@media (max-width: 768px) {
  .cards-container {
    flex-direction: column;
    gap: 2px;
  }
}
.stack-border {
  width: 100%;
  overflow: hidden;
  height: 1px;
  background: var(--shades-desaturate-10, #dadde5);
  margin-top: 26px ;
  margin-bottom: 44px;
}

.stack-bar-container {
  height: 50%;
  margin-top: -5px ;
  margin-bottom: 20px;
}
.pie-chart-container {
  width: 100% ;
  margin-right: 0 ;
  display: flex;
  padding-left: 48px ;
  padding-right: 0px ;
  gap: 47px;
  justify-content: space-between;
}

.progress-bar-place{
width: 80%;
}
.body-chart-container {

  width: 100%;
  padding: 24px 20px ;
  padding-top: 0px ;
}
.my-custom-gutter-row {
  display: flex ;
  gap: 1px ;
  flex-direction: row;
  flex-wrap: wrap;
  
  }
 
  @media (max-width: 850px) {
    .pie-chart-container {
      flex-wrap: wrap ;
      justify-content: center ;
      align-items: center ;
      margin: auto;
      gap: 30px;
      padding-left: 0px ;
      padding-right: 0px;
    }
  }

@media (max-width: 768px) {
  
 .my-custom-gutter-row {
  display: flex ;
  gap: 2px ;
  flex-direction: row;
  flex-wrap: wrap;
  }
  .dougnut-bar-place{
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .progress-bar-place{
    width: 100%;
  }
}


@media (min-width: 1400px) {
  .my-custom-gutter-row {
    gap: 1.2px !important;
    }
}