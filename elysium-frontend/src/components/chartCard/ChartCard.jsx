import React, { useState, useEffect } from "react";
import { Col, Card } from "react-bootstrap";
import "./style.css";
import InfoCard from "../common/InfoCard";
import UploadImae from "./../../assets/images/fetch-upload--cloud.svg";
import TableImage from "./../../assets/images/table--split (1).svg";
import DataBase from "./../../assets/images/data--base.svg";
import Cost from "./../../assets/images/piggy-bank.svg";
import { StackChart } from "../charts/StackChart";
import { DoughnutBar } from "../charts/DoughnutChart";
import { extractServerDataValues } from "../../utils/helpers";
import DasboardTable from "../dashboard/ProgressTable";
import { colors } from "../../constants/colors";

const ChartCard = ({
  archivedData,
  serverArchivingDates,
  serverData,
  tableData,
}) => {
  const [maxSize, setMaxSize] = useState();

  // Prepare data for the server stack chart
  const totalColor = colors.length;
  const data = serverData
    ? serverData?.map((server, index) => {
        const data = server?.data;
        const values = extractServerDataValues(data);
        const colorIndex = index % totalColor;
        return {
          label: server?.name,
          data: values,
          backgroundColor: colors[colorIndex],
          borderColor: "transparent",
        };
      })
    : [];

  // Calculate the maximum size for the chart based on server data
  useEffect(() => {
    if (!serverData || !serverData[0]?.data) {
      return;
    }

    let maxSum = 0;
    const keys = Object.keys(serverData[0].data);

    if (serverArchivingDates?.length === 1) {
      const totalSum = serverData.reduce((accumulator, server) => {
        const value = Object.values(server?.data);
        return (
          accumulator +
          (Array.isArray(value) ? value.reduce((sum, v) => sum + v, 0) : 0)
        );
      }, 0);
      maxSum = totalSum;
    }

    if (keys.length > 0) {
      keys.forEach((key) => {
        const sumAtIndex = serverData.reduce((accumulator, server) => {
          const value = server?.data?.[key];
          return accumulator + (value !== undefined ? value : 0);
        }, 0);
        maxSum = Math.max(maxSum, sumAtIndex);
      });
    }
    const roundedMaxValue = Math.ceil(maxSum / 5) * 5;
    setMaxSize(roundedMaxValue);
  }, [archivedData, serverArchivingDates, serverData, tableData]);

  return (
    <Card className="chart-card-container p-0">
      <Card.Header className="w-100 p-0 m-0 border-0">
        <div className=" px-0  my-custom-gutter-row">
          <Col className="mx-0" style={{ paddingRight: "0px" }}>
            <InfoCard
              content={"Total Archived Data"}
              count={
                archivedData?.total_archive_data
                  ? `${parseFloat(archivedData?.total_archive_data).toFixed(
                      2
                    )} MB`
                  : "0 MB"
              }
              logo={UploadImae}
              fontSize={"24px"}
            />
          </Col>
          <Col className="mx-0" style={{ paddingRight: "0px" }}>
            <InfoCard
              content={"Total Archived databases"}
              count={
                archivedData?.total_db_archived
                  ? archivedData?.total_db_archived
                  : "0"
              }
              logo={DataBase}
            />
          </Col>
          <Col className="mx-0" style={{ paddingRight: "0px" }}>
            <InfoCard
              content={"Total Archived tables"}
              count={
                archivedData?.total_table_archived
                  ? archivedData?.total_table_archived
                  : "0"
              }
              logo={TableImage}
            />
          </Col>

          <Col className="mx-0 " style={{ paddingRight: "0px" }}>
            <InfoCard
              content={"Total  Storage Cost Saving"}
              count={
                archivedData?.total_table_storage_cost_saving
                  ? archivedData?.total_table_storage_cost_saving?.toLocaleString(
                      "en-US",
                      {
                        style: "currency",
                        currency: "USD",
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      }
                    )
                  : "$0.00"
              }
              logo={Cost}
            />
          </Col>
        </div>
      </Card.Header>
      <div className="body-chart-container ">
        <div className="stack-bar-container">
          <StackChart
            labels={serverArchivingDates}
            datasets={data}
            maxSize={maxSize}
          />
        </div>
        <div className="stack-border"></div>
        <div className="pie-chart-container">
          <div className="dougnut-bar-place">
            {" "}
            <DoughnutBar doughnutChartData={tableData} />
          </div>
          <div className="progress-bar-place">
            <DasboardTable data={tableData} />
          </div>
        </div>
      </div>
    </Card>
  );
};

export default ChartCard;
