import React, { useState, useMemo, useRef, useEffect } from 'react';
import { MaterialReactTable } from 'material-react-table';
import { useNavigate, useLocation } from 'react-router-dom';
import { Box } from '@mui/material';
import { Container } from 'react-bootstrap';
import BreadCrumbs from '../breadCrumbs/BreadCrumbs';
import ConfirmationModal from '../confirmationModal/ConfirmationModal';
import Notification from '../notificationModal/Notification';
import Loader from '../loader/Loader';
import { IoMdOpen } from "react-icons/io";
import { HiOutlineArchive } from "react-icons/hi";
import { RiDeleteBin6Line } from 'react-icons/ri';
import { BsDatabase } from "react-icons/bs";
import { dbServer } from '../../services/dbServers'
import './style.css';

function ServerDBTable() {
  const [open, setOpen] = useState(false);
  const [rowSelection, setRowSelection] = useState([]);
  const [selectedRowArr, setSelectedRowArr] = useState([]);
  const [serverDBData, setServerDBData] = useState([]);
  const navigate = useNavigate();
  const [showNotification, setShowNotification] = useState(false);
  const [confirmModal, setConfirmModal] = useState(false);
  const [selectedRowName, setSelectedRowName] = useState([]);
  const [responseMessage, setResponseMessage] = useState();
  const [serverName, setServerName] = useState();
  const searchRef = useRef(null);
  const location = useLocation();


  useEffect(() => {
    getServerName();
    getServersDB();
  }, []);

  useEffect(() => {
    getSelectedRows();
  }, [rowSelection]);

  // Get Server Name from Params
  const getServerName = () => {
    const { pathname } = location;
    const segments = pathname.split('/');
    setServerName(decodeURIComponent(segments.slice(-1)[0]));
  }

  // Get Selected Row Values
  const getSelectedRows = () => {
    const updatedSelectedRowArr = [];
    const updatedSelectedRowArrName = [];
    if (Object.keys(rowSelection).length > 0) {
      Object.keys(rowSelection).forEach((selectedRow) => {
        // Find the corresponding data from tableData
        const foundRow = serverDBData.find((row) => row.id === parseInt(selectedRow));
        if (foundRow) {
          updatedSelectedRowArr.push(foundRow.id);
          updatedSelectedRowArrName.push(foundRow.db_name);
        }
      });
    }
    setSelectedRowArr(updatedSelectedRowArr);
    setSelectedRowName(updatedSelectedRowArrName);
  }

  // Update Server Information
  const handleInfoServer = () => {
    // setShowNotification(false);
  };

  // Remove Servers
  const handleRemove = () => {
    setShowNotification(false);
    setConfirmModal(true);
  }

  const handleConfirmModal = async () => {
    setConfirmModal(false);
    setRowSelection([]);
    setOpen(true);
    let payload = {
      ids: selectedRowArr
    };
    const response = await dbServer.deleteDBSchema(payload);
    setOpen(false);
    setShowNotification(true);
    setResponseMessage(response.message);
    if (response && response.status === 'Success') {
      getServersDB();
    }
  }

  // DataBase Archive
  const handleArchive = () => {
    console.log("Archive DB");
  }

  const CustomTableToolbar = () => {
    // Custom Table Headers Options
    return (
      <div className='d-flex justify-content-between ps-4 w-100'>
        <div className='table-header server-table'>
          <button className='square rounded add-agent-btn' disabled={selectedRowArr.length > 0 && selectedRowArr.length < 2 ? false : true} onClick={handleInfoServer}><IoMdOpen /> Open</button>
          <button className='square rounded add-agent-btn' disabled={selectedRowArr.length > 0 ? false : true} onClick={handleArchive}><HiOutlineArchive /> Archive</button>
          <button className={`square rounded add-agent-btn ${selectedRowArr.length > 0 ? 'red' : ''}`} disabled={selectedRowArr.length > 0 ? false : true} onClick={handleRemove}><RiDeleteBin6Line /> Remove</button>
        </div>
      </div>
    );
  };

  // Table Headers
  const serverColumns = useMemo(() => [
    {
      accessorKey: 'db_name',
      header: 'Database name',
      selectable: true,
      minSize: '132px',
      Cell: ({ cell }) => (
        <Box
          onClick={() => {
            serverName &&
              navigate(`/database-servers/${serverName}/${cell.getValue()}`, { 
                state: { 
                  id: cell.row.original.id,
                  client_db_schema_id: cell.row.original.client_db_schema_id 
                } 
              });
          }}
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            cursor: 'pointer',
            color: '#174BE6',
          }}
        >
          <BsDatabase style={{ width: '18px', height: '18px', color: '#000' }} />
          <span>{cell.getValue()}</span>
        </Box>
      ),
    },
    {
      accessorFn: (row) => row.total_tables_count,
      total_tables_count: 'total_tables_count',
      header: '# of Tables',
      minSize: '132px',
      fontSize: '10px'
    },
    {
      accessorFn: (row) => row.agent_name,
      agent_name: 'agent_name',
      header: 'Agent',
      minSize: '132px'
    },
    {
      accessorFn: (row) => row.total_current_db_size,
      size: 'total_current_db_size',
      header: 'Size',
      minSize: '132px'
    },
    {
      accessorFn: (row) => row.updated_at,
      updated_at: 'Last modified',
      header: 'Last modified',
      minSize: '132px'
    }
  ], [serverName]);

  // FETCH Servers Data
  const getServersDB = async () => {
    // setServerDBData([
    //   {
    //     "id": 2,
    //     "client_db_server_id": 2,
    //     "client_db_server_name": "Client databasre seuervwer 13223ww",
    //     "agent_name": "Test server elysium backend",
    //     "total_tables_count": 0,
    //     "db_name": "sakila",
    //     "total_current_db_storage_setup_size": 46015,
    //     "total_current_db_size": 6619136,
    //     "total_current_db_data_size": 4210688,
    //     "total_current_db_index_size": 2408448,
    //     "timezone": "",
    //     "created_at": "2023-12-18 16:25:26",
    //     "updated_at": "2023-12-18 16:25:26"
    //   },
    //   {
    //     "id": 3,
    //     "client_db_server_id": 3,
    //     "client_db_server_name": "Client databasre seuervwer 13223ww",
    //     "agent_name": "Test server elysium backend",
    //     "total_tables_count": 0,
    //     "db_name": "sakila",
    //     "total_current_db_storage_setup_size": 46015,
    //     "total_current_db_size": 6619136,
    //     "total_current_db_data_size": 4210688,
    //     "total_current_db_index_size": 2408448,
    //     "timezone": "",
    //     "created_at": "2023-12-18 16:26:05",
    //     "updated_at": "2023-12-18 16:26:05"
    //   },
    //   {
    //     "id": 4,
    //     "client_db_server_id": 4,
    //     "client_db_server_name": "Client databasre seuervwer 13223ww",
    //     "agent_name": "Test server elysium backend",
    //     "total_tables_count": 0,
    //     "db_name": "sakila",
    //     "total_current_db_storage_setup_size": 46015,
    //     "total_current_db_size": 6619136,
    //     "total_current_db_data_size": 4210688,
    //     "total_current_db_index_size": 2408448,
    //     "timezone": "",
    //     "created_at": "2023-12-18 16:28:27",
    //     "updated_at": "2023-12-18 16:28:27"
    //   }])
    setOpen(true);
    let payload = '';
    if (location.state && location.state.id) {
      payload = {
        db_server_id: location.state.id
      }
    }
    const response = await dbServer.getAllServerDB(payload);
    setOpen(false);
    if (response && response.data) {
      setServerDBData(response.data.dbServers);
    } else {
      setServerDBData([]);
    }
  }

  return (
    <>
      <Loader open={open} />
      <Container className='my-5'>
        <div className='my-4'>
          <div className='mb-2 pb-1 text-capitalize'>
            <BreadCrumbs />
          </div>
          <h1 className='mb-4 text-capitalize'>{serverName}</h1>
        </div>
        <div className='tab-content square border rounded-4'>
          <div className='tab-heading mb-3 pt-4 px-3'>
            <h2 className='mb-0'>Database ({serverDBData.length})</h2>
          </div>
          {serverName &&
            <div>
              <MaterialReactTable
                columns={serverColumns}
                data={serverDBData}
                enableSorting={true}
                enableHiding={false}
                enableDensityToggle={false}
                enableColumnActions={false}
                enableColumnFilters={false}
                enableFullScreenToggle={false} // Disable full screen View
                enablePagination={true} // Enable pagination
                getRowId={(row) => row.id}
                enableRowSelection={true}
                onRowSelectionChange={setRowSelection}
                state={{ rowSelection }} // Manage your own state, pass it back to the table (optional)
                // For Searching
                enableGlobalFilterModes
                initialState={{
                  showGlobalFilter: true,
                }}
                muiSearchTextFieldProps={{
                  placeholder: 'Search',
                  ref: searchRef,
                  sx: () => (
                    {
                      minWidth: '252px',
                      '.MuiInputBase-adornedStart': {
                        padding: '0px 6px',
                        background: '#fff'
                      },
                      'button': {
                        padding: '0px',
                        width: 'auto'
                      },
                      '& input': {
                        padding: '8px 0',
                        fontSize: '15px'
                      },
                    }
                  ),
                  variant: 'outlined',
                }}
                muiTableHeadCellProps={{
                  sx: () => ({
                    fontFamily: "Ubuntu",
                  }),
                }}
                muiTopToolbarProps={{
                  sx: () => ({
                    '& .MuiBox-root.css-sq9qdz': {
                      flexDirection: 'row-reverse',
                      alignItems: 'center',
                      padding: '16px',
                    }
                  }),
                }}
                muiTableBodyProps={{
                  sx: () => ({
                    '& tr:nth-of-type(odd)': {
                      backgroundColor: '#f5f8fc',
                    },
                    '& td, & button': {
                      fontFamily: "Ubuntu",
                      fontWeight: "400",
                    }
                  }),
                }}
                muiTablePaginationProps={{
                  sx: () => ({
                    '& p': {
                      fontFamily: "Ubuntu",
                    },
                  })
                }}
                renderTopToolbarCustomActions={() => <CustomTableToolbar />}
              />
            </div>
          }
        </div>
      </Container>
      {showNotification &&
        <Notification responseMessage={responseMessage} />
      }
      {
        confirmModal && (
          <ConfirmationModal buttonValue='Remove'
            showModel={confirmModal}
            onSuccess={handleConfirmModal}
            onClose={() => setConfirmModal(false)}
            title={`Remove DB Server ${selectedRowName} ?`}
            message='Are you sure to remove ?'
          />
        )
      }
    </>
  )
}

export default ServerDBTable
