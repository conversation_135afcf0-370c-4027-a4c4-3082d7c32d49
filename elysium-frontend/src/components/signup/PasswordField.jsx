import React, { useState } from 'react'
import { Form, FormControl, Row, Col, InputGroup } from 'react-bootstrap';
import { FaEye, FaEyeSlash } from 'react-icons/fa';
import { CiCircleCheck, CiCircleRemove } from 'react-icons/ci';


function PasswordField({ values,
  handleChange,
  errors,
  touched,
  showPassword,
  setShowPassword,
  marginClass,
  readonly,
  placeholder }) {

  const [validePassword, setValidePassword] = useState({
    length: false,
    lowercase: false,
    uppercase: false,
    number: false,
    specialChar: false
  })

  const errorsDetails = [
    { name: 'uppercase', regex: /^(?=.*[A-Z])/ },
    { name: 'lowercase', regex: /^(?=.*[a-z])/ },
    { name: 'specialChar', regex: /^(?=.*[!@#$?|><}{%^&*])/ },
    { name: 'number', regex: /^(?=.*\d)/ },
    { name: 'length', regex: 8 },
  ]

  const handleChangePassword = (event) => {
    handleChange(event)
    const { value, name } = event.target;
    if (name === 'password') {
      if (!value) {
        // Reset all state variables when password field is empty
        setValidePassword({
          length: false,
          lowercase: false,
          uppercase: false,
          number: false,
          specialChar: false
        })
      }
      else {

        setValidePassword({
          length: value.length >= 8,
          lowercase: errorsDetails.find(detail => detail.name === 'lowercase').regex.test(value),
          uppercase: errorsDetails.find(detail => detail.name === 'uppercase').regex.test(value),
          number: errorsDetails.find(detail => detail.name === 'number').regex.test(value),
          specialChar: errorsDetails.find(detail => detail.name === 'specialChar').regex.test(value)
        })
      }
    }
  }

  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  return (
    <>
      <Form.Group controlId="password" className={marginClass}>
        <InputGroup className='signup-pwd-field'>
          <FormControl
            type={showPassword ? 'text' : 'password'}
            placeholder={placeholder}
            readOnly={readonly}
            name='password'
            value={values.password}
            className='pwd-field-bg'
            onChange={handleChangePassword}
          />
          <InputGroup.Text onClick={toggleShowPassword} className="pwd-field-bg">

            <span style={{ color: '#6c757d' }}>
              {showPassword ? "Hide" : "Show"}
            </span>
            &nbsp; &nbsp;
            <span>
              {showPassword ? <FaEyeSlash className='pwd-field-bg' /> : <FaEye className='pwd-field-bg' />}
            </span>
          </InputGroup.Text>

        </InputGroup>
      </Form.Group>
      {errors.password && touched.password &&
        <>
          <div className="mt-1 mx-2 p-1">
            <Row>
              {errorsDetails.map((detail) => (
                <Col key={detail.name} className="col-lg-6">
                  {validePassword[detail.name] ? (
                    <CiCircleCheck className='pwd-error-icon' />
                  ) : (
                    <CiCircleRemove style={{ color: 'red' }} />
                  )}
                  <span className={validePassword[detail.name] ? 'succuss-text' : 'error-text'}>
                    {detail.name === 'length' ? ' 8 characters minimum' : ''}
                    {detail.name === 'uppercase' ? ' One uppercase character' : ''}
                    {detail.name === 'lowercase' ? ' One lowercase letter' : ''}
                    {detail.name === 'specialChar' ? ' One special character' : ''}
                    {detail.name === 'number' ? ' One number' : ''}
                  </span>
                </Col>
              ))}
            </Row>
          </div>
        </>
      }
    </>
  )
}

export default PasswordField
