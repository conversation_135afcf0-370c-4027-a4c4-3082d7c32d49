import React, { useState, useEffect } from 'react'
import { Con<PERSON>er, Row, Col } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import SignUpForm from './SignUpForm';
import "./style.css"
import Sidebar from '../../pages/sidebar/Sidebar'
import { serviceCall } from '../../services/signUp'
function SignUp() {

  const [stateArray, setStateArray] = useState([]);
  const [countryArray, setCountryArray] = useState([]);
  const [dbServerArray, setDbServerArray] = useState([]);
  const [dbTablesArray, SetDbTablesArray] = useState([]);

  useEffect(() => {
    getAllStates()
    getAllCountries()
    getDbServer()
    getDbTables()
  }, []);

  const getAllStates = async () => {
    const response = await serviceCall.getStates()
    if (response && response.data) {
      setStateArray(response.data.states)
    }
  }


  const getAllCountries = async () => {
    const response = await serviceCall.getCountry()
    if (response && response.data) {
      setCountryArray(response.data.countries)
    }
  }

  const getDbServer = async () => {
    const response = await serviceCall.getDatabaseServer()
    if (response && response.data) {
      setDbServerArray(response.data.serverOptionsp)
    }
  }

  const getDbTables = async () => {
    const response = await serviceCall.getDatabaseTables()
    if (response && response.data) {
      SetDbTablesArray(response.data.tableOptions)
    }
  }

  return (
    <Container fluid>
      <Row>
        <Col sm={12} md={4} lg={3} className='sidebar-wrapper'>
          <Sidebar />
        </Col>
        <Col sm={12} md={8} lg={9}>
          <div className='signin-form-section'>
            <p className='have-account-text'> Already have an account? </p>
            <Link to="/" className='have-account-link'>
              <button className='signin-nav-btn'> Sign In </button>
            </Link>
          </div>

          <div className='d-flex h-100 align-items-center justify-content-center my-4'>
              <SignUpForm 
               state={stateArray}
               country={countryArray} 
               dbServer={dbServerArray} 
               dbTables={dbTablesArray}
               />
          </div>

        </Col>
      </Row>
    </Container>
  )
}

export default SignUp