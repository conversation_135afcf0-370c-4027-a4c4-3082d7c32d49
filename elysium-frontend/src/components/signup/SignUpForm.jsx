import React, { useState } from 'react';
import { Form, Button, Row, Col } from 'react-bootstrap';
import { useNavigate } from 'react-router';
import { useFormik } from "formik";
import { signUpSchema } from '../../utils/signUpValidation'
import PasswordField from './PasswordField';
import { serviceCall } from '../../services/signUp'
import Loader from '../loader/Loader';


function SignUpForm({ state, country, dbServer, dbTables }) {
  const [open, setOpen] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();
  const [errorMessage, setErrorMessage] = useState();

  const initialValues = {
    firstName: '',
    lastName: '',
    companyName: '',
    address: '',
    secondaryAddress: '',
    countryId: '',
    stateId: '',
    city: '',
    zip: '',
    dbServer: '',
    dbTable: '',
    email: '',
    password: '',
  };
  const { values, handleChange, handleSubmit, errors, touched } =
    useFormik({
      initialValues,
      validationSchema: signUpSchema,
      validateOnChange: true,
      validateOnBlur: false,
      onSubmit: async (values, action) => {
        setOpen(true);
        let payload = {
          company_name: values.companyName,
          country_id: values.countryId,
          state_id: values.stateId,
          address: values.address,
          address2: values.secondaryAddress,
          city: values.city,
          zip: values.zip,
          first_name: values.firstName,
          last_name: values.lastName,
          email: values.email,
          password: values.password,
          db_server_option: values.dbServer,
          db_table_option: values.dbTable,
        }
        const response = await serviceCall.signUp('/auth/register', payload)
        setOpen(false);
        if (response.status === 'Success') {
          action.resetForm();
          setShowPassword(false);
          handleVerifyEmail(values.email);
        }
        else {
          setErrorMessage(response.messages);
        }
      },
    });
  const handleVerifyEmail = (email) => {
    navigate('/emailverification', { state: { navigationFlag: false, email: email } })
  }

  return (
    <div>
      <Loader open={open} />
      <Form onSubmit={handleSubmit} className="d-flex flex-column  mt-2"
        style={{ maxWidth: '416px' }}>
        <Row>
          <h6 className='singup-title'> Create an account</h6>
        </Row>
        <Row>
          <Col className='pe-0'>
            <Form.Group controlId="firstName" className='my-2'>

              <Form.Control
                type="text"
                className='signup-name-filed'
                name='firstName'
                placeholder=" First name"
                value={values.firstName}
                onChange={handleChange}
              />
            </Form.Group>
            {errors.firstName && touched.firstName &&
              <div className="mt-1 mx-2 p-1 error-text" >
                {errors.firstName}
              </div>
            }
          </Col>
          <Col>
            <Form.Group controlId="lastName" className='my-2'>
              <Form.Control
                type="text"
                className='signup-name-filed'
                placeholder="Last name"
                name='lastName'
                value={values.lastName}
                onChange={handleChange}
              />
            </Form.Group>
            {errors.lastName && touched.lastName &&
              <div className="mt-1 mx-2 p-1 error-text" >
                {errors.lastName}
              </div>
            }
          </Col>
        </Row>
        <Form.Group controlId="email" className='my-2'>
          <Form.Control
            type="email"
            placeholder="Email"
            className='signup-field'
            name='email'
            value={values.email}
            onChange={handleChange}
          />
        </Form.Group>
        {errors.email && touched.email &&
          <div className="mt-1 mx-2 p-1 error-text">
            {errors.email}
          </div>
        }

        <Form.Group controlId="companyName" className='my-2'>
          <Form.Control
            type="text"
            className='signup-field'
            placeholder="Company name"
            name='companyName'
            value={values.companyName}
            onChange={handleChange}
          />
        </Form.Group>
        {errors.companyName && touched.companyName &&
          <div className="mt-1 mx-2 p-1 error-text">
            {errors.companyName}
          </div>
        }

        <Form.Group controlId="address" className='my-2'>
          <Form.Control
            type="text"
            className='signup-field'
            placeholder="Address 1"
            name='address'
            value={values.address}
            onChange={handleChange}
          />
        </Form.Group>
        {errors.address && touched.address &&
          <div className="mt-1 mx-2 p-1 error-text">
            {errors.address}
          </div>
        }
        <Form.Group controlId="secondaryAddress" className='my-2'>
          <Form.Control
            type="text"
            className='signup-field'
            placeholder="Address 2"
            name='secondaryAddress'
            value={values.secondaryAddress}
            onChange={handleChange}
          />
        </Form.Group>

        <Row>
          <Col sm={12} md={8} lg={6} className='pe-0'>
            <Form.Group controlId="countryId" className='my-2' >
              <Form.Select
                value={values.countryId}
                className='signup-dropdown-field'
                name='countryId'
                onChange={handleChange}
              >
                <option >Country</option>
                {
                  country.map((data) => (
                    <option key={data.id} value={data.id}>{data.name}</option>
                  ))
                }
              </Form.Select>
            </Form.Group>
            {errors.countryId && touched.countryId &&
              <div className="mt-1 mx-2 p-1 error-text">
                {errors.countryId}
              </div>
            }
          </Col>
          <Col sm={12} md={8} lg={6}>
            <Form.Group controlId="stateId" className='my-2' >
              <Form.Select
                value={values.stateId}
                className='signup-dropdown-field'
                name='stateId'
                onChange={handleChange}
              >
                <option >State</option>
                {
                  state.map((data) => (

                    <option key={data.id} value={data.id}>{data.state_name}</option>
                  ))
                }
              </Form.Select>
            </Form.Group>
            {errors.stateId && touched.stateId &&
              <div className="mt-1 mx-2 p-1 error-text">
                {errors.stateId}
              </div>
            }
          </Col>
        </Row>

        <Row>
          <Col className='pe-0'>
            <Form.Group controlId="zip" className='my-2'>
              <Form.Control
                type="number"
                className='signup-field'
                placeholder="Zip"
                name='zip'
                value={values.zip}
                onChange={handleChange}
              />
            </Form.Group>
            {errors.zip && touched.zip &&
              <div className="mt-1 mx-2 p-1 error-text">
                {errors.zip}
              </div>
            }
          </Col>
          <Col sm={12} md={8} lg={6}>
            <Form.Group controlId="city" className='my-2'>

              <Form.Control
                type="text"
                className='signup-field'
                placeholder="City"
                name='city'
                value={values.city}
                onChange={handleChange}
              />
            </Form.Group>
            {errors.city && touched.city &&
              <div className="mt-1 mx-2 p-1 error-text">
                {errors.city}
              </div>
            }
          </Col>
        </Row>

        <Row>
          <Col sm={12} md={8} lg={6} className='pe-0'>
            <Form.Group controlId="dbserver" className='my-2' >
              <Form.Select
                value={values.dbServer}
                className='signup-dropdown-field'
                name='dbServer'
                onChange={handleChange}
              >
                <option >Number of Db Servers</option>
                {
                  dbServer.map((data) => (
                    <option key={data.id} value={data.id}>{data.name}</option>
                  ))
                }
              </Form.Select>
            </Form.Group>
            {errors.dbServer && touched.dbServer &&
              <div className="mt-1 mx-2 p-1 error-text">
                {errors.dbServer}
              </div>
            }
          </Col>
          <Col sm={12} md={8} lg={6}>
            <Form.Group controlId="dbtables" className='my-2'>
              <Form.Select aria-label="No of Db Tables"
                className='signup-dropdown-field'
                name='dbTable'
                value={values.dbTable}
                onChange={handleChange}
              >
                <option value=''>Number of Db Tables</option>
                {
                  dbTables.map((data) => (
                    <option key={data.id} value={data.id}>{data.name}</option>
                  ))
                }
              </Form.Select>
            </Form.Group>
            {errors.dbTable && touched.dbTable &&
              <div className="mt-1 mx-2 p-1 error-text">
                {errors.dbTable}
              </div>
            }
          </Col>
        </Row>
        <PasswordField values={values} handleChange={handleChange} marginClass='my-2'
          errors={errors} touched={touched} showPassword={showPassword}
          setShowPassword={setShowPassword} placeholder='Password' />
        {errorMessage &&
          errorMessage.map(errors => {
            return (
              <div className="mt-0 mx-2 p-0 error-text">
                {errors}
              </div>
            )
          })
        }
        <Button className='mt-2 signup-btn' type="submit">
          Sign Up
        </Button>
      </Form>
    </div>
  )
}

export default SignUpForm