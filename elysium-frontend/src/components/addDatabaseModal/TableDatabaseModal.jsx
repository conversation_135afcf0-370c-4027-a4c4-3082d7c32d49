import React, { useEffect, useRef, useState } from "react";
import searchIcon from "./../../assets/images/search.svg";
import ArrowLeft from "./../../assets/images/arrow--left.svg";
import ArrowRight from "./../../assets/images/arrow--right.svg";
import ArrowRightVisible from "./../../assets/images/arrow--right-black.svg";
import ArrowleftVisible from "./../../assets/images/arrow-left-black.svg";
import CloseIcon from "./../../assets/images/close.svg";
import TableImage from "./../../assets/images/table--split (2).svg";
import TableImageDisabled from "./../../assets/images/table--split (2) copy.svg";
import DownArrow from "./../../assets/images/chevron--sort--down.svg";
import UpArrow from "./../../assets/images/chevron--sort.svg";
import { serviceCall } from "../../services/scheduleServices";
import Loader from "../loader/Loader";
import Notification from "../notificationModal/Notification";
const itemsPerPage = 10;
/* eslint-disable react-hooks/exhaustive-deps */

const DasboardTable = ({ modalProps }) => {
  const [servers, setServers] = useState([]);
  const [databases, setDatabases] = useState([]);
  const [data, setData] = useState([]);
  const [open, setOpen] = useState(false);
  const [responseMessage, setResponseMessage] = useState();
  const [showNotification, setShowNotification] = useState(false);
  const [isError, setIsError] = useState(false);
  const [isPageClick, setIsPageClick] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [selectedServer, setSelectedServer] = useState("");
  const [selectedDatabase, setSelectedDatabase] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageNumbers, setPageNumbers] = useState([]);
  const [renderNumbers, setRenderNumbers] = useState([]);
  const [rightNumbers, setRightNumbers] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [sortOrder, setSortOrder] = useState({
    table_name: false,
    retention: false,
    retention_index: false,
    is_partitioned: false,
    total_rows: false,
    batch_size: false,
    schedule_status: false,
  });

  const lastPageIndex = Math.ceil(data?.length / itemsPerPage);

  // pagination
  const renderData = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredData?.slice(startIndex, endIndex);
  };

  const handlePrevious = () => {
    if (
      currentPage >= 2 &&
      currentPage <= pageNumbers[pageNumbers?.length - 3] &&
      renderNumbers[0] > 1
    ) {
      renderNumbers.pop();
      renderNumbers.unshift(pageNumbers[renderNumbers[0] - 2]);
    }

    setCurrentPage((prevPage) => Math.max(1, prevPage - 1));
  };

  const handleNext = () => {
    const pageDiff = rightNumbers[0] - renderNumbers[1];
    if (pageNumbers.length > 2 && currentPage >= 2 && pageDiff >= 2) {
      renderNumbers?.shift();
      renderNumbers?.push(pageNumbers[renderNumbers[0]]);
      setCurrentPage((prevPage) => Math.min(lastPageIndex, prevPage + 1));
    } else {
      setCurrentPage((prevPage) => Math.min(lastPageIndex, prevPage + 1));
    }
  };

  const renderPageNumbers = () => {
    const pagesLength = Math.max(
      filteredData?.length > itemsPerPage
        ? Math.ceil(filteredData?.length / itemsPerPage)
        : 1
    );
    const pageNumber = [];
    for (let i = 1; i <= pagesLength; i++) {
      pageNumber.push(i);
    }
    setPageNumbers(pageNumber);
    setRenderNumbers([pageNumber[0], pageNumber[1]]);
    setRightNumbers([
      pageNumber[pageNumber?.length - 2],
      pageNumber[pageNumber?.length - 1],
    ]);
  };

  const handleLastPages = (page) => {
    setCurrentPage(page);
    if (page === pageNumbers[pageNumbers.length - 1]) {
      setRenderNumbers([page - 3, page - 2]);
    } else {
      setRenderNumbers([page - 2, page - 1]);
    }
  };

  //checkboxses
  const handleCheckboxChange = (serverId) => {
    setSelectedRows((prevSelectedRows) => {
      if (prevSelectedRows.includes(serverId)) {
        // If the serverId is already in the selectedRows, remove it
        return prevSelectedRows.filter((id) => id !== serverId);
      } else {
        // Otherwise, add it to the selectedRows
        return [...prevSelectedRows, serverId];
      }
    });
  };

  const handleSelectAll = () => {
    // If all rows are currently selected, unselect all; otherwise, select all
    const lengthNotArchived = filteredData?.filter(
      (row) => row?.is_current === "none"
    );

    const allRowsSelected = selectedRows?.length === lengthNotArchived?.length;
    if (allRowsSelected) {
      setSelectedRows([]);
    } else {
      setSelectedRows(lengthNotArchived?.map((server) => server.table_id));
    }
  };

  const searchOnChange = (e) => {
    const searchedData = data?.filter((item) => {
      const query = e.target.value.toLowerCase();
      setSearchValue(query);
      return (
        item?.table_name.toString()?.toLowerCase().indexOf(query) >= 0 ||
        item?.is_partitioned.toString()?.toLowerCase().indexOf(query) >= 0 ||
        item?.retention_index.toString()?.toLowerCase().indexOf(query) >= 0 ||
        item?.schedule_status?.toString().toLowerCase().indexOf(query) >= 0 ||
        item?.batch_size?.toString().indexOf(query) >= 0 ||
        item?.total_rows?.toString().toLowerCase().indexOf(query) >= 0 ||
        item?.retention?.toString()?.toLowerCase().indexOf(query) >= 0 ||
        item?.is_current?.toString()?.toLowerCase().indexOf(query) >= 0
      );
    });

    setFilteredData(searchedData);
    setCurrentPage(1);
  };

  const handleRemoveSearch = () => {
    setSearchValue("");
    setFilteredData(data);
  };

  //sorting
  const handleSort = (column) => {
    if (!Array.isArray(filteredData)) {
      return;
    }
    // Create a new object with all sorting orders set to false
    const newSortOrder = Object.fromEntries(
      Object.keys(sortOrder).map((key) => [key, false])
    );
    // Toggle the sorting order for the selected column
    newSortOrder[column] = !sortOrder[column];
    setSortOrder(newSortOrder);
    // Call the sortData function with the new sort order
    sortData(column, newSortOrder[column]);
  };

  const sortData = (column, ascending) => {
    const newData = [...filteredData].sort((a, b) => {
      switch (column) {
        case "table_name":
          return ascending
            ? a[column].localeCompare(b[column])
            : b[column].localeCompare(a[column]);
        case "retention":
        case "retention_index":
        case "total_rows":
        case "batch_size":
        case "is_partitioned":
          return ascending
            ? parseInt(a[column]) - parseInt(b[column])
            : parseInt(b[column]) - parseInt(a[column]);
        default:
          return 0;
      }
    });

    setFilteredData(newData);
  };

  // Function to fetch All servers
  const getAllServers = async (range) => {
    setDatabases([]);
    setData([]);
    setOpen(true);
    try {
      const response = await serviceCall.getServers(range);
      if (response?.status === "Success") {
        setServers(response?.data?.server_listing);
        const id = response?.data?.server_listing[0]?.id;
        if (!modalProps?.slectedDatabaseTable) {
          setSelectedServer(id);
        }
      } else {
        setResponseMessage(
          response?.messages
            ? response?.messages
            : response?.response.data?.message || "Something went wrong"
        );
        setIsError(true);
        setShowNotification(true);
        setOpen(false);
      }
    } catch (error) {
      setOpen(false);
    } finally {
    }
  };

  // function fetch all databases of specific server
  const getServerDatabases = async (range) => {
    setOpen(true);
    try {
      const response = await serviceCall.getDatabases(range);
      if (response?.status === "Success") {
        setDatabases(response?.data?.database_listing);
      } else {
        setResponseMessage(
          response?.messages
            ? response?.messages
            : response?.response.data?.message || "Something went wrong"
        );
        setIsError(true);
        setShowNotification(true);
      }
    } catch (error) {
    
    } finally {
      setOpen(false);
    }
  };

  // function fetch all databases of specific server
  const getTableListing = async (payload) => {
    setOpen(true);
    try {
      const response = await serviceCall.getTables(payload);
      if (response?.status === "Success") {
        if (Object.values(response?.data).length > 0) {
          setFilteredData(response?.data?.db_tables);
          setData(response?.data?.db_tables);
        } else {
          setFilteredData([]);
          setData([]);
        }
      } else {
        setResponseMessage(
          response?.messages
            ? response?.messages
            : response?.response.data?.message || "Something went wrong"
        );
        setIsError(true);
        setShowNotification(true);
      }
    } catch (error) {
     
    } finally {
      setOpen(false);
    }
  };

  const AddTable = async (payload) => {
    setOpen(true);
    try {
      const response = await serviceCall.AddTable(payload);
      if (response?.status === "Success") {
        modalProps?.handleReload();
        modalProps?.onHide();
      } else {
        setResponseMessage(
          response?.messages
            ? response?.messages
            : response?.response.data?.message || "Something went wrong"
        );
        setIsError(true);
        setShowNotification(true);
      }
    } catch (error) {
     
    } finally {
      setOpen(false);
    }
  };

  const handleToastClose = () => {
    setShowNotification(false);
    setResponseMessage("");
    setIsError(false);
  };

  const addTableInSchedule = () => {
    if (selectedRows.length > 0) {
      AddTable({
        table_ids: selectedRows,
        server_id: selectedServer,
        database_id: selectedDatabase,
      });
    }
  };

  useEffect(() => {
    renderPageNumbers();
  }, [data, filteredData]);

  useEffect(() => {
    setFilteredData([]);
    setSelectedRows([]);
    setData([]);
    if (selectedDatabase && selectedServer) {
      const payload = {
        database_id: selectedDatabase,
        server_id: selectedServer,
      };
      getTableListing(payload);
    }
  }, [selectedDatabase]);

  useEffect(() => {
    if (renderNumbers[1] === currentPage && currentPage + 1 < rightNumbers[0]) {
      setRenderNumbers([currentPage, currentPage + 1]);
    } else {
      if (currentPage > 1 && currentPage < rightNumbers[0])
        setRenderNumbers([currentPage - 1, currentPage]);
    }
  }, [isPageClick]);

  useEffect(() => {
    if (selectedServer) {
      setDatabases([]);
      setSelectedRows([]);
      setData([]);
      setFilteredData([]);
      getServerDatabases({ server_id: selectedServer });
    }
  }, [selectedServer]);

  const isMounted = useRef(false);

  useEffect(() => {
    if (!isMounted.current) {
      getAllServers();
      isMounted.current = true;
    }
  }, []);

  useEffect(() => {
    if (modalProps?.slectedDatabaseTable) {
      setSelectedServer(modalProps?.slectedDatabaseTable?.serverId);
      setSelectedDatabase(modalProps?.slectedDatabaseTable?.DataBaseId);
    }
  }, [modalProps]);

  return (
    <>
      <Loader open={open} isForModal={true} />
      <div className="add-db-mdl-heading">Schedule Database</div>
      <div className="select-server-db-container">
        <div className="select-server-db-sec-container">
          <div className="media-margin">Tables</div>{" "}
          <select
            className="media-margin server-select-from-drp-dwn select-styled"
            value={selectedServer}
            onChange={(e) => setSelectedServer(e.target.value)}
          >
            {servers ? (
              servers?.map((server, index) => (
                <option className="select-styled" key={index} value={server.id}>
                  {server?.db_server_name}
                </option>
              ))
            ) : (
              <option className="select-styled" key="00" value="">
                Select Server
              </option>
            )}
          </select>
          <select
            className="media-margin server-select-from-drp-dwn select-styled"
            value={selectedDatabase}
            onChange={(e) => setSelectedDatabase(e.target.value)}
          >
            <option value="">Select Database</option>
            {databases &&
              databases?.map((db, index) => (
                <option key={index} value={db?.id}>
                  {db?.db_name}
                </option>
              ))}
          </select>
        </div>
        <div className="select-server-db-search">
          <img
            style={{ height: "20px", width: "20px" }}
            src={searchIcon}
            alt="Search Icon"
          />
          <input
            className="search-input-indb-servers-table"
            type="text"
            value={searchValue}
            placeholder="Search..."
            onChange={searchOnChange}
          />
          {searchValue && (
            <img
              style={{ height: "20px", width: "20px" }}
              src={CloseIcon}
              alt="Search Icon"
              className="icon-hover-state"
              onClick={handleRemoveSearch}
            />
          )}
        </div>
      </div>
      <div className="custom-table-container-wrapper">
        <div style={{ minHeight: "361px" }}>
          <table className="table-container">
            <tbody>
              <tr
                className="table-header-borders"
                style={{ maxHeight: "32px" }}
              >
                <th>
                  <div className="header-container">
                    <label className="container-check-mark">
                      <input
                        type="checkbox"
                        className="checkmark"
                        onChange={handleSelectAll}
                        checked={
                          selectedRows.length ===
                            filteredData?.filter(
                              (row) => row?.is_current === "none"
                            )?.length && filteredData.length > 0
                        }
                      />

                      <span className="checkmark"></span>
                    </label>
                    <span className="border-table-of-modal"></span>
                  </div>
                </th>

                <th className="min-width-table-th">
                  <div className="header-container ">
                    <span onClick={() => handleSort("table_name")}>
                      Table name{" "}
                      <img
                      className="sorting-table-icons"
                        src={sortOrder?.table_name ? UpArrow : DownArrow}
                        alt="sort"
                      />
                    </span>{" "}
                    <span className="border-table-of-modal"></span>
                  </div>
                </th>

                <th className="min-width-table-th">
                  <div className="header-container ">
                    <span onClick={() => handleSort("retention")}>
                      Retention{" "}
                      <img
                        className="sorting-table-icons"
                        src={sortOrder?.retention ? UpArrow : DownArrow}
                        alt="sort"
                      />
                    </span>{" "}
                    <span className="border-table-of-modal"></span>
                  </div>
                </th>
                <th className="min-width-table-th">
                  <div className="header-container">
                    <span onClick={() => handleSort("retention_index")}>
                      RI{" "}
                      <img
                      className="sorting-table-icons"
                        src={sortOrder?.retention_index ? UpArrow : DownArrow}
                        alt="sort"
                      />
                    </span>{" "}
                    <span className="border-table-of-modal"></span>
                  </div>
                </th>
                <th className="min-width-table-th">
                  <div className="header-container">
                    <span onClick={() => handleSort("is_partitioned")}>
                      Partition{" "}
                      <img
                       className="sorting-table-icons"
                        src={sortOrder?.is_partitioned ? UpArrow : DownArrow}
                        alt="sort"
                      />
                    </span>{" "}
                    <span className="border-table-of-modal"></span>
                  </div>
                </th>
                <th className="min-width-table-th">
                  <div className="header-container">
                    <span onClick={() => handleSort("total_rows")}>
                      Total Rows{" "}
                      <img
                        className="sorting-table-icons"
                        src={sortOrder?.total_rows ? UpArrow : DownArrow}
                        alt="sort"
                      />
                    </span>{" "}
                    <span className="border-table-of-modal"></span>
                  </div>
                </th>
                <th className="min-width-table-th">
                  <div className="header-container">
                    <span onClick={() => handleSort("batch_size")}>
                      Batch size{" "}
                      <img
                          className="sorting-table-icons"
                        src={sortOrder?.batch_size ? UpArrow : DownArrow}
                        alt="sort"
                      />
                    </span>{" "}
                    <span className="border-table-of-modal"></span>
                  </div>
                </th>
                <th style={{ width: "186px" }} className="min-width-table-th">
                  <div className="header-container">
                    <span >Schedule status</span>
                  </div>
                </th>
              </tr>

              {selectedDatabase ? (
                renderData()?.length === 0 ? (
                  <tr style={{ height: "320px" }}>
                    <td colSpan="8">
                      <div className="no-atabase-selected-container">
                        <div className="no-atabase-selected-container-text">
                          <div>
                            {open
                              ? ""
                              : "No tables found for the selected database."}
                          </div>{" "}
                        </div>
                      </div>
                    </td>
                  </tr>
                ) : (
                  renderData()?.map((server, index) => {
                    const isDisabled = server?.is_current === "added to list" || server?.is_dropped === 1;
                    const isEvenRow = index % 2 === 0;
                    return (
                      <tr
                        className={`table-rows ${
                          isEvenRow ? "even-row" : "odd-row"
                        }`}
                        key={index}
                        style={{ maxHeight: "32px" }}
                      >
                        <td style={{ paddingRight: "0px" }}>
                          <label className="container-check-mark">
                            <input
                              type="checkbox"
                              className="checkmark"
                              checked={selectedRows.includes(server.table_id)}
                              onChange={() =>
                                handleCheckboxChange(server.table_id)
                              }
                              disabled={isDisabled}
                            />

                            <span className="checkmark"></span>
                          </label>
                        </td>
                        <td style={{ padding: "0px" }}>
                          <div
                            className={`tb-img-name font-modal-table-td ${
                              isDisabled ? "disabled-text" : ""
                            }`}
                          >
                            {" "}
                            <img
                              style={{
                                height: "16px",
                                width: "16px",
                                marginRight: "8px",
                              }}
                              src={isDisabled ? TableImageDisabled : TableImage}
                              alt="no icon"
                            />{" "}
                            {server?.table_name}
                          </div>
                        </td>
                        <td
                          className={`table-rows-color font-modal-table-td ${
                            isDisabled ? "disabled-text" : ""
                          }`}
                        >
                          <span>{server?.retention}</span>
                        </td>
                        <td>
                          <div
                            className={`table-rows-color font-modal-table-td ${
                              isDisabled ? "disabled-text" : ""
                            }`}
                          >
                            <span>{server?.retention_index}</span>
                          </div>
                        </td>
                        <td
                          className={`table-rows-color font-modal-table-td ${
                            isDisabled ? "disabled-text" : ""
                          }`}
                        >
                          <span>{server?.is_partitioned===0? "N/A":server?.is_partitioned}</span>
                        </td>
                        <td>
                          <div
                            className={`table-rows-color font-modal-table-td ${
                              isDisabled ? "disabled-text" : ""
                            }`}
                          >
                            {server?.total_rows}M
                          </div>
                        </td>
                        <td
                          className={`table-rows-color font-modal-table-td ${
                            isDisabled ? "disabled-text" : ""
                          }`}
                        >
                          <span>{server?.batch_size.toLocaleString()}</span>
                        </td>
                        <td className="cost-heading font-modal-table-td">
                          <div className="">
                            {server?.is_current === "added to list"
                              ? `${
                                  server?.is_current?.charAt(0).toUpperCase() +
                                  server?.is_current?.slice(1)
                                }${
                                  server?.schedule_status
                                    ? `, ${server?.schedule_status}`
                                    : ""
                                }`
                              : "None"}
                          </div>
                        </td>
                      </tr>
                    );
                  })
                )
              ) : (
                <tr style={{ height: "320px" }}>
                  <td colSpan="8">
                    <div className="no-atabase-selected-container">
                      <div className="no-atabase-selected-container-text">
                        {open ? (
                          ""
                        ) : (
                          <>
                            {servers?.length > 0 ? (
                              <>
                                <div>No Database selected.</div>{" "}
                                <div>Select a database to continue.</div>
                              </>
                            ) : (
                              "No Server found"
                            )}
                          </>
                        )}
                      </div>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        <div className="progress-table-server-lengh">
          {data?.length > 0 ? (
            <>
              {" "}
              <span className="server-length">
                {filteredData?.length ?? 0} Items
              </span>
              <span className="border-item-selected-total"></span>
              <span className="selected-tables-length">
                {selectedRows?.length ?? 0} item selected
              </span>
            </>
          ) : (
            ""
          )}
        </div>
        <div className="archiving-table-footer">
          <div className="pagination-container-of-modal">
            {data?.length > 0 ? (
              <>
                <button
                  className={`archiving-table-footer-button ${
                    currentPage > 1 ? "active" : ""
                  }`}
                  onClick={handlePrevious}
                  disabled={currentPage === 1}
                >
                  {" "}
                  <img
                    className="btns-arrow"
                    alt=">"
                    src={currentPage > 1 ? ArrowleftVisible : ArrowLeft}
                  />
                  <span>Previous</span>{" "}
                </button>
                <div className="pagination-table-modal">
                  {pageNumbers.length < 4 ? (
                    renderNumbers?.map((pageNumber, index) => (
                      <span
                        key={index}
                        className={`pagination-number ${
                          pageNumber === currentPage
                            ? "pagination-number-active"
                            : ""
                        }`}
                        onClick={() => setCurrentPage(pageNumber)}
                      >
                        {pageNumber}
                      </span>
                    ))
                  ) : (
                    <>
                      {" "}
                      {renderNumbers?.map((pageNumber, index) => (
                        <span
                          key={index}
                          className={`pagination-number ${
                            pageNumber === currentPage
                              ? "pagination-number-active"
                              : ""
                          }`}
                          onClick={() => {
                            setCurrentPage(pageNumber);
                            setIsPageClick(!isPageClick);
                          }}
                        >
                          {pageNumber}
                        </span>
                      ))}
                      <span>...</span>
                      {rightNumbers?.map((pageNumber, index) => (
                        <span
                          key={index}
                          className={`pagination-number ${
                            pageNumber === currentPage
                              ? "pagination-number-active"
                              : ""
                          }`}
                          onClick={() => handleLastPages(pageNumber)}
                        >
                          {pageNumber}
                        </span>
                      ))}
                    </>
                  )}
                </div>
                <button
                  className={`archiving-table-footer-button ${
                    data?.length > 3
                      ? currentPage !== lastPageIndex
                        ? "active"
                        : ""
                      : ""
                  }`}
                  onClick={handleNext}
                  disabled={currentPage === lastPageIndex}
                >
                  {" "}
                  <span>Next</span>{" "}
                  <img
                    className="btns-arrow"
                    alt=">"
                    src={
                      data?.length > 3
                        ? currentPage !== lastPageIndex
                          ? ArrowRightVisible
                          : ArrowRight
                        : ArrowRight
                    }
                  />{" "}
                </button>
              </>
            ) : (
              ""
            )}
          </div>

          <div className="modal-db-servers-footer">
            <button
              className="cncl-db-servers-btn"
              onClick={modalProps?.onHide}
            >
              Cancel
            </button>
            <button
              className={`add-db-servers-btn ${
                selectedRows?.length > 0 ? "selected" : ""
              }`}
              onClick={() => addTableInSchedule()}
            >
              {" "}
              Add
            </button>
          </div>
        </div>
      </div>
      {showNotification && (
        <Notification
          responseMessage={responseMessage}
          isError={isError}
          handleToastClose={handleToastClose}
        />
      )}
    </>
  );
};

export default DasboardTable;
