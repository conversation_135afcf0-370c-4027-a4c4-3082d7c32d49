.add-db-mdl-heading {
  display: flex;
  height: 48px;
  padding: 20px 12px;
  align-items: center;
  gap: 12px;
  align-self: stretch;
  border-bottom: 1px solid var(--Shades-Desaturate-10, #dadde5);
  background: #fff;
  color: var(--Gray-900, #101828);
  font-family: Ubuntu;
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: 28px; /* 155.556% */
}
.add-db-modal {
  border-radius: 5px !important;
  /* overflow: hidden; */
}

.select-server-db-container {
  display: flex;
  padding: 8px 8px 8px 12px;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  background: var(--Shades-Desaturate-11, #f6f8fc);
  width: 100% !important;
}
.select-server-db-sec-container {
  width: 488px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.select-server-db-search {
  display: flex;
  padding: 10px 8px;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  background: #fff;
  height: 28px;
  min-width: 200px;
  border-radius: 5px;
  border: 1px solid var(--shades-desaturate-10, #dadde5) !important;
}
.icon-hover-state:hover{
  border: 1px solid rgb(46, 45, 43);
  border-radius: 7px;
}
.search-input-indb-servers-table {
  border: none;
  outline: none;
  width: 150px;
  color: var(--Text-On-White-High-Contrast, #000117);

  /* Paragraph/S/Regular */
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 142.857% */
}

.server-select-from-drp-dwn {
  display: flex;

  align-items: center;
  gap: 8px;

  background: #fff;
  height: 28px;
  width: 200px;
  border-radius: 5px;
  border: 1px solid var(--shades-desaturate-10, #dadde5) !important;
  color: var(--Text-On-White-High-Contrast, #000117);

  /* Paragraph/S/Regular */
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 142.857% */
  border: none;
  outline: none;
}

.modal-db-servers-footer {
  display: flex;
  height: 48px;
  padding: 20px 12px;
  justify-content: flex-end;
  align-items: center;
  gap: 12px;
  align-self: stretch;
  border-top: 1px solid var(--Shades-Desaturate-10, #dadde5);
  background: #fff;
  outline: none;
  border: none;
}

.cncl-db-servers-btn {
  display: flex;
  height: 32px;
  padding: 0px 12px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 6px;
  border: 1px solid var(--Shades-Desaturate-10, #dadde5);
  background: var(--dark-light-light, #fff);

  /* Shadow/XS */
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
  color: var(--Text-On-White-High-Contrast, #000117);
  text-align: center;

  /* Button/S/Regular */
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px; /* 114.286% */
  letter-spacing: 0.28px;
  outline: none;
}

.add-db-servers-btn {
  display: flex;
  width: 88px;
  height: 40px;
  padding: 0px 16px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  border-radius: 8px;
  background: var(--Shades-Desaturate-10, #dadde5);
  color: var(--Shades-Desaturate-8, #9ba4bc);

  text-align: center;

  /* Button/S/Regular */
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px; /* 114.286% */
  outline: none;
  border: none;
  letter-spacing: 0.28px;
}

.add-db-servers-btn.selected {
  border-radius: 8px;
  background: var(--dark-light-dark, #000117);
  color: var(--dark-light-light, #fff);
}

.custom-modal-table-container-wrapper {
  min-width: 1000px !important;
  overflow-x: scroll !important;
}
.table-rows.even-row {
  background-color: #f6f8fc; /* Background color for even rows */
}
.table-rows.odd-row {
  background-color: #fff; /* Background color for odd rows */
}
.db-server-table-modal-size {
  min-width: 1200px !important;
  border-radius: 10px !important;
}
@media (max-width: 1250px) {
  .db-server-table-modal-size {
    min-width: 1000px !important;
  }
}
@media (max-width: 1020px) {
  .db-server-table-modal-size {
    min-width: 900px !important;
  }
}
@media (max-width: 920px) {
  .db-server-table-modal-size {
    min-width: 800px !important;
  }
}
@media (max-width: 840px) {
  .db-server-table-modal-size {
    min-width: 500px !important;
  }

  .select-server-db-container {
    flex-wrap: wrap;
    justify-content: center;
    display: block;
    gap: 5px;
    /* flex-direction: column; */
  }
  .progress-table-server-lengh {
    min-width: 700px;
  }
}

@media (max-width: 550px) {
  .db-server-table-modal-size {
    min-width: 370px !important;
    max-width: 370px !important;
  }

  .select-server-db-container {
    flex-wrap: wrap;
    /* justify-content: center; */
    gap: 5px;
    /* flex-direction: column; */
  }
  .select-server-db-sec-container {
    display: block;
    gap: 5px;
    /* flex-wrap: wrap;
    justify-content: center;
    gap: 10px; */
  }
  .media-margin {
    margin-bottom: 8px;
  }
}
.archiving-table-header {
  display: flex;
  padding: 12px 24px;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 12px;
  align-self: stretch;
  color: var(--gray-900, #101828);
  font-family: Inter;
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 28px;
  border-bottom: 2px solid var(--shades-desaturate-10, #dadde5);
}
.header-container-border{
  border-bottom: 2px solid var(--shades-desaturate-10, #dadde5);

}

.pagination-container-of-modal {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-width: 400px;
}
.pagination-table-modal{
  width: 290px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 30px
}
.min-width-table-th {
  min-width: 100px;
  color: var(--Text-On-White-Medium-Contrast, #5c5d6a);

  /* Caption/Medium */
  font-family: Ubuntu;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px; /* 166.667% */
  letter-spacing: 0.24px;
  padding: 3px;
}

.min-width-table-th-schedule-setup {
  max-width: 516px;
  color: var(--Text-On-White-Medium-Contrast, #5c5d6a);

  /* Caption/Medium */
  font-family: Ubuntu;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px; /* 166.667% */
  letter-spacing: 0.24px;
}

.font-modal-table-td {
  color: var(--Text-On-White-High-Contrast, #000117);

  /* Caption/Regular */
  font-family: Ubuntu;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 166.667% */
  letter-spacing: 0.24px;
}

.font-modal-table-td.disabled-text {
  color: var(--Text-On-White-High-Contrast, #7b7d8a);
}

.selected-tables-length {
  color: var(--Text-On-White-High-Contrast, #000117);

  /* Caption/Regular */
  font-family: Ubuntu;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 166.667% */
  letter-spacing: 0.24px;
}

.border-item-selected-total {
  width: 1px;
  height: 12px;
  background: #dadde5;
}
.table-header-borders {
  border-top: 1px solid var(--Shades-Desaturate-10, #dadde5);
  border-bottom: 1px solid var(--Shades-Desaturate-10, #dadde5);
  height: 32px;
}

.table-body-ofschedule-container {
  min-width: 100%;
  height: 320px;
  position: relative;
}
.no-atabase-selected-container {
  display: flex;
  justify-content: center;
  width: 100%;
}

.no-atabase-selected-container-text {
  color: var(--Shades-Desaturate-8, #9ba4bc);
  text-align: center;

  /* Paragraph/S/Regular */
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 142.857% */
  width: 197px;
}

.border-table-of-modal {
  background-color: #c6ccdc;
  width: 1px;
  margin-right: 3px;
  height: 24px;
  margin-left: -7px;
}

.container-check-mark {
  display: block;
  position: relative;
  background: #fff;
  padding-left: 22px;
  margin-bottom: 22px;
  cursor: pointer;
  font-size: 22px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Hide the browser's default checkbox */
.container-check-mark input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

/* Create a custom checkbox */
.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 18px;
  width: 18px;
  border-radius: 6px;
  border: 1px solid var(--Shades-Desaturate-10, #dadde5);
  background: var(--dark-light-light, #fff);
}

/* On mouse-over, add a grey background color */
.container-check-mark:hover input ~ .checkmark {
  background-color: #ccc;
}

/* When the checkbox is checked, add a blue background */
.container-check-mark input:checked ~ .checkmark {
  background-color: black;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

/* Show the checkmark when checked */
.container-check-mark input:checked ~ .checkmark:after {
  display: block;
}

/* Style the checkmark/indicator */
.container-check-mark .checkmark:after {
  left: 5px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 3px 3px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}

.server-select-from-drp-dwn option:hover {
  background-color: black !important;
}
.select-styled {
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  color: var(--Text-On-White-High-Contrast, #000117);
  cursor: pointer;
}

.select-styled option:hover {
  background-color: black;
}
.sorting-table-icons{
  height: 20px;
  width: 20px;
  cursor: pointer;
}
