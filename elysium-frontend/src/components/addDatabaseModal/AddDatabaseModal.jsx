import Modal from "react-bootstrap/Modal";
import "./style.css";
import DatabaseModalTable from "./TableDatabaseModal";

function AddDatabaseModal(props) {
  const {show,  onHide} = props
  return (
    <div className="custom-table-modal-container-wrapper">
      <Modal
        show={show}
        onHide={onHide}
        style={{ overflow: "scroll" }}
        aria-labelledby="contained-modal-title-vcenter"
        centered
        dialogClassName="db-server-table-modal-size"
      >
        <DatabaseModalTable modalProps={props} />
      </Modal>
    </div>
  );
}

export default AddDatabaseModal;
