import React, { useState, useMemo, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { MaterialReactTable } from 'material-react-table';
import { Container, Button } from 'react-bootstrap';
import ConfirmationModal from '../confirmationModal/ConfirmationModal';
import TableCustomFilters from '../settingPageTabs/agentTab/TableCustomFilters';
import { serverInfo } from '../../services/serversInfo'
import { dbServer } from '../../services/dbServers'
import Loader from '../loader/Loader';
import { FaPlus } from 'react-icons/fa';
import { IoMdOpen } from "react-icons/io";
import { TbRefresh } from 'react-icons/tb';
import { RiDeleteBin6Line } from 'react-icons/ri';
import { TbPlugConnected } from 'react-icons/tb';
import AddDbServerModal from './AddDbServerModal';
import Notification from '../notificationModal/Notification';
import TerminalModal from '../terminalModal/TerminalModal';
import './style.css';

function DBServersTable() {
  const [open, setOpen] = useState(false);
  const [serverData, setServerData] = useState([]);
  const [rowSelection, setRowSelection] = useState([]);
  const [selectedRowArr, setSelectedRowArr] = useState([]);
  const [selectedRowName, setSelectedRowName] = useState([]);
  const [addModalStatus, setAddModalStatus] = useState(false);
  const [showNotification, setShowNotification] = useState(false);
  const [responseMessage, setResponseMessage] = useState();
  const [remoteServersType, setRemoteServersType] = useState([]);
  const [getDBServerTypes, setGetDBServerTypes] = useState([]);
  const [confirmModal, setConfirmModal] = useState(false);
  const [modalInfo, setModalInfo] = useState(false);
  const searchRef = useRef(null);
  const navigate = useNavigate();

  // Add new state variables for terminal modal
  const [showTerminal, setShowTerminal] = useState(false);
  const [terminalLogs, setTerminalLogs] = useState([]);
  const [terminalTitle, setTerminalTitle] = useState('');
  const [currentDbServerId, setCurrentDbServerId] = useState(null);

  useEffect(() => {
    getSelectedRows();
  }, [rowSelection]);

  useEffect(() => {
    getServersData();
    filterFunc();
    getRemoteServersType();
    getAllDBServerTypes();
  }, []);

  const getRemoteServersType = async () => {
    const response = await serverInfo.agentSeversData('1');
    if (response && response.data) {
      setRemoteServersType(response.data.agents);
    } else {
      setRemoteServersType([]);
    }
  }

  const getAllDBServerTypes = async () => {
    const response = await dbServer.getAllDBServerTypes();
    if (response && response.data) {
      setGetDBServerTypes(response.data.dbServerTypes);
    } else {
      setGetDBServerTypes([]);
    }
  }

  const responseValues = (response) => {
    setAddModalStatus(false);
    setRowSelection([]);
    setShowNotification(true);
    setResponseMessage(response.message);
    getServersData();
  };

  // Add Servers Modal
  const handleAddServersBtn = () => {
    setShowNotification(false);
    setAddModalStatus(true);
    setModalInfo(false);
  }
  // Update Server Information
  const handleInfoServerBtn = () => {
    setShowNotification(false);
    setAddModalStatus(true);
    setModalInfo(() => {
      return true;
    });
  }
  const handleClose = () => {
    setAddModalStatus(false);
  }

  // Get Selected Row Values
  const getSelectedRows = () => {
    const updatedSelectedRowArr = [];
    const updatedSelectedRowArrName = [];
    if (Object.keys(rowSelection).length > 0) {
      Object.keys(rowSelection).forEach((selectedRow) => {
        // Find the corresponding data from tableData
        const foundRow = serverData.find((row) => row.id === parseInt(selectedRow));
        if (foundRow) {
          updatedSelectedRowArr.push(foundRow.id);
          updatedSelectedRowArrName.push(foundRow.hostname);
        }
      });
    }
    setSelectedRowArr(updatedSelectedRowArr);
    setSelectedRowName(updatedSelectedRowArrName);
  }

  useEffect(() => {
    clearSearchField();
  }, [searchRef])
  // Reset All Filters
  const clearSearchField = () => {
    const inputElement = searchRef.current.querySelector('.MuiInputBase-input');
    // Check if the input element exists before setting its value
    if (inputElement) {
      inputElement.value = '';
      searchRef.current.value = '';
    }
  }
  const handleReset = () => {
    setShowNotification(false);
    setSelectedFilterValue('0');
    clearSearchField();
    setRowSelection([]);
    setSelectedRowArr([]);
    getServersData();
  }

  // Get Filtered Data from DB
  const [filtersValue, setFiltersValue] = useState([]);
  const [selectedFilterValue, setSelectedFilterValue] = useState(0);
  const filterFunc = async () => {
    // FETCH Filtering Values
    const response = await serverInfo.agentServerFilters();
    if (response && response.data) {
      setFiltersValue(response.data.remoteServerFilters);
    } else {
      setFiltersValue([]);
    }
  }
  // End Filtering

  // Remove Servers
  const handleRemove = () => {
    setShowNotification(false);
    setConfirmModal(true);
  }
  const handleConfirmModal = async () => {
    setConfirmModal(false);
    setRowSelection([]);
    setOpen(true);
    let payload = {
      ids: selectedRowArr
    };
    const response = await dbServer.deleteDBServer(payload);
    setOpen(false);
    setShowNotification(true);
    setResponseMessage(response.message);
    if (response && response.status === 'Success') {
      getServersData();
    }
  }
  // End Remove Servers

  // Handle test connection
  const handleTestConnection = async (dbServerId, hostname) => {
    setCurrentDbServerId(dbServerId);
    setTerminalTitle(`Testing Connection to Database Server #${dbServerId} (${hostname})`);
    setTerminalLogs([
      { type: 'info', message: `Initializing connection test to ${hostname}...` },
      { type: 'command', message: `Testing SSH tunnel and MySQL connection...` }
    ]);
    setShowTerminal(true);
    
    try {
      // Make the actual API call
      const response = await dbServer.testDBServerConnection(dbServerId);
      
      // Process backend logs if available
      if (response && response.data && response.data.logs) {
        const backendLogs = response.data.logs.map(log => {
          // Determine log type based on content
          let type = 'info';
          if (log.includes('Error') || log.includes('ERROR') || log.includes('failed') || log.includes('WARNING')) {
            type = log.includes('WARNING') ? 'warning' : 'error';
          } else if (log.includes('successful') || log.includes('detected') || log.includes('Success')) {
            type = 'success';
          } else if (log.includes('Executing command:') || log.includes('Attempting') || log.includes('ssh-keygen') || log.includes('Testing')) {
            type = 'command';
          } else if (log.includes('Please run') || log.includes('Troubleshooting') || log.includes('Possible causes')) {
            type = 'warning';
          }
          
          return { type, message: log };
        });
        
        setTerminalLogs(prev => [...prev.slice(0, 2), ...backendLogs]);
      }
      
      // Add final status message
      if (response && response.status === 'Success') {
        setTerminalLogs(prev => [...prev, 
          { type: 'success', message: response.message }
        ]);
      } else {
        setTerminalLogs(prev => [...prev, 
          { type: 'error', message: response.message || 'Connection test failed.' }
        ]);
      }
      
      // Refresh the table data
      getServersData();
    } catch (error) {
      setTerminalLogs(prev => [...prev,
        { type: 'error', message: 'An error occurred during the connection test.' },
        { type: 'error', message: error.message }
      ]);
    }
  };

  const CustomTableToolbar = () => {
    // Get Filtered Data from DB
    const handleFilterSelect = (event) => {
      const selectedValue = event.target.value;
      getServersData(selectedValue);
      setSelectedFilterValue(selectedValue);
      setRowSelection([]);
      setSelectedRowArr([]);
    };

    // Custom Table Headers Options
    return (
      <div className='d-flex justify-content-between ps-4 w-100'>
        <div className='table-header server-table'>
          <button className='square rounded add-agent-btn' onClick={handleReset}><TbRefresh style={{ width: '18px', height: '20px' }} /></button>
          <button className='square rounded add-agent-btn' disabled={selectedRowArr.length > 0 && selectedRowArr.length < 2 ? false : true} onClick={handleInfoServerBtn}><IoMdOpen /> Open</button>
          <button className='square rounded add-agent-btn test-connection-btn' disabled={selectedRowArr.length !== 1} onClick={() => {
            const server = serverData.find(s => s.id === selectedRowArr[0]);
            if (server) {
              handleTestConnection(server.id, server.hostname);
            }
          }}><TbPlugConnected /> Test Connection</button>
          <button className={`square rounded add-agent-btn ${selectedRowArr.length > 0 ? 'red' : ''}`} disabled={selectedRowArr.length > 0 ? false : true} onClick={handleRemove}><RiDeleteBin6Line /> Remove</button>
        </div>
        <div className='table-header search-filter-gap'>
          <TableCustomFilters
            handleFilterSelect={handleFilterSelect}
            filtersValue={filtersValue}
            selectedFilterValue={selectedFilterValue}
          />
        </div>
      </div>
    );
  };

  // Table Headers
  const serverColumns = useMemo(() => [
    {
      accessorKey: 'hostname',
      header: 'Host name',
      selectable: true,
      minSize: '132px',
      muiTableBodyCellProps: ({ cell }) => ({
        onClick: () => {
          navigate(`/database-servers/${cell.getValue()}`, { state: { id: cell.row.id } });
        },
        sx: {
          cursor: 'pointer',
          color: '#174BE6',
          textDecoration: 'underline'
        },
      }),
    },
    {
      accessorFn: (row) => row.remote_server_name,
      remote_server_name: 'remote_server_name',
      header: 'Agent',
      minSize: '132px',
      fontSize: '10px'
    },
    {
      accessorFn: (row) => row.statusName,
      id: 'status',
      header: 'Status',
      Cell: ({ cell }) => {
        const status = cell.row._valuesCache.status;
        return (
          <div className={`server-status ${status === 'Completed' ? "complete-status" : 'pending-status'}`}>{status}</div>
        )
      }
    },
    {
      accessorFn: (row) => row.created_at,
      created_at: 'created_at',
      header: 'Date uploaded',
      minSize: '132px'
    },
    {
      accessorFn: (row) => row.updated_at,
      updated_at: 'updated_at',
      header: 'Last modified',
      minSize: '132px'
    },
    {
      accessorKey: 'actions',
      header: 'Actions',
      enableSorting: false,
      Cell: ({ row }) => (
        <div className='d-flex gap-2'>
          <button
            className='square rounded add-agent-btn test-connection-btn'
            onClick={() => handleTestConnection(row.original.id, row.original.hostname)}
          >
            <TbPlugConnected /> Test Connection
          </button>
        </div>
      ),
    },
  ], []);

  // FETCH Servers Data
  const getServersData = async (selectedValue) => {
    setOpen(true);
    const response = await dbServer.getAllDBServer(selectedValue);
    setOpen(false);
    if (response && response.data) {
      setServerData(response.data.dbServers);
    } else {
      setServerData([]);
    }
  }

  return (
    <>
      <Loader open={open} />
      <Container className='my-5'>
        <div className='my-4 pb-2 d-flex justify-content-between align-items-center'>
          <h1 className='m-0'>Database servers</h1>
          <Button className='add-server-btn d-flex align-items-center' onClick={handleAddServersBtn}><FaPlus style={{ paddingLeft: '4px' }} /> Add Server</Button>
        </div>
        <AddDbServerModal handleClose={handleClose} addModalStatus={addModalStatus} responseValues={responseValues} selectedRowArr={selectedRowArr}
          remoteServersType={remoteServersType} getDBServerTypes={getDBServerTypes} modalInfo={modalInfo} serverData={serverData} />
        <div className='tab-content square border rounded-4'>
          <div className='tab-heading mb-3 pt-4 px-3'>
            <h2 className='mb-0'>Servers({serverData.length})</h2>
          </div>
          <div>
            <MaterialReactTable
              columns={serverColumns}
              data={serverData}
              enableSorting={true}
              enableHiding={false}
              enableDensityToggle={false}
              enableColumnActions={false}
              enableColumnFilters={false}
              enableFullScreenToggle={false} // Disable full screen View
              enablePagination={true} // Enable pagination
              getRowId={(row) => row.id}
              enableRowSelection={true}
              onRowSelectionChange={setRowSelection}
              state={{ rowSelection }} // Manage your own state, pass it back to the table (optional)
              // For Searching
              enableGlobalFilterModes
              initialState={{
                showGlobalFilter: true,
              }}
              muiSearchTextFieldProps={{
                placeholder: 'Search',
                ref: searchRef,
                sx: () => (
                  {
                    minWidth: '252px',
                    '.MuiInputBase-adornedStart': {
                      padding: '0px 6px',
                      background: '#fff'
                    },
                    'button': {
                      padding: '0px',
                      width: 'auto'
                    },
                    '& input': {
                      padding: '8px 0',
                      fontSize: '15px'
                    },
                  }
                ),
                variant: 'outlined',
              }}
              muiTableHeadCellProps={{
                sx: () => ({
                  fontFamily: "Ubuntu",
                }),
              }}
              muiTopToolbarProps={{
                sx: () => ({
                  '& .MuiBox-root.css-sq9qdz': {
                    flexDirection: 'row-reverse',
                    alignItems: 'center',
                    padding: '16px',
                  }
                }),
              }}
              muiTableBodyProps={{
                sx: () => ({
                  '& tr:nth-of-type(odd)': {
                    backgroundColor: '#f5f8fc',
                  },
                  '& td, & button': {
                    fontFamily: "Ubuntu",
                    fontWeight: "400",
                  }
                }),
              }}
              muiTablePaginationProps={{
                sx: () => ({
                  '& p': {
                    fontFamily: "Ubuntu",
                  },
                })
              }}
              renderTopToolbarCustomActions={() => <CustomTableToolbar />}
            />
          </div>
        </div>
      </Container>
      {showNotification &&
        <Notification responseMessage={responseMessage} />
      }
      {
        confirmModal && (
          <ConfirmationModal buttonValue='Remove'
            showModel={confirmModal}
            onSuccess={handleConfirmModal}
            onClose={() => setConfirmModal(false)}
            title={`Remove DB Server ${selectedRowName} ?`}
            message='Are you sure to remove ?'
          />
        )
      }
      {showTerminal && (
        <TerminalModal
          show={showTerminal}
          onHide={() => setShowTerminal(false)}
          title={terminalTitle}
          logs={terminalLogs}
          agentId={currentDbServerId}
        />
      )}
    </>
  )
}

export default DBServersTable
