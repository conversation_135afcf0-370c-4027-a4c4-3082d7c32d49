.container .add-server-btn,
.container .add-server-btn:hover {
  border-radius: 9px;
  padding: 12px;
  border: 1px solid #000117;
  color: #fff;
  background: #000117;
  gap: 8px;
  font-size: 14px;
  font-style: normal;
  line-height: 16px;
}

.add-agent-btn {
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
  padding: 8px 12px;
  border: 1px solid #DADDE5;
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #fff;
  font-family: Ubuntu;
  font-size: 14px;
  font-weight: 400;
}

/* Fix for disabled button styling */
.add-agent-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.add-agent-btn:not(:disabled) {
  cursor: pointer;
}

/* Add the test connection button styles */
.test-connection-btn {
  color: #174BE6;
  border-color: #174BE6;
  transition: all 0.3s ease;
}

.test-connection-btn:hover {
  background-color: #174BE6;
  color: white;
}

.test-connection-btn svg {
  margin-right: 4px;
}

.server-table .tab-content .form-select {
  padding: 8px;
}

.add-server-modal h3 {
  font-size: 20px;
  font-weight: 700;
}

.add-server-modal .btn-agent {
  border-radius: 8px;
  border-color: #000117;
}

.add-server-modal .btn-add-agent {
  background: #000117;
}

.add-server-modal .btn-cancel-agent {
  background: #fff;
  color: #000117;
}
