import React, { useState, useEffect } from 'react'
import { Modal, Form, Button, InputGroup, FormControl, Alert } from 'react-bootstrap';
import { useFormik } from 'formik';
import { v4 as uuidv4 } from 'uuid';
import { addDbServerValidation } from '../../utils/addDbServerValidation';
import { dbServer } from '../../services/dbServers'
import Loader from '../loader/Loader';

function AddDbServerModal({
  addModalStatus,
  handleClose,
  responseValues,
  remoteServersType,
  getDBServerTypes,
  modalInfo,
  serverData,
  selectedRowArr
}) {
  const [open, setOpen] = useState(false);
  const [errorResponse, setErrorResponse] = useState([]);
  const [createUUID, setCreateUUID] = useState('');

  // Reset the form when the modal status changes
  useEffect(() => {
    formik.resetForm();
    setCreateUUID(uuidv4().replace(/-/g, ""));
    setErrorResponse('');
    if (modalInfo) {
      getServerInfo();
    }
  }, [addModalStatus]);

  const setServerData = ((serverData) => {
    formik.setValues({
      servername: serverData.db_server_name,
      hostname: serverData.hostname,
      username: serverData.username,
      password: serverData.password,
      uuid: serverData.client_db_server_uuid,
      port: serverData.port,
      type: serverData.client_db_server_type_id,
      remoteserver: serverData.remote_server_id,
    });
  });
  const getServerInfo = () => {
    for (const row of serverData) {
      if (row.id === parseInt(selectedRowArr)) {
        setServerData(row);
        break;
      }
    }
  }

  // Initialize Formik Values
  const initialValues = {
    servername: '',
    hostname: '',
    username: '',
    password: '',
    uuid: createUUID,
    port: '',
    type: '',
    remoteserver: ''
  };

  const formik = useFormik({
    initialValues,
    validationSchema: addDbServerValidation,
    validateOnChange: true,
    validateOnBlur: false,
    onSubmit: async (values) => {
      setErrorResponse('');
      setOpen(true);
      let payload = {
        db_server_name: values.servername,
        hostname: values.hostname,
        username: values.username,
        password: values.password,
        client_db_server_uuid: initialValues.uuid,
        port: values.port,
        client_db_server_type_id: values.type,
        remote_server_id: values.remoteserver,
        is_active: 1,
      }
      let url = modalInfo ? `updateDBServer/${selectedRowArr}` : 'createDBServer';
      const response = modalInfo ? await dbServer.addDBServer(url, payload) : await dbServer.addDBServer(url, payload)
      setOpen(false);
      if (response.status === 'Success') {
        responseValues(response);
      } else {
        const res = response.messages;
        setErrorResponse(res);
      }
    }
  });

  return (
    <>
      <Loader open={open} />
      <Modal show={addModalStatus} onHide={handleClose} className='add-server-modal'>
        <div className='p-4'>
          <h3 className='text-left mb-4'>{modalInfo ? 'Update Server' : 'Add Server'}</h3>
          {errorResponse !== '' && (
            <Alert variant="danger" className="mb-0" style={{ fontSize: '14px' }}>
              {errorResponse && errorResponse.map((res, index) => (
                <div key={index}>{res}</div>
              ))}
            </Alert>
          )}
          <div className='tab-content position-relative'>
            <Form onSubmit={formik.handleSubmit} className='d-block' autoComplete="off">
              <div className='pb-2 mb-1'>
                <Form.Group controlId="remoteserver">
                  <Form.Select
                    value={formik.values.remoteserver}
                    className='signup-dropdown-field'
                    name='remoteserver'
                    onChange={formik.handleChange}
                  >
                    <option >Select Remote Server</option>
                    {
                      remoteServersType.map((data) => (
                        <option key={data.agentID} value={data.agentID}>{data.name}</option>
                      ))
                    }
                  </Form.Select>
                  {formik.errors.remoteserver && formik.touched.remoteserver && (
                    <div className="mt-1 p-1 error-text">
                      {formik.errors.remoteserver}
                    </div>
                  )}
                </Form.Group>
              </div>
              <div className='pb-2 mb-1'>
                <Form.Group controlId="servername">
                  <InputGroup className='signup-pwd-field'>
                    <FormControl
                      type='text'
                      placeholder='Server Name'
                      name='servername'
                      value={formik.values.servername}
                      onChange={formik.handleChange}
                      className='pwd-field-bg'
                    />
                  </InputGroup>
                  {formik.errors.servername && formik.touched.servername &&
                    <div className="p-1 error-text" >
                      {formik.errors.servername}
                    </div>
                  }
                </Form.Group>
              </div>
              <div className='pb-2 mb-1'>
                <Form.Group controlId="hostname">
                  <InputGroup className='signup-pwd-field'>
                    <FormControl
                      type='text'
                      placeholder='Host Name'
                      name='hostname'
                      value={formik.values.hostname}
                      onChange={formik.handleChange}
                      className='pwd-field-bg'
                    />
                  </InputGroup>
                  {formik.errors.hostname && formik.touched.hostname &&
                    <div className="p-1 error-text" >
                      {formik.errors.hostname}
                    </div>
                  }
                </Form.Group>
              </div>
              <div className='pb-2 mb-1'>
                <Form.Group controlId="username">
                  <InputGroup className='signup-pwd-field'>
                    <FormControl
                      type='text'
                      placeholder='User Name'
                      name='username'
                      value={formik.values.username}
                      onChange={formik.handleChange}
                      className='pwd-field-bg'
                      autoComplete="new-user"
                    />
                  </InputGroup>
                  {formik.errors.username && formik.touched.username &&
                    <div className="p-1 error-text" >
                      {formik.errors.username}
                    </div>
                  }
                </Form.Group>
              </div>
              <div className='pb-2 mb-1'>
                <Form.Group controlId="password">
                  <InputGroup>
                    <FormControl
                      type='password'
                      placeholder='Password'
                      name='password'
                      value={formik.values.password}
                      onChange={formik.handleChange}
                      className='pwd-field-bg'
                      autoComplete="new-password"
                    />
                  </InputGroup>
                  {formik.errors.password && formik.touched.password &&
                    <div className="p-1 error-text" >
                      {formik.errors.password}
                    </div>
                  }
                </Form.Group>
              </div>
              <div className='pb-2 mb-1'>
                <Form.Group controlId="port">
                  <InputGroup>
                    <FormControl
                      type='text'
                      placeholder='Port'
                      name='port'
                      value={formik.values.port}
                      onChange={formik.handleChange}
                      className='pwd-field-bg'
                    />
                  </InputGroup>
                  {formik.errors.port && formik.touched.port &&
                    <div className="p-1 error-text" >
                      {formik.errors.port}
                    </div>
                  }
                </Form.Group>
              </div>
              <div className='pb-2 mb-1 d-none'>
                <Form.Group controlId="uuid">
                  <InputGroup className='read-only'>
                    <FormControl
                      type='text'
                      placeholder='UUID'
                      name='uuid'
                      value={initialValues.uuid}
                      readOnly
                    />

                  </InputGroup>
                </Form.Group>
              </div>
              <Form.Group controlId="type">
                <Form.Select
                  value={formik.values.type}
                  className='signup-dropdown-field'
                  name='type'
                  onChange={formik.handleChange}
                >
                  <option >Type</option>
                  {
                    getDBServerTypes.map((data) => (

                      <option key={data.id} value={data.id}>{data.name}</option>
                    ))
                  }
                </Form.Select>
                {formik.errors.type && formik.touched.type && (
                  <div className="mt-1 p-1 error-text">
                    {formik.errors.type}
                  </div>
                )}
              </Form.Group>
              <div className='mt-4 text-end'>
                <Button className='btn-agent btn-add-agent me-3' type="submit"> {modalInfo ? 'Update' : ' Add'} </Button>
                <Button className='btn-agent btn-cancel-agent' onClick={handleClose}>
                  Cancel
                </Button>
              </div>
            </Form>
          </div>
        </div >
      </Modal >
    </>
  )
}

export default AddDbServerModal
