import React, { useState, useMemo, useRef, useEffect } from 'react';
import { MaterialReactTable } from 'material-react-table';
import { Box } from '@mui/material';
import { Container } from 'react-bootstrap';
import { useLocation } from 'react-router-dom';
// import ConfirmationModal from '../confirmationModal/ConfirmationModal';
import Loader from '../loader/Loader';
import { HiOutlineArchive } from "react-icons/hi";
import { CiViewTable } from "react-icons/ci";
import { dbServer } from '../../services/dbServers'
import BreadCrumbs from '../breadCrumbs/BreadCrumbs';
import './style.css';

function DBTables() {
  const [open, setOpen] = useState(false);
  const [rowSelection, setRowSelection] = useState([]);
  const [selectedRowArr, setSelectedRowArr] = useState([]);
  const [dbTableData, setDBTableData] = useState([]);
  const [serverName, setServerName] = useState();
  // const [showNotification, setShowNotification] = useState(false);
  const searchRef = useRef(null);
  const location = useLocation();

  useEffect(() => {
    getServerName();
    getDBTableData();
  }, [])
  useEffect(() => {
    getSelectedRows();
  }, [rowSelection]);

  // Get DB Name from Params
  const getServerName = () => {
    const { pathname } = location;
    const segments = pathname.split('/');
    setServerName(decodeURIComponent(segments.slice(-1)[0]));
  }

  // Get Selected Row Values
  const getSelectedRows = () => {
    const updatedSelectedRowArr = [];
    if (Object.keys(rowSelection).length > 0) {
      Object.keys(rowSelection).forEach((selectedRow) => {
        // Find the corresponding data from tableData
        const foundRow = dbTableData.find((row) => row.id === parseInt(selectedRow));
        if (foundRow) {
          updatedSelectedRowArr.push(foundRow.id);
        }
      });
    }
    setSelectedRowArr(updatedSelectedRowArr);
  }

  // DataBase Archive
  const handleArchive = () => {
    console.log("Archive DB");
  }

  const CustomTableToolbar = () => {
    // Custom Table Headers Options
    return (
      <div className='d-flex justify-content-between ps-4 w-100'>
        <div className='table-header server-table'>
          <button className='square rounded add-agent-btn' disabled={selectedRowArr && selectedRowArr.length > 0 ? false : true} onClick={handleArchive}><HiOutlineArchive /> Archive</button>
        </div>
      </div>
    );
  };

  // Table Headers
  const tableColumns = useMemo(() => [
    {
      accessorKey: 'table_name',
      header: 'Table name',
      selectable: true,
      minSize: '132px',
      Cell: ({ renderedCellValue, row }) => (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
          }}
        >
          <CiViewTable style={{ width: '18px', height: '18px' }} />
          <span>
            {renderedCellValue}
            {row.original.is_dropped === 1 && <span className="ms-2 text-danger">(dropped)</span>}
          </span>
        </Box>
      ),
    },
    {
      accessorFn: (row) => row.retention,
      retention: 'retention',
      header: 'Retention',
      fontSize: '10px'
    },
    {
      accessorFn: (row) => row.partition,
      partition: 'partition',
      header: 'Partition',
    },
    {
      accessorFn: (row) => row.total_current_table_rows,
      total_current_table_rows: 'Total Rows',
      header: 'Total Rows',
    },
    {
      accessorFn: (row) => row.batchsize,
      batchsize: 'Batch size',
      header: 'Batch size',
    },
    {
      accessorFn: (row) => row.archivestatus,
      archivestatus: 'Archive status',
      header: 'Archive status',
    }
  ], []);

  // FETCH Servers Data
  const getDBTableData = async () => {
    setOpen(true);
    let payload = '';
    if (location.state && location.state.client_db_schema_id) {
      payload = {
        client_db_schema_id: location.state.client_db_schema_id
      }
    }
    const response = await dbServer.getAllDatabasesTablesList(payload);
    setOpen(false);
    if (response && response.data) {
      setDBTableData(response.data.dbTables);
    } else {
      setDBTableData([]);
    }
  }

  return (
    <>
      <Loader open={open} />
      <Container className='my-5'>
        <div className='my-4'>
          <div className='mb-2 pb-1 text-capitalize'>
            <BreadCrumbs />
          </div>
          <h1 className='mb-4 text-capitalize'>{serverName}</h1>
        </div>
        <div className='tab-content square border rounded-4'>
          <div className='tab-heading mb-3 pt-4 px-3'>
            <h2 className='mb-0'>Tables ({dbTableData.length})</h2>
          </div>
          <div>
            <MaterialReactTable
              columns={tableColumns}
              data={dbTableData}
              enableSorting={true}
              enableHiding={false}
              enableDensityToggle={false}
              enableColumnActions={false}
              enableColumnFilters={false}
              enableFullScreenToggle={false} // Disable full screen View
              enablePagination={true} // Enable pagination
              getRowId={(row) => row.id}
              // enableRowSelection={true}
              onRowSelectionChange={setRowSelection}
              state={{ rowSelection }} // Manage your own state, pass it back to the table (optional)
              enableRowSelection={(row) => row.original.is_dropped !== 1}
              muiTableBodyRowProps={({ row }) => ({
                className: row.original.is_dropped === 1 ? 'bg-warning' : '',
                sx: {
                  backgroundColor: row.original.is_dropped === 1 ? '#f8d7da' : '',
                }
              })}
              // For Searching
              enableGlobalFilterModes
              initialState={{
                showGlobalFilter: true,
              }}
              muiSearchTextFieldProps={{
                placeholder: 'Search',
                ref: searchRef,
                sx: () => (
                  {
                    minWidth: '252px',
                    '.MuiInputBase-adornedStart': {
                      padding: '0px 6px',
                      background: '#fff'
                    },
                    'button': {
                      padding: '0px',
                      width: 'auto'
                    },
                    '& input': {
                      padding: '8px 0',
                      fontSize: '15px'
                    },
                  }
                ),
                variant: 'outlined',
              }}
              muiTableHeadCellProps={{
                sx: () => ({
                  fontFamily: "Ubuntu",
                }),
              }}
              muiTopToolbarProps={{
                sx: () => ({
                  '& .MuiBox-root.css-sq9qdz': {
                    flexDirection: 'row-reverse',
                    alignItems: 'center',
                    padding: '16px',
                  }
                }),
              }}
              muiTableBodyProps={{
                sx: () => ({
                  '& tr:nth-of-type(odd):not(.alert)': {
                    backgroundColor: '#f5f8fc',
                  },
                  '& td, & button': {
                    fontFamily: "Ubuntu",
                    fontWeight: "400",
                  }
                }),
              }}
              muiTablePaginationProps={{
                sx: () => ({
                  '& p': {
                    fontFamily: "Ubuntu",
                  },
                })
              }}
              renderTopToolbarCustomActions={() => <CustomTableToolbar />}
            />
          </div>
        </div>
      </Container>
    </>
  )
}

export default DBTables
