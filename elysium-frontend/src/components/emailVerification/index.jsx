import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from 'react-router';
import { Link } from 'react-router-dom';
import { Container, Row, Col, Button } from 'react-bootstrap';
import OtpInput from 'react-otp-input';
import { CiCircleRemove, CiUndo } from 'react-icons/ci';
import Sidebar from '../../pages/sidebar/Sidebar';
import { serviceCall } from '../../services/emailVerify'
import Loader from '../loader/Loader';
import "./style.css"


function Index() {
  const [open, setOpen] = useState(false);
  const [otp, setOtp] = useState('');
  const [timer, setTimer] = useState(60);
  const [invalidFlag, setInValidFlag] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const navigate = useNavigate();
  const location = useLocation();
  const navigationFlag = location.state.navigationFlag
  const userEmail = location.state.email

  useEffect(() => {
    let intervalId;
    if (timer !== 0) {
      intervalId = setInterval(() => {
        setTimer(prevTimer => prevTimer - 1);
      }, 1000);
    }
    return () => clearInterval(intervalId);
  }, [timer]);

  const getEmailOtp = async () => {
    await serviceCall.getOtp(userEmail)
  }

  const handleVerifyEmail = async () => {
    setOpen(true);
    const verifyObj = { email: userEmail, otp };
    const response = await serviceCall.emailVerify(verifyObj);
    setOpen(false);
    if (response.status === "Success") {
      navigationFlag === true ? navigate('/resetPassword', { state: { email: userEmail } }) :
        navigate('/priceplan');
    }
    setInValidFlag(true);
    setErrorMessage(response.message);
    setOtp('');
  }
  const handleResendCode = () => {
    setOtp('')
    setInValidFlag(false)
    getEmailOtp()
    setTimer(60)
  }
  return (
    <Container fluid>
      <Loader open={open} />
      <Row>
        <Col sm={12} md={4} lg={3} className='sidebar-wrapper'>
          <Sidebar />
        </Col>
        <Col sm={12} md={8} lg={9}>
          {!navigationFlag &&
            <div className='signin-form-section'>
              <p className='have-account-text'> Already have an account? </p>
              <Link to="/" className='have-account-link'>
                <button className='signin-nav-btn'> Sign In </button>
              </Link>
            </div>

          }
          <div className='d-flex align-items-center justify-content-center h-100'>
            <div>
              <h1>Verify your email</h1>
              <p>We’re sending an email to {userEmail} with a code. <br></br>
                Please enter the code below</p>
              <OtpInput
                value={otp}
                onChange={setOtp}
                numInputs={6}
                inputType="number"
                renderSeparator={<span style={{ width: "8px" }}></span>}
                renderInput={(props) => <input {...props} />}
                inputStyle={{
                  border: "1px solid #000117",
                  borderRadius: "8px",
                  width: "44px",
                  height: "48px",
                  fontSize: "18px",
                  color: "#000117",
                  fontWeight: "400"
                }}
              />
              <div className='mt-12'>
                {invalidFlag ?
                  <span className='margin-right' style={{ color: '#EF0C0C' }}>
                    <CiCircleRemove className='circle-remove' />
                    &nbsp;{errorMessage}
                  </span> : ''
                }
              </div>
              <div className='mt-12'>
                {timer === 0 ?
                  <span onClick={handleResendCode} className='resend-button'> Resend code  <CiUndo style={{ fontSize: 16 }} />
                  </span>
                  :
                  <>
                    < span > Resend code in:</span> <span className='time-counts'>{timer}s</span>
                  </>
                }
              </div>
              <Button className='verify-btn' disabled={otp.length === 6 ? false : true} type="submit" onClick={handleVerifyEmail}>
                Verify email
              </Button>
            </div>
          </div>
        </Col>
      </Row>
    </Container >
  )
}

export default Index