import React, { useState, useEffect } from 'react';
import { updateUserInfo } from '../../services/updateProfileInfo';
import { baseUrl } from '../../config/constants';
import { styled } from '@mui/material/styles';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell, { tableCellClasses } from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import SaveAltIcon from '@mui/icons-material/SaveAlt';
import { Button } from 'react-bootstrap';
import { red } from '@mui/material/colors';

function PaymentHistoryTable() {

  const [invoices, setInvoices] = useState([]);

  useEffect(() => {
    getInvoices();
  }, [])

  const getInvoices = async () => {
    const response = await updateUserInfo.getInvoices();
    if (response.status === 'Success' && response.data) {
      setInvoices(response.data.invoices);
    } else {
      setInvoices([]);
    }
  }

  const StyledTableCell = styled(TableCell)(({ theme }) => ({
    [`&.${tableCellClasses.head}`]: {
      backgroundColor: theme.palette.common.white,
      color: '#5C5D6A',
      fontFamily: 'Ubuntu',
      fontSize: 12,
    },
    [`&.${tableCellClasses.body}`]: {
      fontSize: 14,
      paddingTop: 6,
      paddingBottom: 6,
      paddingRight: 16,
      paddingLeft: 16,
      fontFamily: 'Ubuntu',
    },
  }));

  const StyledTableRow = styled(TableRow)(({ theme }) => ({
    '&:nth-of-type(odd)': {
      backgroundColor: theme.palette.action.hover,
    },
    '&:last-child td, &:last-child th': {
      border: 0,
    },
  }));

  return (
    <>
      {invoices.length > 0 ?
        <TableContainer component={Paper}>
          <Table aria-label="customized table">
            <TableHead>
              <TableRow>
                <StyledTableCell>Date</StyledTableCell>
                <StyledTableCell>Plan</StyledTableCell>
                <StyledTableCell>Amount</StyledTableCell>
                <StyledTableCell>Status</StyledTableCell>
                <StyledTableCell align='center'>Action</StyledTableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {invoices.map((invoice, i) => (
                <StyledTableRow key={i}>
                  <StyledTableCell component="th" scope="row">
                    {invoice.date}
                  </StyledTableCell>
                  <StyledTableCell>{invoice.plan}</StyledTableCell>
                  <StyledTableCell>{invoice.amount}</StyledTableCell>
                  <StyledTableCell className={invoice.status === 'Paid' ? "green" : red}>{invoice.status}</StyledTableCell>
                  <StyledTableCell align="right">
                    <a href={`${baseUrl}/${invoice.url}`} > <Button className='download-btn' download> <SaveAltIcon className='me-2' />Download</Button></a>
                  </StyledTableCell>
                </StyledTableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        : ''}
    </>
  );
}

export default PaymentHistoryTable