import React, { useState, useEffect } from 'react'
import { Form, Button, Col } from 'react-bootstrap';
import { useFormik } from "formik";
import { updateProfileValidation } from '../../utils/updateProfileValidation'
import { serviceCall } from '../../services/signUp'
import { updateUserInfo } from '../../services/updateProfileInfo'
import Notification from '../notificationModal/Notification';
import Loader from '../loader/Loader';
import { imageUrl } from '../../config/constants';
import { useDispatch } from 'react-redux';
import { updateProfileImage } from '../../redux/features/UserInformation';

function ProfileSettings() {
  const [country, setCountry] = useState([]);
  const [timezone, setTimezone] = useState([]);
  const [file, setFile] = useState();
  const [isFormChanged, setIsFormChanged] = useState(false);
  const [open, setOpen] = useState(false);
  const [isError, setIsError] = useState();
  const [responseMessage, setResponseMessage] = useState();
  const [showNotification, setShowNotification] = useState(false);
  const [userName, setUserName] = useState();
  const [globalUserImg, setGlobalUserImg] = useState();
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(updateProfileImage(globalUserImg));
  }, [globalUserImg]);

  useEffect(() => {
    getAllCountries();
    getTimezone();
    getUserInfo();
  }, []);

  const getUserInfo = async () => {
    setOpen(true);
    const response = await updateUserInfo.userInfo();
    if (response && response.data && response.data.user) {
      setOpen(false);
      const userData = response.data.user;
      formik.setValues({
        firstName: userData.first_name,
        lastName: userData.last_name,
        companyName: userData.company,
        countryId: userData.country_id,
        email: userData.email,
        timezoneId: userData.timezone_id,
        userImage: userData.image,
      });
      let profileImg = userData.image_path;
      profileImg != null ? setFile(`${imageUrl}${profileImg}`) : setFile('');
      profileImg != null ? setGlobalUserImg(profileImg) : setGlobalUserImg('');
      setUserName(userData.first_name.charAt(0) + userData.last_name.charAt(0));
    } else {
      setOpen(false);
      setShowNotification(true);
      setIsError(true);
      setResponseMessage(response)
    }
  }

  const getAllCountries = async () => {
    const response = await serviceCall.getCountry()
    if (response && response.data) {
      setCountry(response.data.countries)
    } else {
      setShowNotification(true);
      setIsError(true);
      setResponseMessage(response)
    }
  }

  const getTimezone = async () => {
    const response = await updateUserInfo.getTimezone();
    if (response && response.data) {
      setTimezone(response.data.timezones)
    } else {
      setShowNotification(true);
      setIsError(true);
      setResponseMessage(response)
    }
  }

  const removeUserImage = () => {
    setFile('');
    setGlobalUserImg('');
  }

  const handleUserImage = (e) => {
    setFile(URL.createObjectURL(e.target.files[0]));
  }

  const initialValues = {
    firstName: '',
    lastName: '',
    companyName: '',
    countryId: '',
    email: '',
    timezoneId: '',
    userImage: '',
    isImageRemoved: ''
  };

  const formik = useFormik({
    initialValues,
    validationSchema: updateProfileValidation,
    validateOnChange: true,
    validateOnBlur: false,
    onSubmit: async (values, action) => {
      setOpen(true);
      setShowNotification(false);
      let formData = new FormData();
      formData.append("first_name", values.firstName);
      formData.append("last_name", values.lastName);
      formData.append("email", values.email);
      formData.append("company_name", values.companyName);
      formData.append("country_id", values.countryId);
      formData.append("timezone_id", values.timezoneId);
      if (values.userImage) {
        formData.append("image", values.userImage);
      }
      file !== '' ? formData.append("isImageRemoved", true) : formData.append("isImageRemoved", false);
      const response = await updateUserInfo.updateProfileData('/updateUserProfile', formData);
      if (response.status === 'Success') {
        setOpen(false)
        setShowNotification(true);
        setIsError(false);
        setResponseMessage(response.message);
        getUserInfo();
      }
      else {
        setOpen(false)
        setShowNotification(true);
        setIsError(true);
        setResponseMessage(response.messages);
      }
    },
  });

  useEffect(() => {
    const hasNonEmptyField = Object.values(formik.values).some((value) => value !== '');
    setIsFormChanged(hasNonEmptyField);
  }, [formik.values]);

  const handleCancel = () => {
    formik.resetForm();
  };
  return (
    <>
      <Loader open={open} />
      <div className='tab-content'>
        <div className='tab-heading mb-3 pb-1'>
          <h2 className='mb-0'>Personal info</h2>
          <p className='mt-1 mb-0'>Update your personal details here.</p>
        </div>
        <Form onSubmit={formik.handleSubmit} className='d-block'>
          <div className='py-4 square border-top d-flex justify-content-left'>
            <Col className='p-0' xs md='3'>
              <Form.Label>Name</Form.Label>
            </Col>
            <Col className='pe-0' xs md="3">
              <Form.Group controlId="firstName" className='px-3'>
                <Form.Control
                  type="text"
                  className='signup-name-filed'
                  name='firstName'
                  placeholder=" First name"
                  value={formik.values.firstName}
                  onChange={formik.handleChange}
                />
                {formik.errors.firstName && formik.touched.firstName &&
                  <div className="mt-1 p-1 error-text" >
                    {formik.errors.firstName}
                  </div>
                }
              </Form.Group>
            </Col>
            <Col xs md="3">
              <Form.Group controlId="lastName" className='pe-4'>
                <Form.Control
                  type="text"
                  className='signup-name-filed'
                  placeholder="Last name"
                  name='lastName'
                  value={formik.values.lastName}
                  onChange={formik.handleChange}
                />
                {formik.errors.lastName && formik.touched.lastName &&
                  <div className="mt-1 p-1 error-text" >
                    {formik.errors.lastName}
                  </div>
                }
              </Form.Group>
            </Col>
          </div>
          <div className='py-4 square border-top d-flex justify-content-left'>
            <Col className='p-0' xs md='3'>
              <Form.Label>Email address</Form.Label>
            </Col>
            <Col xs md="6">
              <Form.Group controlId="email" className='ps-3 pe-4'>
                <Form.Control
                  type="email"
                  placeholder="Email"
                  className='signup-field'
                  name='email'
                  value={formik.values.email}
                  onChange={formik.handleChange}
                />
                {formik.errors.email && formik.touched.email &&
                  <div className="mt-1 p-1 error-text">
                    {formik.errors.email}
                  </div>
                }
              </Form.Group>
            </Col>
          </div>

          <div className='py-4 square border-top d-flex justify-content-left'>
            <Col className='p-0' xs md='3'>
              <Form.Label>Company name</Form.Label>
            </Col>
            <Col xs md="6">
              <Form.Group controlId="companyName" className='ps-3 pe-4'>
                <Form.Control
                  type="text"
                  className='signup-field'
                  placeholder="Company name"
                  name='companyName'
                  value={formik.values.companyName}
                  onChange={formik.handleChange}
                />
                {formik.errors.companyName && formik.touched.companyName &&
                  <div className="mt-1 p-1 error-text">
                    {formik.errors.companyName}
                  </div>
                }
              </Form.Group>
            </Col>
          </div>
          {/* Upload image Functionality */}
          <div className='py-4 square border-top d-flex justify-content-left'>
            <Col className='p-0' xs md='3'>
              <Form.Label>Your photo <br /> Formats: png, jpg, gif. Max size: 1 MB.</Form.Label>
            </Col>
            <Col xs md="6" className='ps-3 pe-4'>
              <Col className='p-0 upload-user-image d-flex align-items-center'>
                {file ?
                  <img className='me-3'
                    alt='User'
                    src={file}
                  /> :
                  <p className='username d-flex align-items-center justify-content-center m-0 me-3'> {userName} </p>
                }
                <Form.Group controlId="userImage">
                  <label className='upload-button ms-1'> Upload Image
                    <input type='file' name="userImage" onChange={(e) => {
                      handleUserImage(e)
                      formik.setFieldValue("userImage", e.currentTarget.files[0])
                    }} value={formik.values.file} />
                  </label>
                </Form.Group>
                {file &&
                  <Button className='upload-button remove-button ms-2' onClick={removeUserImage} > Remove
                  </Button>}
              </Col>
              {formik.errors.userImage && formik.touched.userImage &&
                <div className="mt-1 p-1 error-text">
                  {formik.errors.userImage}
                </div>
              }
            </Col>
          </div>

          <div className='py-4 square border-top d-flex justify-content-left'>
            <Col className='p-0' xs md='3'>
              <Form.Label>Country</Form.Label>
            </Col>
            <Col xs md="6">
              <Form.Group controlId="countryId" className='ps-3 pe-4' >
                <Form.Select
                  value={formik.values.countryId}
                  className='signup-dropdown-field'
                  name='countryId'
                  onChange={formik.handleChange}
                >
                  <option >Country</option>
                  {
                    country.map((data) => (
                      <option key={data.id} value={data.id}>{data.name}</option>
                    ))
                  }
                </Form.Select>
                {formik.errors.countryId && formik.touched.countryId &&
                  <div className="mt-1 p-1 error-text">
                    {formik.errors.countryId}
                  </div>
                }
              </Form.Group>
            </Col>
          </div>
          <div className='py-4 square border-top d-flex justify-content-left'>
            <Col className='p-0' xs md='3'>
              <Form.Label>Timezone</Form.Label>
            </Col>
            <Col xs md="6">
              <Form.Group controlId="timezoneId" className='ps-3 pe-4' >
                <Form.Select
                  value={formik.values.timezoneId}
                  className='signup-dropdown-field'
                  name='timezoneId'
                  onChange={formik.handleChange}
                >
                  <option >Time Zone</option>
                  {
                    timezone.map((data) => (
                      <option key={data.id} value={data.id}>{data.name}</option>
                    ))
                  }
                </Form.Select>
                {formik.errors.timezoneId && formik.touched.timezoneId &&
                  <div className="mt-1 p-1 error-text">
                    {formik.errors.timezoneId}
                  </div>
                }
              </Form.Group>
            </Col>
          </div>
          <div className='mt-5 d-flex'>
            <Button className='me-2 save-btn' type="submit">
              Save
            </Button>
            <div className={`save-btn cancel-btn ${isFormChanged ? 'enabled' : ''}`} onClick={handleCancel}>
              Cancel
            </div>
          </div>
        </Form >
        {showNotification &&
          <Notification responseMessage={responseMessage} isError={isError} />
        }
      </div >
    </>
  )
}

export default ProfileSettings
