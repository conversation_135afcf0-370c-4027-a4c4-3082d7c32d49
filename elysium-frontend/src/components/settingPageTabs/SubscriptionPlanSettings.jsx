import React, { useEffect, useState } from 'react'
import { Card, Button, Col, Modal } from 'react-bootstrap';
import PaymentHistoryTable from './PaymentHistoryTable';
import UpgradePricePlanModal from './UpgradePricePlanModal';
import UpgradePaymentCheckout from './UpgradePaymentCheckout';
import { updateUserInfo } from '../../services/updateProfileInfo'
import Notification from '../notificationModal/Notification';
import UpdateBankDetailForm from '../payment/UpdateBankDetailForm'
import {
  FaCcVisa, FaCcMastercard, FaCcPaypal, FaPlus, FaCcAmex,
  FaCcDiscover, FaCcDinersClub, FaCcJcb
} from 'react-icons/fa';
import unionPayIcon from '../../assets/images/unionpay.svg';
import Loader from '../loader/Loader';
import { useNavigate } from 'react-router-dom';

function SubscriptionPlanSettings() {
  const navigate = useNavigate();
  const [subscriptionModal, setSubscriptionModal] = useState(false);
  const [currentPlan, setCurrentPlan] = useState([]);
  const [cardInfo, setCardInfo] = useState([]);
  const [userCurrentPlanId, setUserCurrentPlanId] = useState();
  const [upgradePaymentModal, setUpgradePaymentModal] = useState(false)
  const [priceObj, setPriceObj] = useState();
  const [showNotification, setShowNotification] = useState(false);
  const [isError, setIsError] = useState();
  const [responseMessage, setResponseMessage] = useState();
  const [open, setOpen] = useState(false);
  const [updateBankDetailModal, setUpdateBankDetailModal] = useState(false);
  const [updateInvoiceTable, setUpdateInvoiceTable] = useState(true);

  const handleClose = () => setSubscriptionModal(false);
  const handleShow = () => setSubscriptionModal(true);

  const handleUpdatePaymentModalClose = () => setUpdateBankDetailModal(false);

  const handlePaymentModalClose = () => setUpgradePaymentModal(false);

  useEffect(() => {
    userCurrentPlan();
    userCardInfo();
  }, [])

  const userCurrentPlan = async () => {
    setOpen(true);
    const response = await updateUserInfo.userCurrentPlan();
    setOpen(false);
    if (response && response.data) {
      setCurrentPlan(response.data.plan)
      setUserCurrentPlanId(response.data.plan.plan_id);
    } else {
      setShowNotification(true);
      setIsError(true);
      setResponseMessage(response);
    }
  }

  const userCardInfo = async () => {
    const response = await updateUserInfo.userCardInfo();
    if (response && response.data) {
      setCardInfo(response.data.cards);
    } else {
      setShowNotification(true);
      setIsError(true);
      setResponseMessage(response);
    }
  }

  const handleContinueToPayment = (title, price, stripe_plan, id, tag, plan_price_end_date) => {
    setSubscriptionModal(false);
    setUpgradePaymentModal(true);
    setPriceObj({
      title,
      price,
      stripe_plan,
      id,
      tag,
      plan_price_end_date
    });
  }

  const handleChoosePlan = () => {
    setSubscriptionModal(true);
    setUpgradePaymentModal(false);
  }

  const handleUpgradePayment = async (plan_id) => {
    setShowNotification(false);
    setUpgradePaymentModal(false);
    setOpen(true);
    setUpdateInvoiceTable(false);
    const response = await updateUserInfo.upgradePlan(plan_id);
    setShowNotification(true);
    setOpen(false);
    if (response.status === 'Success') {
      setIsError(false);
      userCurrentPlan();
      setResponseMessage(response.message);
      setUpdateInvoiceTable(true);
    } else {
      setIsError(true);
      setResponseMessage(response);
    }
  }

  const handleCancelSubscription = async () => {
    setOpen(true);
    setShowNotification(false);
    const response = await updateUserInfo.cancelSubscription();
    setOpen(false);
    setShowNotification(true);
    userCurrentPlan();
    userCardInfo();
    if (response.status === 'Success') {
      setIsError(false);
      setResponseMessage(response.message);
    } else {
      setIsError(true);
      setResponseMessage(response);
    }
  }

  const choosePaymentPlans = () => {
    navigate('/priceplan', { state: { navigationFlag: true } })
  }

  const handleUpdatePaymentModal = () => {
    setShowNotification(false);
    setUpdateBankDetailModal(true);
  }

  const bankDetailResponse = (obj) => {
    setShowNotification(false);
    userCardInfo();
    setShowNotification(true);
    setResponseMessage(obj.responseMessage);
    setIsError(obj.isError);
  }

  const getIconComponent = () => {
    switch (cardInfo.type) {
      case 'visa':
        return <FaCcVisa />;
      case 'mastercard':
        return <FaCcMastercard />;
      case 'discover':
        return <FaCcDiscover />;
      case 'paypal':
        return <FaCcPaypal />;
      case 'amex':
        return <FaCcAmex />;
      case 'diners':
        return <FaCcDinersClub />;
      case 'jcb':
        return <FaCcJcb />;
      case 'unionpay':
        return <img src={unionPayIcon} alt='unionPay' />
      default:
        return <FaCcMastercard />;
    }
  };

  return (
    <>
      <Loader open={open} />
      <div className='tab-content position-relative'>
        <div className='tab-heading mb-3 pb-1'>
          <h2 className='mb-0'>Subscription</h2>
          <p className='mt-1 mb-0'>Your actual subscription plan</p>
        </div>
        <div className='py-4 square border-top d-flex justify-content-left'>
          <Col className='p-0' xs md='3'>
            <label className='form-label'>Current Plan </label>
          </Col>
          <Col xs md="5">
            <Card>
              {currentPlan.status === 'non-subscribed' ?
                <Card.Body>
                  <Card.Title className='d-flex subscription-title align-items-end m-0'>{currentPlan?.message}</Card.Title>
                </Card.Body> :
                <Card.Body>
                  <Card.Title className='d-flex subscription-title align-items-end m-0'>{currentPlan?.name}
                    <p className='ms-2 m-0 tab-content'>
                      {currentPlan.status === 'subscribed' ? `${currentPlan?.price}/${currentPlan?.duration}`
                        : <span>{currentPlan?.days_left} days left </span>}
                    </p></Card.Title>
                  <Card.Text className='tab-content fw-normal'>
                    Includes {currentPlan.table_plan_limit}, {currentPlan.name} Dashboard <br />
                    {currentPlan.status === 'subscribed' ?
                      `Next invoice issue date: ${currentPlan.ends_at}` : ''}
                  </Card.Text>
                  <Button className='me-2 save-btn' onClick={currentPlan.status === 'trial' ? choosePaymentPlans : handleShow}>Upgrade plan</Button>
                  {currentPlan.status === 'subscribed' ?
                    <Button className='me-2 save-btn cancel-btn enabled' onClick={handleCancelSubscription}>Cancel Subscription</Button>
                    : ''}
                </Card.Body>
              }
            </Card>
          </Col>
        </div>
        <Modal show={subscriptionModal} onHide={handleClose} className='price-plan-modal'>
          <Modal.Header className='border-0 p-2 m-1' closeButton>
          </Modal.Header>
          <div className='px-4'>
            <h2 className='text-center'>Upgrade your account</h2>
            <UpgradePricePlanModal userCurrentPlanId={userCurrentPlanId} handleContinueToPayment={handleContinueToPayment} />
          </div>
        </Modal>
        <Modal show={upgradePaymentModal} onHide={handlePaymentModalClose} className='payment-checkout-modal'>
          <Modal.Header className='border-0 p-2 m-1' closeButton>
          </Modal.Header>
          <div className='px-4'>
            <h2 className='text-center'>Upgrade your account</h2>
            <UpgradePaymentCheckout priceObj={priceObj} handleChoosePlan={handleChoosePlan} handleUpgradePayment={handleUpgradePayment} />
          </div>
        </Modal>
        <Modal show={updateBankDetailModal} onHide={handleUpdatePaymentModalClose} className='update-payment-modal'>
          <Modal.Header className='border-0 p-2 m-1' closeButton>
          </Modal.Header>
          <div className='px-4'>
            <h2 className='text-center mb-4 pb-2'>Payment information</h2>
            <UpdateBankDetailForm handleUpdatePaymentModalClose={handleUpdatePaymentModalClose} bankDetailResponse={bankDetailResponse} />
          </div>
        </Modal>
        <div className='tab-heading py-4 square border-top'>
          <h2 className='mb-0'>Payment Method</h2>
          <p className='mt-1 mb-0'>Update your personal details here.</p>
        </div>
        <div className='py-4 d-flex justify-content-left'>
          <Col className='p-0' xs md='3'>
            <label className='form-label'>Card details</label>
          </Col>
          <Col xs md="5">
            {cardInfo !== '' && cardInfo ?
              <Card>
                <Card.Body className='d-flex'>
                  <div className='payment-icon me-2 pe-1'>
                    {getIconComponent()}
                  </div>
                  <div>
                    <Card.Title className='subscription-title fs-6 m-0'><span className='text-capitalize'> {cardInfo.type} </span> ending in {cardInfo.last4}</Card.Title>
                    <Card.Text className='tab-content fw-normal m-0'>Expiry {cardInfo.exp_month}/{cardInfo.exp_year}</Card.Text>
                    <Card.Text className='dark-color tab-content fw-normal pointer' onClick={handleUpdatePaymentModal}>Edit</Card.Text>
                  </div>
                </Card.Body>
              </Card>
              :
              <Button className='me-2 add-payment-btn' onClick={choosePaymentPlans}><FaPlus /> Add Payment method</Button>
            }
          </Col>
        </div>
        <div className='tab-heading py-4 square border-top'>
          <h2 className='mb-0'>Billing and invoicing</h2>
          <p className='mt-1 mb-0'>Manage your billing and payment details.</p>
        </div>
        <div className='py-4 d-flex justify-content-left'>
          <Col className='p-0' xs md='3'>
            <label className='form-label'>Billing history</label>
          </Col>
          {updateInvoiceTable &&
            <Col xs md="9">
              <PaymentHistoryTable />
            </Col>
          }
        </div>
        {showNotification &&
          <Notification responseMessage={responseMessage} isError={isError} />
        }
      </div >
    </>
  )
}

export default SubscriptionPlanSettings
