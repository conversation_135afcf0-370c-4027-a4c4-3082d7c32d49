import React, { useState, useEffect } from 'react'
import { Form, Button, Col, InputGroup, FormControl } from 'react-bootstrap';
import { useFormik } from "formik";
import Notification from '../notificationModal/Notification';
import { objectStorageValidation } from '../../utils/objectStorageValidation'
import Loader from '../loader/Loader';
import { objectStorage } from '../../services/objectStorage';

function ObjectStorage() {
  const [open, setOpen] = useState(false);
  const [cloudType, setCloudType] = useState([]);
  const [objectType, setObjectType] = useState([]);
  const [isError, setIsError] = useState(false);
  const [responseMessage, setResponseMessage] = useState();
  const [showNotification, setShowNotification] = useState(false);
  const [objectTypeDisabled, setObjectTypeDisabled] = useState(true);

  useEffect(() => {
    getObjectStorage();
    getAllCloudStorageTypes();
  }, []);

  const getObjectStorage = async () => {
    const response = await objectStorage.getObjectStorage();
    if (response && response.data && response.status === "Success") {
      const objectValues = response.data.clientObjectStorage;
      formik.setValues({
        bucketname: objectValues.bucket_name,
        cloudId: objectValues.cloud_provider_name,
        objectId: objectValues.object_storage_type
      })
    } else {
      setShowNotification(true);
      setIsError(true);
      setResponseMessage(response)
    }
  }

  const getAllCloudStorageTypes = async () => {
    setOpen(true);
    const response = await objectStorage.getAllCloudStorageTypes()
    setOpen(false);
    if (response && response.data) {
      setCloudType(response.data.cloudProviderNames);
    } else {
      setCloudType([]);
    }
  }

  const getAllObjectStorageTypes = async (payload) => {
    setOpen(true);
    const response = await objectStorage.getAllObjectStorageTypes(payload)
    setOpen(false);
    if (response && response.data) {
      setObjectType(response.data.objectStorageTypes);
    } else {
      setObjectType([]);
    }
  }

  const initialValues = {
    bucketname: '',
    cloudId: '',
    objectId: ''
  };

  const formik = useFormik({
    initialValues,
    validationSchema: objectStorageValidation,
    validateOnChange: true,
    validateOnBlur: false,
    onSubmit: async (values) => {
      setOpen(true);
      setShowNotification(false);
      let payload = {
        bucket_name: values.bucketname,
        cloud_provider_name: values.cloudId,
        object_storage_type_name: values.objectId
      }
      const response = await objectStorage.storeObjectStorage(payload);
      if (response.status === 'Success') {
        setOpen(false)
        setShowNotification(true);
        setIsError(false);
        setResponseMessage(response.message);
      }
      else {
        setOpen(false)
        setShowNotification(true);
        setIsError(true);
        setResponseMessage(response.messages);
      }
    },
  });

  // useEffect hook to fetch data when cloudId changes
	useEffect(() => {
		if (!formik.values.cloudId)  return; // Do nothing if cloudId is empty

		let payload = {
			cloud_provider_name: formik.values.cloudId
		};

		// Call the API to fetch object type options
		getAllObjectStorageTypes(payload);
		setObjectTypeDisabled(false); // Enable the second dropdown
	}, [formik.values.cloudId]); // Trigger effect whenever cloudId changes

  return (
    <>
      <Loader open={open} />
      <div className='tab-content'>
        <Form onSubmit={formik.handleSubmit} className='d-block'>
          <div className='py-4 square d-flex justify-content-left'>
            <Col className='p-0' xs md='3'>
              <Form.Label>Bucket Name</Form.Label>
            </Col>
            <Col xs md="6">
              <Form.Group controlId="bucketname" className='ps-3 pe-4' >
                <InputGroup className='signup-pwd-field'>
                  <FormControl
                    value={formik.values.bucketname}
                    className='signup-dropdown-field'
                    name='bucketname'
                    placeholder='Enter the bucket name'
                    onChange={formik.handleChange}>
                  </FormControl>
                </InputGroup>
                {formik.errors.bucketname && formik.touched.bucketname &&
                  <div className="mt-1 p-1 error-text">
                    {formik.errors.bucketname}
                  </div>
                }
              </Form.Group>
            </Col>
          </div>
          <div className='py-4 square border-top d-flex justify-content-left'>
            <Col className='p-0' xs md='3'>
              <Form.Label>Cloud Storage Type:</Form.Label>
            </Col>
            <Col xs md="6">
              <Form.Group controlId="cloudId" className='ps-3 pe-4' >
                <Form.Select
                  value={formik.values.cloudId}
                  className='signup-dropdown-field'
                  name='cloudId'
                  onChange={(e) => {
                    formik.handleChange(e);
                    setObjectTypeDisabled(e.target.value === '');
                    formik.setFieldValue('objectId', '');
                  }}
                >
                  <option value={""}>Select</option>
                  {
                    cloudType.map((data) => (
                      <option key={data.cloud_provider_name} value={data.cloud_provider_name}>{data.cloud_provider_name}</option>
                    ))
                  }
                </Form.Select>
                {formik.errors.cloudId && formik.touched.cloudId &&
                  <div className="mt-1 p-1 error-text">
                    {formik.errors.cloudId}
                  </div>
                }
              </Form.Group>
            </Col>
          </div>
          <div className='py-4 square border-top d-flex justify-content-left'>
            <Col className='p-0' xs md='3'>
              <Form.Label>Object Storage Type:</Form.Label>
            </Col>
            <Col xs md="6">
              <Form.Group controlId="objectId" className='ps-3 pe-4' >
                <Form.Select
                  value={formik.values.objectId}
                  className='signup-dropdown-field'
                  name='objectId'
                  onChange={formik.handleChange}
				          disabled={objectTypeDisabled}
                >
                  <option value={""}>Select</option>
                  {
                    objectType.map((data) => (
                      <option key={data.object_storage_type} value={data.object_storage_type}>{data.object_storage_type}</option>
                    ))
                  }
                </Form.Select>
                {formik.errors.objectId && formik.touched.objectId &&
                  <div className="mt-1 p-1 error-text">
                    {formik.errors.objectId}
                  </div>
                }
              </Form.Group>
            </Col>
          </div>
          <div className='mt-5 d-flex'>
            <Button className='me-2 save-btn' type="submit">
              Save
            </Button>
            <div className={`save-btn cancel-btn`}>
              Test
            </div>
          </div>
        </Form >
        {showNotification &&
          <Notification responseMessage={responseMessage} isError={isError} />
        }
      </div >
    </>
  )
}

export default ObjectStorage
