import React from 'react';
import Form from 'react-bootstrap/Form';

function TableCustomFilters({
  handleFilterSelect,
  filtersValue,
  selectedFilterValue
}) {

  return (
    <>
      <Form className='d-flex align-items-center filter-gap'>
        <Form.Label className='m-0'>Filter by : </Form.Label>
        <Form.Select aria-label="All" className='filter-select'
          onChange={handleFilterSelect}
          value={selectedFilterValue}
        >
          <option value='0'>All</option>
          {filtersValue && filtersValue.map((filter, i) => (
            <option value={filter.id} key={i}>{filter.name}</option>
          ))
          }
        </Form.Select>
      </Form>
    </>
  )
}

export default TableCustomFilters
