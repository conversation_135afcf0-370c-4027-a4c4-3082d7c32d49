import React, { useEffect, useState } from 'react';
import { useFormik } from "formik";
import { Modal, Form, Button, Col, InputGroup, FormControl, Alert } from 'react-bootstrap';
import { MdOutlineCopyAll } from "react-icons/md";
import { addAgentValidation } from '../../../utils/addAgentValidation'
import Loader from '../../loader/Loader';
import { v4 as uuidv4 } from 'uuid';
import { serverInfo } from '../../../services/serversInfo'

function AddAgentModal({
  modalStatus,
  handleClose,
  infoModal,
  responseValues,
  selectedRowArr,
  tableData
}) {
  const [open, setOpen] = useState(false);
  const [errorResponse, setErrorResponse] = useState([]);
  const [flashBG, setFlashBG] = useState(false);
  let url = '';
  // Reset the form when the modal status changes
  useEffect(() => {
    formik.resetForm();
    setErrorResponse('');
    if (infoModal) {
      getServerInfo();
    }
  }, [modalStatus]);

  const setServerData = ((serverData) => {
    formik.setValues({
      agentName: serverData.name,
      host: serverData.hostname,
      port: serverData.port,
      userName: serverData.username,
      uuid: serverData.agent_uuid,
      publickey: process.env.REACT_APP_PUBLIC_KEY,
      tls: serverData.is_tls_required,
    });
  });

  const getServerInfo = () => {
    for (const row of tableData) {
      if (row.agentID === parseInt(selectedRowArr)) {
        setServerData(row);
        break;
      }
    }
  }

  // Initialize Formik Values
  const initialValues = {
    agentName: '',
    host: '',
    port: '',
    userName: '',
    uuid: uuidv4().replace(/-/g, ""),
    publickey: process.env.REACT_APP_PUBLIC_KEY,
    tls: false,
  };

  // Copy Input Field Data
  const copyToClipboard = () => {
    navigator.clipboard.writeText(initialValues.publickey);
    setFlashBG(true);
    setTimeout(() => {
      setFlashBG(false);
    }, 1000);
  }

  const formik = useFormik({
    initialValues,
    validationSchema: addAgentValidation,
    validateOnChange: true,
    validateOnBlur: false,
    onSubmit: async (values) => {
      setErrorResponse('');
      setOpen(true);
      let payload = {
        name: values.agentName,
        hostname: values.host,
        port: values.port,
        username: values.userName,
        agent_uuid: values.uuid,
        is_tls_required: values.tls,
        is_active: 1,
      }
      url = infoModal ? `updateRemoteServer/${selectedRowArr}` : 'createRemoteServer';

      const response = infoModal ? await serverInfo.addServer(url, payload) : await serverInfo.updateAgent(url, payload)
      setOpen(false);
      if (response.status === 'Success') {
        responseValues(response);
      } else {
        const res = response.messages;
        setErrorResponse(res);
      }
    }
  });

  return (
    <>
      <Loader open={open} />
      <Modal show={modalStatus} onHide={handleClose} className='add-agent-modal'>
        <Modal.Header className='border-0 p-2 m-1' closeButton></Modal.Header>
        <div className='px-4'>
          <h2 className='text-left mb-4'>{infoModal ? 'Agent Info' : 'New Agent'}</h2>
          {errorResponse !== '' && (
            <Alert variant="danger" className="mb-0" style={{ fontSize: '14px' }}>
              {errorResponse && errorResponse.map((res, index) => (
                <div key={index}>{res}</div>
              ))}
            </Alert>
          )}
          <div className='tab-content position-relative'>
            <Form onSubmit={formik.handleSubmit} className='d-block'>
              <div className='pb-3'>
                <Col className='p-0'>
                  <Form.Label>Agent Name</Form.Label>
                </Col>
                <Col>
                  <Form.Group controlId="agentName">
                    <InputGroup className='signup-pwd-field'>
                      <FormControl
                        type='text'
                        placeholder='Agent Name'
                        name='agentName'
                        value={formik.values.agentName}
                        onChange={formik.handleChange}
                        className='pwd-field-bg'
                      />
                    </InputGroup>
                    {formik.errors.agentName && formik.touched.agentName &&
                      <div className="mt-1 p-1 error-text" >
                        {formik.errors.agentName}
                      </div>
                    }
                  </Form.Group>
                </Col>
              </div>
              <div className='pb-3 d-flex justify-content-left'>
                <div className='col-6 pe-3'>
                  <Col className='p-0'>
                    <Form.Label>Host</Form.Label>
                  </Col>
                  <Col>
                    <Form.Group controlId="host">
                      <InputGroup className='signup-pwd-field'>
                        <FormControl
                          type='text'
                          placeholder='Host'
                          name='host'
                          value={formik.values.host}
                          onChange={formik.handleChange}
                          className='pwd-field-bg'
                        />

                      </InputGroup>
                      {formik.errors.host && formik.touched.host &&
                        <div className="mt-1 p-1 error-text" >
                          {formik.errors.host}
                        </div>
                      }
                    </Form.Group>
                  </Col>
                </div>
                <div className='col-6'>
                  <Col className='p-0'>
                    <Form.Label>PORT</Form.Label>
                  </Col>
                  <Col>
                    <Form.Group controlId="port">
                      <InputGroup className='signup-pwd-field'>
                        <FormControl
                          type='text'
                          placeholder='Port'
                          name='port'
                          value={formik.values.port}
                          onChange={formik.handleChange}
                          className='pwd-field-bg'
                        />

                      </InputGroup>
                      {formik.errors.port && formik.touched.port &&
                        <div className="mt-1 p-1 error-text" >
                          {formik.errors.port}
                        </div>
                      }
                    </Form.Group>
                  </Col>
                </div>
              </div>
              <div className='pb-3'>
                <Col className='p-0'>
                  <Form.Label>User Name</Form.Label>
                </Col>
                <Col>
                  <Form.Group controlId="userName">
                    <InputGroup className='signup-pwd-field'>
                      <FormControl
                        type='text'
                        placeholder='User Name'
                        name='userName'
                        value={formik.values.userName}
                        onChange={formik.handleChange}
                        className='pwd-field-bg'
                      />
                    </InputGroup>
                    {formik.errors.userName && formik.touched.userName &&
                      <div className="mt-1 p-1 error-text" >
                        {formik.errors.userName}
                      </div>
                    }
                  </Form.Group>
                </Col>
              </div>
              <div className='pb-3'>
                <Col className='p-0'>
                  <Form.Label>UUID</Form.Label>
                </Col>
                <Col>
                  <Form.Group controlId="uuid">
                    <InputGroup className='signup-pwd-field read-only'>
                      <FormControl
                        type='text'
                        placeholder='UUID'
                        name='uuid'
                        value={formik.values.uuid}
                        className='pwd-field-bg'
                        readOnly
                      />

                    </InputGroup>
                    {formik.errors.uuid && formik.touched.uuid &&
                      <div className="mt-1 p-1 error-text" >
                        {formik.errors.uuid}
                      </div>
                    }
                  </Form.Group>
                </Col>
              </div>
              <div className='pb-3'>
                <Col className='p-0'>
                  <Form.Label>Public Key</Form.Label>
                </Col>
                <Col>
                  <Form.Group controlId="publickey">
                    <InputGroup className='signup-pwd-field read-only'>
                      <FormControl
                        type='text'
                        placeholder='Public Key'
                        name='publickey'
                        value={formik.values.publickey}
                        className={`pwd-field-bg ${flashBG && 'flash-bg'}`}
                        readOnly
                      />
                      <span className='copy-icon' onClick={copyToClipboard}><MdOutlineCopyAll /></span>
                    </InputGroup>
                    {formik.errors.publickey && formik.touched.publickey &&
                      <div className="mt-1 p-1 error-text" >
                        {formik.errors.publickey}
                      </div>
                    }
                  </Form.Group>
                </Col>
              </div>
              <Form.Group className="mb-3" controlId="tls">
                <Form.Check
                  type="checkbox"
                  id="tls"
                  name="tls"
                  label="Required TLS"
                  checked={formik.values.tls}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                />
                {formik.errors.tls && formik.touched.tls && (
                  <div className="mt-1 p-1 error-text">
                    {formik.errors.tls}
                  </div>
                )}
              </Form.Group>
              <div className='mb-4 text-end'>
                <Button className='btn-agent btn-add-agent me-3' type="submit">
                  {infoModal ? 'Update' : 'Add'}
                </Button>
                <Button className='btn-agent btn-cancel-agent' onClick={handleClose}>
                  Cancel
                </Button>
              </div>
            </Form>
          </div>
        </div>
      </Modal >
    </>
  )
}

export default AddAgentModal
