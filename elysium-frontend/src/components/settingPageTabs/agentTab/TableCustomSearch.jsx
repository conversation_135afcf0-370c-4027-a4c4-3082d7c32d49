import React, { useCallback, useRef } from 'react';
import { InputGroup, Form } from 'react-bootstrap';
import { AiOutlineSearch } from 'react-icons/ai';

const debounce = (func, wait) => {
  let timeout;

  return (...args) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  }
}

function TableCustomSearch({
  searchData
}) {
  const inputElem = useRef(null);
  const handleSearch = useCallback(debounce(inputVal => searchData(inputVal), 500), []);
  return (
    <InputGroup className="search-field">
      <InputGroup.Text id="basic-addon1"> <AiOutlineSearch /> </InputGroup.Text>
      <Form.Control
        className="border-start-0 ps-0"
        placeholder="Search"
        aria-label="Search"
        aria-describedby="basic-addon1"
        ref={inputElem}
        // value={search}
        // type="text"
        onChange={() => handleSearch(inputElem.current)}
        // value={search.searchValue}
        name="searchValue"
      />
    </InputGroup>
  );
};

export default TableCustomSearch;