.css-1omuo8w-MuiToolbar-root {
  background-color: #F6F8FC !important;
}

.table-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.add-agent-btn {
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
  padding: 8px 12px;
  border: 1px solid #DADDE5;
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #fff;
}

.table-header .filter-select {
  min-width: 135px;
  cursor: pointer;
}

.filter-gap {
  gap: 8px;
}

.filter-gap .form-label {
  width: -webkit-fill-available;
}

#basic-addon1 {
  padding: 6px 8px;
  border-right: none;
  background-color: #fff;
}

.search-filter-gap {
  gap: 24px;
}

.input-group.search-field {
  width: 252px;
}

.server-status {
  border-radius: 16px;
  padding: 4px 9px;
  line-height: 20px;
  width: fit-content;
}

.complete-status {
  background: #BDF4C3;
  color: #169C00;
}

.pending-status {
  background: #C6CCDC;
  color: #363F5A;
}

.add-agent-modal .modal-dialog {
  max-width: 797px;
}

.add-agent-modal .btn-agent {
  border-radius: 8px;
  border-color: #000117;
}

.add-agent-modal .btn-add-agent {
  background: #000117;
}

.add-agent-modal .btn-cancel-agent {
  background: #fff;
  color: #000117;
}

.input-group.read-only input {
  color: #7D7E88;
}

.MuiBox-root css-sq9qdz {
  align-items: center;
}

.red {
  color: red;
}

.copy-icon {
  display: flex;
  width: 30px;
  align-items: center;
  border: 1px solid #DDE2E6;
  cursor: pointer;
}

.copy-icon svg {
  width: 98%;
  height: auto;
  margin: 0 auto;
}

.read-only input[name="publickey"] {
  border-right: 1px solid #DDE2E6;
}

.flash-bg {
  animation-name: flash;
  animation-timing-function: ease-out;
  animation-duration: 1s;
}

@keyframes flash {
  0% {
    background: #C5CCDC;
  }

  100% {
    background: transparent;
  }
}

.test-connection-btn {
  color: #174BE6;
  border-color: #174BE6;
  transition: all 0.3s ease;
}

.test-connection-btn:hover {
  background-color: #174BE6;
  color: white;
}

.test-connection-btn svg {
  margin-right: 4px;
}
