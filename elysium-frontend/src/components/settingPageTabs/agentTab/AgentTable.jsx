import React, { useMemo, useState, useEffect, useRef } from 'react';
import { MaterialReactTable } from 'material-react-table';
import { ContentCopy } from '@mui/icons-material';
import AddIcon from '@mui/icons-material/Add';
import InfoRoundedIcon from '@mui/icons-material/InfoRounded';
import { RiDeleteBin6Line } from 'react-icons/ri';
import { TbRefresh, TbPlugConnected } from 'react-icons/tb';
import { serverInfo } from '../../../services/serversInfo'
import TableCustomFilters from './TableCustomFilters';
import AddAgentModal from './AddAgentModal';
import Loader from '../../loader/Loader';
import Notification from '../../notificationModal/Notification';
import ConfirmationModal from '../../confirmationModal/ConfirmationModal';
import './style.css';
import TerminalModal from '../../terminalModal/TerminalModal';

const AgentTable = () => {

  const [tableData, setTableData] = useState([]);
  const [rowSelection, setRowSelection] = useState([]);
  const [modalStatus, setModalStatus] = useState(false);
  const [infoModal, setInfoModal] = useState(false);
  const [open, setOpen] = useState(false);
  const [showNotification, setShowNotification] = useState(false);
  const [responseMessage, setResponseMessage] = useState();
  const [selectedRowArr, setSelectedRowArr] = useState([]);
  const [confirmModal, setConfirmModal] = useState(false);
  const searchRef = useRef(null);
  const [showTerminal, setShowTerminal] = useState(false);
  const [terminalLogs, setTerminalLogs] = useState([]);
  const [terminalTitle, setTerminalTitle] = useState('');
  const [currentAgentId, setCurrentAgentId] = useState(null);

  useEffect(() => {
    getSelectedRows();
  }, [rowSelection]);

  const responseValues = (response) => {
    setModalStatus(false);
    setRowSelection([]);
    setShowNotification(true);
    setResponseMessage(response.message);
    agentTableData();
  };

  // Close Modal
  const handleClose = () => {
    setModalStatus(false);
  }

  // Add Server Modal Handle
  const handleAddServer = () => {
    setShowNotification(false);
    setInfoModal(false);
    setModalStatus(true);
  }

  // Update Server Information
  const handleInfoServer = () => {
    setShowNotification(false);
    setModalStatus(true);
    setInfoModal(true);
  };

  // Get Selected Row Values
  const getSelectedRows = () => {
    const updatedSelectedRowArr = [];
    if (Object.keys(rowSelection).length > 0) {
      Object.keys(rowSelection).forEach((selectedRow) => {
        // Find the corresponding data from tableData
        const foundRow = tableData.find((row) => row.agentID === parseInt(selectedRow));
        if (foundRow) {
          updatedSelectedRowArr.push(foundRow.agentID);
        }
      });
    }
    setSelectedRowArr(updatedSelectedRowArr);
  }

  // Get Filtered Data from DB
  const [filtersValue, setFiltersValue] = useState([]);
  const [selectedFilterValue, setSelectedFilterValue] = useState(0);
  const filterFunc = async () => {
    // FETCH Filtering Values
    const response = await serverInfo.agentServerFilters();
    if (response && response.data) {
      setFiltersValue(response.data.remoteServerFilters);
    } else {
      setFiltersValue([]);
    }
  }
  // End Filtering

  // Remove Servers
  const handleRemove = () => {
    setShowNotification(false);
    setConfirmModal(true);
  }

  const handleConfirmModal = async () => {
    setConfirmModal(false);
    setRowSelection([]);
    setOpen(true);
    let payload = {
      ids: selectedRowArr
    };
    const response = await serverInfo.deleteAgent(payload);
    setOpen(false);
    setShowNotification(true);
    setResponseMessage(response.message);
    if (response && response.status === 'Success') {
      agentTableData();
    }
  }
  // End Remove Servers

  // Handle test connection
  const handleTestConnection = async (agentId, hostname) => {
    setCurrentAgentId(agentId);
    setTerminalTitle(`Testing Connection to Agent #${agentId} (${hostname})`);
    setTerminalLogs([
      { type: 'info', message: `Initializing connection test to ${hostname}...` },
      { type: 'command', message: `ssh -p <port> -i /path/to/key ${hostname}` }
    ]);
    setShowTerminal(true);
    
    try {
      // Make the actual API call
      const response = await serverInfo.testAgentConnection(agentId);
      
      // Process backend logs if available
      if (response && response.data && response.data.logs) {
        const backendLogs = response.data.logs.map(log => {
          // Determine log type based on content
          let type = 'info';
          if (log.includes('Error') || log.includes('ERROR') || log.includes('failed') || log.includes('WARNING')) {
            type = log.includes('WARNING') ? 'warning' : 'error';
          } else if (log.includes('successful') || log.includes('detected') || log.includes('Success')) {
            type = 'success';
          } else if (log.includes('Executing command:') || log.includes('Attempting') || log.includes('ssh-keygen') || log.includes('Testing TCP')) {
            type = 'command';
          } else if (log.includes('Please run') || log.includes('Troubleshooting') || log.includes('Possible causes')) {
            type = 'warning';
          }
          
          return { type, message: log };
        });
        
        setTerminalLogs(prev => [...prev.slice(0, 2), ...backendLogs]);
        
        // Add specific instructions for connection timeout
        if (response.message && response.message.includes('timed out') || 
            (response.data.logs && response.data.logs.some(log => log.includes('timed out')))) {
          setTerminalLogs(prev => [...prev, 
            { type: 'warning', message: 'Connection Timeout Detected' },
            { type: 'info', message: 'This usually indicates one of the following issues:' },
            { type: 'info', message: '1. The server is not reachable (network issue or server is down)' },
            { type: 'info', message: '2. A firewall is blocking the connection' },
            { type: 'info', message: '3. The SSH service is not running on the specified port' },
            { type: 'info', message: '4. The hostname or port might be incorrect' },
            { type: 'warning', message: 'Recommended Actions:' },
            { type: 'command', message: 'ping ' + hostname },
            { type: 'info', message: 'Verify the server is online and responding to pings' },
            { type: 'command', message: 'telnet ' + hostname + ' <port>' },
            { type: 'info', message: 'Check if the port is open and accepting connections' },
            { type: 'info', message: 'Verify your network/VPN settings allow connections to this server' },
            { type: 'info', message: 'Contact your network administrator to check firewall rules' }
          ]);
        }
      }
      
      // Add final status message
      if (response && response.status === 'Success') {
        setTerminalLogs(prev => [...prev, 
          { type: 'success', message: response.message }
        ]);
      } else {
        setTerminalLogs(prev => [...prev, 
          { type: 'error', message: response.message || 'Connection test failed.' }
        ]);
      }
      
      // Refresh the table data
      agentTableData();
    } catch (error) {
      setTerminalLogs(prev => [...prev,
        { type: 'error', message: 'An error occurred during the connection test.' },
        { type: 'error', message: error.message }
      ]);
    }
  };

  useEffect(() => {
    clearSearchField();
  }, [searchRef])

  // Reset All Filters
  const clearSearchField = () => {
    const searchInput = document.querySelector('.MuiInputBase-input');
    if (searchInput) {
      searchInput.value = ''; // Clear the search field
    }
  };
  const handleReset = () => {
    setShowNotification(false);
    setSelectedFilterValue('0');
    clearSearchField();
    setRowSelection([]);
    setSelectedRowArr([]);
    agentTableData();
  }
  const CustomTableToolbar = () => {
    // Get Filtered Data from DB
    const handleFilterSelect = (event) => {
      const selectedValue = event.target.value;
      agentTableData(selectedValue);
      setSelectedFilterValue(selectedValue);
    };

    // Custom Table Headers Options
    return (
      <div className='d-flex justify-content-between pe-4 w-100'>
        <div className='table-header'>
          <button className='square rounded add-agent-btn' onClick={handleAddServer}><AddIcon />Add</button>
          <button className='square rounded add-agent-btn' disabled={selectedRowArr.length > 0 && selectedRowArr.length < 2 ? false : true} onClick={handleInfoServer}><InfoRoundedIcon /> Info</button>
          <button className={`square rounded add-agent-btn ${selectedRowArr.length > 0 ? 'red' : ''}`} disabled={selectedRowArr.length > 0 ? false : true} onClick={handleRemove}><RiDeleteBin6Line /> Remove</button>
          <button className='square rounded add-agent-btn' onClick={handleReset}><TbRefresh style={{ width: '18px', height: '20px' }} /></button>
        </div>
        <div className='table-header search-filter-gap'>
          <TableCustomFilters
            handleFilterSelect={handleFilterSelect}
            filtersValue={filtersValue}
            selectedFilterValue={selectedFilterValue}
          />
        </div>
      </div>
    );
  };

  // FETCH Filter Values
  const agentTableData = async (selectedValue) => {
    setOpen(true);
    const response = await serverInfo.agentSeversData(selectedValue);
    setOpen(false);
    if (response && response.data) {
      setTableData(response.data.agents);
    } else {
      setTableData([]);
    }
  }

  useEffect(() => {
    agentTableData();
    filterFunc();
  }, []);

  // Table Header
  const columns = useMemo(() => [
    {
      accessorKey: 'agentID',
      header: 'Agent ID',
      selectable: true
    },
    {
      accessorFn: (row) => row.name,
      id: 'name',
      header: 'Agent name'
    },
    {
      accessorFn: (row) => row.agent_uuid,
      id: 'agent_uuid',
      header: 'Agent UUID',
      enableClickToCopy: true,
      muiTableBodyCellCopyButtonProps: {
        fullWidth: false,
        endIcon: <ContentCopy />,
        sx: { justifyContent: 'flex-start' },
      },
    },
    {
      accessorFn: (row) => row.hostname,
      id: 'hostname',
      header: 'Host Info'
    },
    {
      accessorFn: (row) => row.status,
      id: 'status',
      header: 'Status',
      Cell: ({ cell }) => {
        const status = cell.row._valuesCache.status;
        return (
          <div className={`server-status ${status === 'Completed' ? "complete-status" : 'pending-status'}`}>{status}</div>
        )
      }
    },
    {
      id: 'actions',
      header: 'Actions',
      Cell: ({ row }) => {
        return (
          <button 
            className="square rounded add-agent-btn test-connection-btn"
            onClick={() => handleTestConnection(row.original.agentID, row.original.hostname)}
          >
            <TbPlugConnected /> Test Connection
          </button>
        )
      }
    },
  ], []);

  return (
    <>
      <Loader open={open} />
      <MaterialReactTable
        columns={columns}
        data={tableData}
        enableSorting={true}
        enableHiding={false}
        enableDensityToggle={false}
        enableColumnActions={false}
        enableColumnFilters={false}
        enableFullScreenToggle={false} // Disable full screen View
        enablePagination={true} // Enable pagination
        getRowId={(row) => row.agentID}
        enableRowSelection={true}
        onRowSelectionChange={setRowSelection}
        state={{ rowSelection }} // Manage your own state, pass it back to the table (optional)
        // For Searching
        enableGlobalFilterModes
        initialState={{
          showGlobalFilter: true,
        }}
        muiSearchTextFieldProps={{
          placeholder: 'Search',
          ref: searchRef,
          sx: () => (
            {
              minWidth: '252px',
              '.MuiInputBase-adornedStart': {
                padding: '0px 6px',
                background: '#fff'
              },
              'button': {
                padding: '0px',
                width: 'auto'
              },
              '& input': {
                padding: '8px 0',
              },
            }
          ),
          variant: 'outlined',
        }}
        muiTableHeadCellProps={{
          sx: () => ({
            fontFamily: "Ubuntu",
          }),
        }}
        muiTableBodyProps={{
          sx: () => ({
            '& tr:nth-of-type(odd)': {
              backgroundColor: '#f5f8fc',
            },
            '& td, & button': {
              fontFamily: "Ubuntu",
              fontWeight: "400",
            }
          }),
        }}
        muiTablePaginationProps={{
          sx: () => ({
            '& p': {
              fontFamily: "Ubuntu",
            },
          })
        }}
        renderTopToolbarCustomActions={() => <CustomTableToolbar />}
      />
      <AddAgentModal handleClose={handleClose} modalStatus={modalStatus}
        selectedRowArr={selectedRowArr} infoModal={infoModal}
        responseValues={responseValues} tableData={tableData} />
      {showNotification &&
        <Notification responseMessage={responseMessage} />
      }
      {
        confirmModal && (
          <ConfirmationModal buttonValue='Remove'
            showModel={confirmModal}
            onSuccess={handleConfirmModal}
            onClose={() => setConfirmModal(false)}
            title={`Remove Agent #${selectedRowArr} ?`}
            message='Are you sure to remove ?'
          />
        )
      }
      <TerminalModal 
        show={showTerminal}
        onHide={() => setShowTerminal(false)}
        logs={terminalLogs}
        title={terminalTitle}
        agentId={currentAgentId}
      />
    </>
  );
};

export default AgentTable;
