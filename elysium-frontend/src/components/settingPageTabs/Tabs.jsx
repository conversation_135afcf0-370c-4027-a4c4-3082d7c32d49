import React, { useState } from 'react';
import { Container, Tab, Tabs } from 'react-bootstrap';
import './style.css'
import ProfileSettings from './ProfileSettings';
import PasswordUpdateSettings from './PasswordUpdateSettings';
import SubscriptionPlanSettings from './SubscriptionPlanSettings';
import AgentTab from './agentTab/AgentTab';
import ObjectStorage from './ObjectStorage';

function SettingTabs() {
  const [activeTab, setActiveTab] = useState('profile');
  const handleTabSelect = (selectedTab) => {
    setActiveTab(selectedTab);
  };

  return (
    <Container className='my-5'>
      <h1 className='mb-4'>Settings</h1>
      <Tabs
        id="controlled-tab-example"
        activeKey={activeTab}
        onSelect={handleTabSelect}
        className="mb-4 pb-2 custom-tabs"
      >
        <Tab eventKey="profile" title="Profile ">
          {activeTab === 'profile' && <ProfileSettings />}
        </Tab>
        <Tab eventKey="password" title="Password">
          {activeTab === 'password' && <PasswordUpdateSettings />}
        </Tab>
        <Tab eventKey="subscription" title="Subscription">
          {activeTab === 'subscription' && <SubscriptionPlanSettings />}
        </Tab>
        <Tab eventKey="agent" title="Agent">
          {activeTab === 'agent' && <AgentTab />}
        </Tab>
        <Tab eventKey="object-storage" title="Object Storage">
          {activeTab === 'object-storage' && <ObjectStorage />}
        </Tab>
      </Tabs>
    </Container>
  );
}

export default SettingTabs;