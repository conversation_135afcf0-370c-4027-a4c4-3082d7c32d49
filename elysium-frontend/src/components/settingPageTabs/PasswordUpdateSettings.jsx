import React, { useState, useEffect } from 'react'
import { Form, Button, Col, InputGroup, FormControl } from 'react-bootstrap';
import { useFormik } from "formik";
import PasswordField from '../signup/PasswordField';
import { updateUserInfo } from '../../services/updateProfileInfo';
import { updatePasswordSchema } from '../../utils/updatePassword'
import Notification from '../notificationModal/Notification';
import Loader from '../loader/Loader';
import { FaEye, FaEyeSlash } from 'react-icons/fa';

function PasswordUpdateSettings() {
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showRepeatPassword, setShowRepeatPassword] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isFormChanged, setIsFormChanged] = useState(false);
  const [open, setOpen] = useState(false);
  const [responseMessage, setResponseMessage] = useState();
  const [showNotification, setShowNotification] = useState(false);
  const [isError, setIsError] = useState(false);

  const initialValues = {
    currentPassword: '',
    password: '',
    confirmPassword: ''
  };

  const formik = useFormik({
    initialValues,
    validationSchema: updatePasswordSchema,
    validateOnChange: true,
    validateOnBlur: false,
    onSubmit: async (values, action) => {
      setShowNotification(false);
      setOpen(true);
      let payload = {
        current_password: values.currentPassword,
        new_password: values.password,
        new_confirm_password: values.confirmPassword
      }
      const response = await updateUserInfo.updatePassword('/updateUserProfilePassword', payload);
      if (response.status === 'Success') {
        setOpen(false);
        setShowNotification(true);
        setIsError(false);
        setResponseMessage(response.message);
        action.resetForm();
      } else {
        setOpen(false);
        setShowNotification(true);
        setIsError(true);
        setResponseMessage(response);
      }
    }
  });

  const toggleCurrentShowPassword = () => {
    setShowCurrentPassword(!showCurrentPassword);
  };
  const toggleRepeatShowPassword = () => {
    setShowRepeatPassword(!showRepeatPassword);
  };
  useEffect(() => {
    const hasNonEmptyField = Object.values(formik.values).some((value) => value !== '');
    setIsFormChanged(hasNonEmptyField);
  }, [formik.values]);

  const handleCancel = () => {
    formik.resetForm();
  };

  return (
    <>
      <Loader open={open} />
      <div className='tab-content position-relative'>
        <div className='tab-heading mb-3 pb-1'>
          <h2 className='mb-0'>Password</h2>
          <p className='mt-1 mb-0'>Update your password</p>
        </div>
        <Form onSubmit={formik.handleSubmit} className='d-block'>
          <div className='py-4 square border-top d-flex justify-content-left'>
            <Col className='p-0' xs md='3'>
              <Form.Label>Current Password</Form.Label>
            </Col>
            <Col xs md="6">
              <Form.Group controlId="currentPassword" className='ps-3 pe-4'>
                <InputGroup className='signup-pwd-field'>
                  <FormControl
                    type={showCurrentPassword ? 'text' : 'password'}
                    placeholder='Current Password'
                    name='currentPassword'
                    value={formik.values.currentPassword}
                    onChange={formik.handleChange}
                    className='pwd-field-bg'
                  />
                  <InputGroup.Text onClick={toggleCurrentShowPassword} className="pwd-field-bg">
                    <span style={{ color: '#6c757d' }}>
                      {showCurrentPassword ? "Hide" : "Show"}
                    </span>
                    &nbsp; &nbsp;
                    <span>
                      {showCurrentPassword ? <FaEyeSlash className='pwd-field-bg' /> : <FaEye className='pwd-field-bg' />}
                    </span>
                  </InputGroup.Text>

                </InputGroup>
                {formik.errors.currentPassword && formik.touched.currentPassword &&
                  <div className="mt-1 p-1 error-text" >
                    {formik.errors.currentPassword}
                  </div>
                }
              </Form.Group>
            </Col>
          </div>
          <div className='py-4 square border-top d-flex justify-content-left'>
            <Col className='p-0' xs md='3'>
              <Form.Label>New Password</Form.Label>
            </Col>
            <Col xs md="6">
              <PasswordField
                values={formik.values}
                handleChange={formik.handleChange}
                marginClass='ps-3 pe-4'
                errors={formik.errors}
                touched={formik.touched}
                placeholder='Password'
                showPassword={showPassword}
                setShowPassword={setShowPassword} />
              {formik.errorMessage &&
                formik.errorMessage.map(errors => {
                  return (
                    <div className="mt-0 mx-2 p-0 error-text">
                      {errors}
                    </div>
                  )
                })
              }
            </Col>
          </div>
          <div className='pb-4 square border-bottom d-flex justify-content-left'>
            <Col className='p-0' xs md='3'>
              <Form.Label>Repeat Password</Form.Label>
            </Col>
            <Col xs md="6">
              <Form.Group controlId="confirmPassword" className='ps-3 pe-4'>
                <InputGroup className='signup-pwd-field'>
                  <FormControl
                    type={showRepeatPassword ? 'text' : 'password'}
                    placeholder='Repeat new password'
                    name='confirmPassword'
                    value={formik.values.confirmPassword}
                    onChange={formik.handleChange}
                    className='pwd-field-bg'
                  />
                  <InputGroup.Text onClick={toggleRepeatShowPassword} className="pwd-field-bg">

                    <span style={{ color: '#6c757d' }}>
                      {showRepeatPassword ? "Hide" : "Show"}
                    </span>
                    &nbsp; &nbsp;
                    <span>
                      {showRepeatPassword ? <FaEyeSlash className='pwd-field-bg' /> : <FaEye className='pwd-field-bg' />}
                    </span>
                  </InputGroup.Text>

                </InputGroup>
                {formik.errors.confirmPassword && formik.touched.confirmPassword &&
                  <div className="mt-1 p-1 error-text" >
                    {formik.errors.confirmPassword}
                  </div>
                }
              </Form.Group>
            </Col>
          </div>
          <div className='mt-5 d-flex'>
            <Button className='me-2 save-btn' type="submit">
              Update Password
            </Button>
            <div className={`save-btn cancel-btn ${isFormChanged ? 'enabled' : ''}`} onClick={handleCancel}>
              Cancel
            </div>
          </div>
        </Form>
        {showNotification &&
          <Notification responseMessage={responseMessage} isError={isError} />
        }
      </div>
    </>
  )
}

export default PasswordUpdateSettings
