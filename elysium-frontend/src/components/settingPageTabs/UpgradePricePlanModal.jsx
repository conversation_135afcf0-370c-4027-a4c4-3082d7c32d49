import React, { useState, useEffect } from 'react';
import { Row, Col, Button, Card } from 'react-bootstrap';
import { BsCheck } from "react-icons/bs";
import { serviceCall } from '../../services/pricePlan'
import Loader from '../loader/Loader';

function UpgradePricePlanModal({
  userCurrentPlanId,
  handleContinueToPayment,
}) {

  const [pricePlanArray, setPricePlanArray] = useState([])
  const [toggle, setToggle] = useState('Monthly');
  const [open, setOpen] = useState(true);

  useEffect(() => {
    fetchPricePlans();
  }, [toggle]);

  const fetchPricePlans = async () => {
    try {
      let payload = {
        "subscription_type_id": toggle === 'Monthly' ? 2 : 3
      }

      const response = await serviceCall.getAllSubscriptions(payload);
      if (response.status === 'Success' && response.data.subscriptionPlans) {
        setOpen(false);
        setPricePlanArray(response.data.subscriptionPlans);
      }
    } catch (error) {
      setOpen(false);
      setPricePlanArray([]);
    }
  };

  const togglePlan = (val) => {
    setOpen(true);
    setToggle(val);
  };

  const handleContactSupport = () => {
    const link = document.createElement('a');
    link.href = 'mailto:<EMAIL>';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  const monthlyVariant = toggle === 'Monthly' ? 'dark' : 'light';
  const yearlyVariant = toggle === 'Yearly' ? 'dark' : 'light';

  return (
    <>
      <Loader open={open} />
      <div>
        <Row className='my-4 button-toggle'>
          <Col sm={6} className='px-1'>
            <Button variant={monthlyVariant} onClick={() => togglePlan('Monthly')}> Monthly</Button>
          </Col>
          <Col sm={6} className='px-1 text-center'>
            <Button variant={yearlyVariant} onClick={() => togglePlan('Yearly')}> Yearly</Button>
          </Col>
        </Row>
      </div>
      {/* for pricing card row */}
      <Row className='mt-2 align-items-end mx-auto'>

        {pricePlanArray.length > 0 && pricePlanArray.map((card, i) => (
          <Col key={i} className="md-3 p-0" >
            <Card className="price-plan-card p-4 my-4 mx-auto">
              <Card.Body className='p-0'>
                <Row className="align-items-center">
                  <Col>
                    <Card.Title className='price-text'> <b>{card.plan_name}</b> </Card.Title>
                  </Col>
                </Row>
                <p>
                  <b className='price-text'> {card.plan_price} </b>
                  <span className='keys-services'>
                    {card.priceDescription} {card.subscription_type_id === 2 ?
                      card.plan_name === 'Enterprise' ? '/yearly billing only' : ' /month'
                      : card.plan_name === 'Enterprise' ? '/yearly billing only' : ' /year'}
                  </span>
                </p>

                <li style={{ fontSize: 14, color: '#5C5D6A' }}> {card.table_plan_limit}</li>
                {card.price_per_table &&
                  <li style={{ fontSize: 14, color: '#5C5D6A' }}> {card.price_per_table}</li>
                }

                {i === 3 && <br />}
                <h6 className='mt-3 key-benefits-text'> Key Benefits:</h6>

                {Object.entries(card.key_benefits).map(([text, value], i) => (
                  < li key={i}
                    className={`keys-val-services ${value === 0 ? 'disabled-benefit-list' : ''}`}
                  >
                    <BsCheck style={{ color: '#169C00' }} /> {text} </li>
                ))
                }
                <div className='trail-btn-row'>
                  <Button variant=''
                    disabled={userCurrentPlanId === card.stripe_plan ? true : false}
                    onClick={(e) => card.plan_name === 'Enterprise' ? handleContactSupport() : handleContinueToPayment(card.plan_name, card.plan_price, card.stripe_plan, card.id, card.subscription_type, card.plan_price_end_date)}
                    className={userCurrentPlanId === card.stripe_plan ? 'upgrade-btn' : 'none-select-btn upgrade-btn'}>
                    {userCurrentPlanId === card.stripe_plan ? 'Your current plan' : card.plan_name === 'Enterprise' ? 'Contact us' : 'Upgrade plan'}
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>
        ))}
      </Row >
    </>
  )
}

export default UpgradePricePlanModal