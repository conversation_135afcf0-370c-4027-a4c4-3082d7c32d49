import React from 'react'
import { Container, Row, Col, Card, Button } from 'react-bootstrap';
import { BsCheck } from "react-icons/bs";

import "./style.css"

function UpgradePaymentCheckout({
  priceObj,
  handleChoosePlan,
  handleUpgradePayment
}) {

  return (
    <>
      <Container fluid>
        <div className='d-flex flex-column justify-content-center align-items-center my-4'>
          <Row style={{ width: '424px' }}>
            <Card className='px-0'>
              <Card.Body className='my-4 py-2 px-4'>
                <Card.Title className='plan-title mb-3 pb-3'> {priceObj.title}  Plan </Card.Title>
                <Row className='mb-2 justify-content-between align-items-center'>
                  <Col>Billing</Col>
                  <Col className='text-end'> {priceObj.tag}</Col>
                </Row>
                <Row className='mb-2 justify-content-between align-items-center'>
                  <Col>{priceObj.title}</Col>
                  {priceObj.price.includes('Custom') ?
                    <Col className='text-end'> {priceObj.price}</Col> :
                    <Col className='text-end'> {priceObj.price}.00</Col>
                  }
                </Row>
                <hr />
                <Row className='mb-2 justify-content-between align-items-center'>
                  <Col md={4}> <b className='plan-title'>Total </b></Col>
                  <Col className='text-end' md={8}>
                    {priceObj.price.includes('Custom') ? (
                      <b className='plan-title'>{priceObj.price}</b>
                    ) : (
                      <>
                        <b className='plan-title'>{priceObj.price}.00 </b> USD
                      </>
                    )}
                  </Col>
                </Row>
                <Row>
                  <div className='mt-3 mb-4 renew-text p-0'>
                    <BsCheck style={{ color: '#169C00' }} className='me-1' />
                    Your plan will be renewed on {priceObj.plan_price_end_date}
                  </div>
                </Row>
                <Row>
                  <Button variant=''
                    onClick={(e) => { handleUpgradePayment(priceObj.id) }}
                    className='none-select-btn upgrade-btn py-2'>
                    Start Now!
                  </Button>
                </Row>

                <Row>
                  <Button variant=''
                    onClick={(e) => { handleChoosePlan() }}
                    className='mt-2 choose-plan-btn'>
                    Choose another plan
                  </Button>
                </Row>

              </Card.Body>
            </Card>
          </Row>
        </div>
      </Container>
    </>
  )
}

export default UpgradePaymentCheckout