.custom-tabs {
  gap: 4px;
}

.custom-tabs,
.custom-tabs .active {
  border: none !important;
}

.custom-tabs .active {
  background: #DADDE5 !important;
  border-radius: 6px;
}

.custom-tabs .nav-item button {
  color: #5C5D6A;
  font-size: 14px;
  line-height: 20px;
  font-weight: 500;
}

.custom-tabs .nav-item button.active,
.tab-content,
.tab-content .form-control,
.tab-content .form-select {
  font-size: 14px;
  line-height: 20px;
  font-weight: 500;
  color: #5C5D6A;
}

.custom-tabs .nav-item button.active {
  color: #000117;
}

.tab-content .form-control,
.tab-content .form-select {
  font-weight: 400;
  padding: 10px 8px;
  color: #000117;
}

.tab-heading h2 {
  font-size: 20px;
  font-weight: 500;
  line-height: 28px;
  color: #000117;
}

.tab-heading p {
  font-size: 14px;
  line-height: 20px;
}

.upload-user-image img {
  max-width: 64px;
  height: 62px;
  border-radius: 50%;
}

.upload-button input {
  display: none;
}

.upload-button {
  padding: 12px 16px;
  background: #000117;
  color: #fff;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 400;
}

.remove-button.btn {
  line-height: 18px;
  background: #fff;
  color: #000117;
  border: 1px solid #DADDE5;
  padding: 12px 16px;
}

.username {
  width: 56px;
  height: 54px;
  border-radius: 50%;
  background: #363F5A;
  font-size: 24px;
  line-height: 32px;
  color: #fff;
  text-transform: uppercase;
}

.d-flex .save-btn {
  font-size: 14px;
  line-height: 16px;
  padding: 12px 22px;
  background-color: #000117;
  border: none;
}

.d-flex .save-btn:hover {
  background-color: #000117;
}

.save-btn.cancel-btn,
.save-btn.cancel-btn:hover {
  cursor: default;
  border-radius: 8px;
  background-color: #fff;
  color: #C6CCDC;
  border: 1px solid #C6CCDC;
}

.save-btn.cancel-btn.enabled {
  color: #000117;
  cursor: pointer;
}

.signup-pwd-field {
  border-right: 1px solid #DADDE5;
  border-radius: 0.375rem;
}

.tab-content .pwd-field-bg {
  border-right: 0px;
  background: #fff;
}

.subscription-title.card-title.h5 {
  font-size: 18px;
  font-weight: 700;
  line-height: 24px;
  color: #000117;
}

.payment-icon {
  width: 46px;
}

.tab-content .card {
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10),
    0px 1px 2px 0px rgba(0, 0, 0, 0.06);
}

a .download-btn,
a .download-btn:hover {
  color: #fff;
  font-size: 14px;
  padding: 8px 12px;
  background-color: #000117;
  border: #000117;
}

.css-i4bv87-MuiSvgIcon-root {
  font-size: 1.3rem !important;
}

.price-plan-modal .modal-dialog {
  max-width: 1200px;
}

.payment-checkout-modal .modal-dialog {
  max-width: 800px;
}

.price-plan-modal .button-toggle {
  width: fit-content;
  margin: 0 auto;
}

.payment-icon svg {
  width: 44px;
  height: 44px;
}

.trail-btn-row .upgrade-btn {
  padding: 8px 12px;
  border-radius: 6px;
  background-color: #C6CCDC !important;
  width: 100%;
  color: #747F9B !important;
  margin-top: 8px;
  font-size: 14px;
}

.none-select-btn.upgrade-btn {
  background: #000117 !important;
  color: #ffffff !important;
}

.row .price-plan-card {
  height: 442px;
  width: 270px;
}

.row .choose-plan-btn {
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #DADDE5;
  background: #FFF;
}

.col .add-payment-btn,
.col .add-payment-btn:hover {
  border-radius: 9px;
  padding: 14px;
  border: 1px solid #DADDE5;
  background: #fff;
  color: #000117;
}

.update-payment-modal .modal-dialog {
  max-width: 450px;
}

.cancel-modal.btn,
.cancel-modal.btn:hover {
  width: 100%;
  border-radius: 9px;
  border: 1px solid #DADDE5;
  background: #FFF;
  color: #000117;
}

.css-vv34gr-MuiTableRow-root th {
  color: #5C5D6A !important;
  font-weight: 500;
}