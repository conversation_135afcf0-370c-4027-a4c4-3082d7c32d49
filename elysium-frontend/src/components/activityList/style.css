.activity-list-container {
  padding: 16px;
  padding-right: 3px;
  display: flex;
  height: 744px;
  min-width: 20%;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 8px;
  border: 1px solid var(--shades-desaturate-10, #dadde5);
  background: var(--dark-light-light, #fff);
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.06);
}

.card-heading {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  align-self: stretch;
  color: var(--text-on-white-high-contrast, #000117);
  font-family: Ubuntu;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--dark-light-light, #fff);
  padding-right: 16px;
  position: sticky;
  top: 0;
  z-index: 1;
}

.clear-all-btn-activity {
  display: flex;
  height: 32px;
  align-items: center;
  border-radius: 6px;
  border: 1px solid var(--shades-desaturate-10, #dadde5);
  background: var(--dark-light-light, #fff);
  color: var(--text-on-white-high-contrast, #000117);
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}
.archiving-card {
  width: 100%;
}
.archiving-heading {
  color: var(--text-on-white-high-contrast, #000117);
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 142.857% */
  margin-left: 5px;
}
.archiving-time {
  color: var(--gray-600, #475467);
  font-family: Ubuntu;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0.24px;
}
.archiving-box {
  display: flex;
  /* width: ; */
  min-width: 186px;
  padding: 4px 8px;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 6px;
  border: 1px solid var(--shades-desaturate-10, #dadde5);
  background: var(--shades-desaturate-11, #f6f8fc);
}
.archiving-box-text {
  color: var(--shades-desaturate-6, #535d7b);
  font-family: Ubuntu;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0.24px;
  cursor: pointer;
}
.archiving-box-database {
  color: var(--info-main, #0288d1);
  font-family: Ubuntu;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0.24px;
  text-decoration-line: underline;
}
.archiving-box-card {
  margin-top: 16px;
}
.day-heading {
  color: #000;
  margin-bottom: 16px;
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  line-height: 20px;
}
.cloud-upload {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  border-radius: 50%;
  background-color: var(--shades-desaturate-9, #c6ccdc);
}
.cloud-upload-image {
  width: 16px;
  height: 16px;
}
.dash-border {
  height: 62px;
  margin-left: 9px;
  margin-right: 21px;
  margin-top: 15px;
  border: 1px dashed var(--shades-desaturate-11, #c6ccdc);
}
.started-margin {
  margin-left: 33px;
}

@media (max-width: 768px) {
  .activity-list-container {
    min-height: 793px;
  }
  .d-activity-container {
    width: 100%;
  }
}

.background-color {
  background: var(--shades-desaturate-11, #f6f8fc);
}

.scroll-margin {
  margin-right: 13px;
}

.fixed-header-container {
  height: calc(100% - 40px);
  overflow-y: auto;
}

.scroll-container {
  overflow-y: scroll;
}

.scroll-container::-webkit-scrollbar {
  width: 2px;
}
/* .scroll-container {
  scrollbar-width: thin !;
  scrollbar-color: var(--Shades-Desaturate-8, #9ba4bc) transparent;
} */

.scroll-container::-webkit-scrollbar-thumb {
  background-color: var(--shades-desaturate-8, #9ba4bc);
  border-radius: 5px;
  /* max-height: 100px;  */
}

.scroll-container.custom-scrollbar-thumb::-webkit-scrollbar-thumb {
  /* max-height: 100px;  */
}

.scroll-container::-webkit-scrollbar-thumb:vertical {
  height: 100px;
}

.scroll-container::-webkit-scrollbar-track {
  background-color: var(--dark-light-light, #fff);
}

.scroll-container::-webkit-scrollbar-button {
  display: none;
}

.scroll-container::-webkit-scrollbar-corner {
  background-color: transparent;
}

.no-archiving {
  color: var(--Shades-Desaturate-8, #9ba4bc);
  text-align: center;

  /* Paragraph/S/Regular */
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 142.857% */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 650px;
  gap: 22px;
}
