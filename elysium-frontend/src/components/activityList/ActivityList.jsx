import React, { useState, useEffect } from "react";
import { Card, Image } from "react-bootstrap";
import CheckMark from "./../../assets/images/checkmark.svg";
import CloudUpload from "./../../assets/images/cloud--upload.svg";
import Close from "./../../assets/images/close.svg";
import "./style.css";
import { formatDateDisplay, formatTimeDisplay } from "../../utils/helpers";
import { useNavigate } from "react-router";
import emptyIcon from "./../../assets/images/no-activity.svg";

const ActivityList = ({ activityLogs }) => {
  const navigate = useNavigate();
  const [activityData, setActivityData] = useState(activityLogs);
  const handleClearAll = () => {
    setActivityData([]);
  };

  useEffect(() => {
    setActivityData(activityLogs ?? []);
  }, [activityLogs]);

  console.log("actitvty-logs", activityData);

  return (
    <div className="activity-list-container">
      <Card.Header className="w-100 p-0 m-0 border-0 bg-transparent ">
        <div className="card-heading">
          Activity
          <button className="clear-all-btn-activity" onClick={handleClearAll}>
            Clear All
          </button>
        </div>
      </Card.Header>
      <Card.Body className="w-100 p-0 scroll-container custom-scrollbar-thumb">
        <div className="scroll-design">
          {activityData?.length === 0 ? (
            <div className="no-archiving">
              <img style={{ width: "90px", height: "90px" }} src={emptyIcon} alt="No activity icon" className="empty-icon" />
              <span style={{ width: "179px" }}>No activity logs are available at the moment.</span>
            </div>
          ) : (
            activityData?.map((actitvty, index) => {
              const status = actitvty?.export_history?.status;
              const isError = actitvty?.export_history?.is_error === 1;

              return (
                <React.Fragment key={index}>
                  <h1 className="day-heading mt-3">{formatDateDisplay(actitvty?.export_history?.started_at) === formatDateDisplay(activityData[index - 1]?.export_history?.started_at) ? <div style={{ marginBottom: "32px" }}></div> : formatDateDisplay(actitvty?.export_history?.started_at)}</h1>
                  <div className="w-100 p-0 mt-3" key={index}>
                    {status !== "Started" && (
                      <div className="w-100 p-0 m-0 border-0 bg-transparent">
                        <div className="d-flex justify-content-between scroll-margin align-items-center p-0">
                          <div className="d-flex gap-1 align-items-center">
                            <div className="cloud-upload">
                              <Image className="cloud-upload-image" src={!isError ? CheckMark : Close} alt="Icon" fluid />
                            </div>
                            <span className="archiving-heading">{!isError ? `Archiving Finished` : "Archiving Error"}</span>
                          </div>
                          <span className="archiving-time">{formatTimeDisplay(actitvty?.export_history?.ended_at)}</span>
                        </div>
                      </div>
                    )}

                    <div className="px-0 w-100 mt-1">
                      {status !== "Started" && (
                        <div className="d-flex g-2 align-items-center">
                          <div className="dash-border"></div>
                          <div className="archiving-box p-2">
                            <div className="archiving-box-text">
                              Database:{" "}
                              <span
                                onClick={() =>
                                  navigate("/database", {
                                    state: {
                                      tableId: actitvty?.table_id,
                                      id: actitvty?.database_id,
                                      serverId: actitvty?.server_id,
                                      tableName: actitvty?.table_name,
                                      name: actitvty?.database_name,
                                      serverName: actitvty?.server_name,
                                    },
                                  })
                                }
                                className="archiving-box-database"
                              >
                                {actitvty.database_name}
                              </span>
                            </div>
                            <div className="archiving-box-text">Table: [{actitvty?.table_name}]</div>
                            <div className="archiving-box-text">Time: {actitvty?.export_history?.total_time} min.</div>
                          </div>
                        </div>
                      )}

                      <div className="d-flex justify-content-between archiving-box-card align-items-center scroll-margin">
                        <div className="d-flex gap-2 align-items-center">
                          <div className="cloud-upload">
                            <Image className="cloud-upload-image" src={CloudUpload} alt="Icon" fluid />
                          </div>
                          <span className="archiving-heading">Archiving started</span>
                        </div>
                        <span className="archiving-time">{formatTimeDisplay(actitvty?.export_history?.started_at)}</span>
                      </div>

                      <div className="d-flex align-items-center started-margin mt-1">
                        <div className="archiving-box p-2 ml-1">
                          <div className="archiving-box-text">
                            Database:{" "}
                            <span
                              onClick={() =>
                                navigate("/database", {
                                  state: {
                                    tableId: actitvty?.table_id,
                                    id: actitvty?.database_id,
                                    serverId: actitvty?.server_id,
                                    tableName: actitvty?.table_name,
                                    name: actitvty?.database_name,
                                    serverName: actitvty?.server_name,
                                  },
                                })
                              }
                              className="archiving-box-database"
                            >
                              {actitvty.database_name}
                            </span>
                          </div>
                          <div className="archiving-box-text">Table: [{actitvty.table_name}]</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </React.Fragment>
              );
            })
          )}
        </div>
      </Card.Body>
    </div>
  );
};

export default ActivityList;
