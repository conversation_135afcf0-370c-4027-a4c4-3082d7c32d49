import React, { useState, useEffect } from "react";
import Header from "../../pages/header/Header";
import {  Row, Col } from "react-bootstrap";
import DateRangePicker from "../common/Calendar";
import { Link, useLocation, useNavigate } from "react-router-dom";
import <PERSON>rumbArrow from "./../../assets/images/chevron--right.svg";
import TablesLineChart from "./TablesLineCard";
import ArchivingTableDashboard from "./TableArchivingDashboard";
import {
  convertToISODate,
  convertToMonthName,
  formatDateIntoDays,
} from "../../utils/helpers";
import { serviceCall } from "../../services/dashboard";
import { calculateDaysFlag } from "../../constants/calender";
import Notification from "../notificationModal/Notification";
import Loader from "../loader/Loader";

function TablesDashboard() {
  const location = useLocation();
  const navigate = useNavigate();
  const serverId = location?.state?.serverId;
  const databaseId = location?.state?.databaseId;
  const tableName = location?.state?.tableName;
  const serverName = location?.state?.name;
  const databaseName = location?.state?.databaseName;
  const tableId = location?.state?.tableId
  const [responseMessage, setResponseMessage] = useState();
  const [showNotification, setShowNotification] = useState(false);
  const [isError, setIsError] = useState(false);
  const [open, setOpen] = useState(false);
  const [tableArchivedCardData, setTableArchivedCardData] = useState();
  const [tableArchivedBarPanelData, setTableArchivedBarPanelData] = useState({
    data: "",
    label: "",
  });
  const [tableArchivedListingPanelData, setTableArchivedListingPanelData] =
    useState();

  const getArchivedData = async (range) => {
    setOpen(true);
    try {
      const response = await serviceCall.getTableArchivedData(range);
      if (response?.status === "Success") {
        setTableArchivedCardData(response?.data?.Table_total_stats[0]);
      } else {
        setTableArchivedCardData();
        setResponseMessage(
          response?.messages
            ? response?.messages
            : response?.response.data?.message || "Something went wrong"
        );
        setIsError(true);
        setShowNotification(true);
      }
    } catch (error) {
     
    } finally {
      setOpen(false);
    }
  };

  const getTableLineData = async (range) => {
    setOpen(true);
    try {
      const response = await serviceCall.getTableBarData(range);
      if (response?.status === "Success") {
        const dateParts = response?.data?.table_lineChart[0]?.dates[0]?.split("-");
        let label;
        if (dateParts?.length === 2) {
          label = await convertToMonthName(response?.data?.table_lineChart[0]?.dates);
        } else if (dateParts?.length === 3) {
          label = await formatDateIntoDays(response?.data?.table_lineChart[0]?.dates);
        } else if (dateParts?.length === 1) {
          label = response?.data?.table_lineChart[0]?.dates;
        }
        setTableArchivedBarPanelData({
          data: response.data?.table_lineChart[1],
          label: label,
        });
      } else {
        setTableArchivedBarPanelData({
          data: "",
          label: "",
        });
        setResponseMessage(
          response?.message
            ? response?.message
            : response?.response?.data?.message || "Something went wrong"
        );
        setIsError(true);
        setShowNotification(true);
      }
    } catch (error) {
    
    } finally {
      setOpen(false);
    }
  };
  

  const getTableData = async (range) => {
    setOpen(true);
    try {
      const response = await serviceCall.getTableListingData(range);
      if (response?.status === "Success") {
        setTableArchivedListingPanelData(response?.data?.table_activities);
      } else {
        setResponseMessage(
          response?.messages
            ? response?.messages
            : response?.response.data?.message || "Something went wrong"
        );
        setIsError(true);
        setShowNotification(true);
        setTableArchivedListingPanelData("");
      }
    } catch (error) {
     
    } finally {
      setOpen(false);
    }
  };

  const handleDateRangeChange = async (date) => {
    const range = await convertToISODate(date);
    const daysInRange =  calculateDaysFlag(
      range?.start_date,
      range?.end_date
    );

    if (range?.start_date && range?.end_date) {
      const payload = {
        start_date: range?.start_date,
        end_date: range?.end_date,
        flag: daysInRange,
        table_id: tableId,
        server_id: serverId,
        database_id: databaseId,
      };
      getArchivedData(payload);
      getTableLineData(payload);
      getTableData(payload);
    }
  };

  const handleToastClose = () => {
    setShowNotification(false);
    setResponseMessage("");
    setIsError(false);
  };
/* eslint-disable react-hooks/exhaustive-deps */

  useEffect(() => {
    if (!serverId || !serverName || !databaseId || !databaseName || !tableId) {
      navigate("/");
    }
  }, [serverId, serverName, databaseId, tableName, serverName, databaseName]); 

  return (
    <>
      <Header />
      <Loader open={open} />
      <div className="server-dashboard-container">
        <Row>
          <Col xs={12}>
            <div className="server-breadcrumb">
              <Link to="/dashboard" className="breadcrumb-link">
                Dashboard
              </Link>
              <img
                style={{ height: "12px", width: "12px" }}
                alt=">"
                src={CrumbArrow}
              />
              <span
                onClick={() =>
                  navigate("/server", {
                    state: {
                      server_id: serverId,
                      server_name: serverName,
                    },
                  })
                }
                className="breadcrumb-link"
              >
                {serverName}
              </span>
              <img
                style={{ height: "12px", width: "12px" }}
                alt=">"
                src={CrumbArrow}
              />
              <span
                onClick={() =>
                  navigate("/database", {
                    state: {
                      id: databaseId,
                      serverId: serverId,
                      name: databaseName,
                      serverName: serverName,
                    },
                  })
                }
                className="breadcrumb-link"
              >
                {databaseName}
              </span>

              <img
                style={{ height: "12px", width: "12px" }}
                alt=">"
                src={CrumbArrow}
              />
              <span className="active-link-crumb">{tableName}</span>
            </div>
          </Col>
          <Col xs={12}>
            <h5 className="dashboard-heading-databases">
              {tableName} Statisctics
            </h5>
          </Col>
        </Row>
        <Row style={{ width: "100%" }} className="m-0 p-0">
          <div className="server-bar-width">
            <div className="archived-header">
              <h6 className="archived-heading">Database Archived Data</h6>
              <DateRangePicker handleDateRangeChange={handleDateRangeChange} />
            </div>
            <TablesLineChart
              archivedTableData={tableArchivedCardData}
              tableLineData={tableArchivedBarPanelData?.data}
              labels={tableArchivedBarPanelData?.label}
            />
            <ArchivingTableDashboard
              heading={"Database Table Archiving Activities"}
              data={tableArchivedListingPanelData}
            />
          </div>
        </Row>
        {showNotification && (
          <Notification
            responseMessage={responseMessage}
            isError={isError}
            handleToastClose={handleToastClose}
          />
        )}
      </div>
    </>
  );
}

export default TablesDashboard;
