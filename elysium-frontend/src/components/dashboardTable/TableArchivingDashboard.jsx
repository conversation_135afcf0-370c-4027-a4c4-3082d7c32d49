import React, { useEffect, useState } from "react";
import <PERSON><PERSON>ef<PERSON> from "./../../assets/images/arrow--left.svg";
import ArrowRight from "./../../assets/images/arrow--right.svg";
import DownArrow from "./../../assets/images/chevron--sort--down.svg";
import UpArrow from "./../../assets/images/chevron--sort.svg";
import ArrowRightVisible from "./../../assets/images/arrow--right-black.svg";
import ArrowleftVisible from "./../../assets/images/arrow-left-black.svg";
import { useNavigate } from "react-router";

const itemsPerPage = 4;

const ArchivingTableDashboard = ({ heading, data }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [sortedData, setSortedData] = useState(data);
  const [sortOrder, setSortOrder] = useState({
    datetime: false,
    total_rows_archive: false,
    total_file_size: false,
    dir_name: false,
  });
  const navigate = useNavigate();
  const lastPageIndex = Math.ceil(sortedData?.length / itemsPerPage);

  const sortData = (column) => {
    const newData = [...sortedData].sort((a, b) => {
      switch (column) {
        case "datetime":
          return sortOrder.datetime
            ? new Date(a[column]) - new Date(b[column])
            : new Date(b[column]) - new Date(a[column]);
        case "total_rows_archive":
          return sortOrder.total_rows_archive
            ? parseInt(b.total_rows_archive) - parseInt(a.total_rows_archive)
            : parseInt(a.total_rows_archive) - parseInt(b.total_rows_archive);
        case "total_file_size":
          const sizeA = parseInt(a[column]);
          const sizeB = parseInt(b[column]);
          setSortOrder((prevSortOrder) => ({
            ...prevSortOrder,
            total_file_size: !prevSortOrder.total_file_size,
          }));
          return sortOrder.total_file_size ? sizeA - sizeB : sizeB - sizeA;
        case "dir_name":
          return sortOrder.dir_name
            ? a[column].localeCompare(b[column])
            : b[column].localeCompare(a[column]);
        default:
          return 0;
      }
    });

    setSortedData(newData);
  };

  const renderData = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return sortedData?.slice(startIndex, endIndex);
  };

  const handleSort = (column) => {
    if (!Array.isArray(sortedData)) {
      return;
    }
    sortData(column);
    setSortOrder((prevSortOrder) => ({
      ...Object.keys(prevSortOrder).reduce((acc, key) => {
        acc[key] = key === column ? !prevSortOrder[key] : false;
        return acc;
      }, {}),
    }));
  };

  const handlePrevious = () => {
    setCurrentPage((prevPage) => Math.max(1, prevPage - 1));
  };

  const handleNext = () => {
    setCurrentPage((prevPage) => Math.min(lastPageIndex, prevPage + 1));
  };

  useEffect(() => {
    setSortedData(data ?? []);
  }, [data]);

  return (
    <>
      <div className="server-container-chart server-table-bg">
        <div className="archiving-table-header">{heading}</div>
        <div style={{ overflowX: "auto" }}>
          <table
            className="table-container"
            style={{ overflowX: "scroll", width: "100%" }}
          >
            <tbody>
              <tr className="header-container-border">
                <th style={{ paddingLeft: "24px", minWidth: "364px" }}>
                  <div className="header-container ">
                    <span className="">
                      Datetime
                      <img
                        className="sorting-table-icons"
                        onClick={() => handleSort("datetime")}
                        src={sortOrder.datetime ? UpArrow : DownArrow}
                        alt="sort"
                      />
                    </span>
                    <span className="border-table-server"></span>
                  </div>
                </th>
                <th style={{ minWidth: "272px" }} className="">
                  <div className="header-container ">
                    <span className="server-table-heading">
                      <span>Total Rows Archived</span>
                      <img
                        className="sorting-table-icons"
                        onClick={() => handleSort("total_rows_archive")}
                        src={sortOrder.total_rows_archive ? UpArrow : DownArrow}
                        alt="sort"
                      />
                    </span>
                    <span className="border-table"></span>
                  </div>
                </th>
                <th style={{ minWidth: "272px" }}>
                  <div className="header-container ">
                    <span className="server-table-heading">
                      <div>Total File Size</div>
                      <img
                        className="sorting-table-icons"
                        onClick={() => handleSort("total_file_size")}
                        src={sortOrder.total_file_size ? UpArrow : DownArrow}
                        alt="sort"
                      />
                    </span>
                    <span className="border-table-server"></span>
                  </div>
                </th>
                <th style={{ minWidth: "412px" }}>
                  <span className="server-table-heading">
                    <div>Location</div>
                    <img
                      className="sorting-table-icons"
                      onClick={() => handleSort("dir_name")}
                      src={sortOrder.dir_name ? UpArrow : DownArrow}
                      alt="sort"
                    />
                  </span>
                </th>
              </tr>
              {renderData()?.length === 0 ? (
                <tr style={{ height: "184px" }}>
                  <td colSpan="8">
                    <div className="no-atabase-selected-container">
                      <div className="no-atabase-selected-container-text">
                        <div>
                          No database table archiving activities available
                        </div>{" "}
                      </div>
                    </div>
                  </td>
                </tr>
              ) : (
                renderData()?.map((server, index) => {
                  return (
                    <tr key={index} className="table-rows">
                      <td style={{ paddingLeft: "24px" }}>
                        {" "}
                        <div
                          onClick={() => navigate(server)}
                          className="table-rows-color"
                          style={{
                            display: "flex",
                            alignItems: "center",
                            gap: "8px",
                          }}
                        >
                          {server?.datetime?.split("T")[0]}
                        </div>
                      </td>
                      <td className="table-rows-color">
                        <span>{server?.total_rows_archive}</span>
                      </td>
                      <td className="table-rows-color">
                        <span>{server?.total_file_size}MB</span>
                      </td>
                      <td className="table-rows-color">
                        <span>{server?.dir_name}</span>
                      </td>
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>

        <div className="archiving-table-footer">
          <button
            className={`archiving-table-footer-button ${
              currentPage > 1 ? "active" : ""
            }`}
            onClick={handlePrevious}
            disabled={currentPage === 1}
          >
            {" "}
            <img
              className="btns-arrow"
              alt=">"
              src={currentPage > 1 ? ArrowleftVisible : ArrowLeft}
            />
            <span>Previous</span>{" "}
          </button>
          <span className="table-count">{currentPage}</span>
          <button
            className={`archiving-table-footer-button ${
              data?.length > 3
                ? currentPage !== lastPageIndex
                  ? "active"
                  : ""
                : ""
            }`}
            onClick={handleNext}
            disabled={currentPage === lastPageIndex}
          >
            {" "}
            <span>Next</span>{" "}
            <img
              className="btns-arrow"
              alt=">"
              src={
                data?.length > 3
                  ? currentPage !== lastPageIndex
                    ? ArrowRightVisible
                    : ArrowRight
                  : ArrowRight
              }
            />{" "}
          </button>
        </div>
      </div>
    </>
  );
};

export default ArchivingTableDashboard;
