import React, { useState, useEffect } from "react";
import { Col, Card } from "react-bootstrap";
import InfoCard from "../common/InfoCard";
import UploadImae from "./../../assets/images/fetch-upload--cloud.svg";
import TableImage from "./../../assets/images/table--split (1).svg";
import { LineChart } from "../charts/LineChart";
import {
  addEmptyStrings,
  duplicateValuesWithNulls,
  extractServerDataValues,
} from "../../utils/helpers";

const TablesLineChart = ({ archivedTableData, tableLineData, labels }) => {
  const [linelabels, setLineLabels] = useState([]);
  const [maxSize, setMaxSize] = useState();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  // Process tableLineData to create chart data objects
  const data = tableLineData
    ? tableLineData?.map((server, index) => {
        const data = server?.data;
        const values = extractServerDataValues(data);
        // values.push(null);
        // values.unshift(null);
        const final = duplicateValuesWithNulls(values);
        return {
          label: server?.name,
          data:
            labels?.length < 8 && windowWidth > 490
              ? final
              : [null, ...values, null],
          borderColor: "#1E88E5",
          backgroundColor: "#1E88E5",
        };
      })
    : [];

  // Calculate the maximum sum for the chart based on tableLineData and labels
  useEffect(() => {
    if (!tableLineData || !tableLineData[0]?.data) {
      return;
    }

    let maxSum = 0;
    const keys = Object.keys(tableLineData[0].data);

    // Calculate the total sum if there is only one label
    if (labels?.length === 1) {
      const totalSum = tableLineData.reduce((accumulator, server) => {
        const value = Object.values(server?.data);
        return (
          accumulator +
          (Array.isArray(value) ? value.reduce((sum, v) => sum + v, 0) : 0)
        );
      }, 0);
      maxSum = totalSum;
    }

    // Calculate the maximum sum for each key in the data
    if (keys.length > 0) {
      keys.forEach((key) => {
        const sumAtIndex = tableLineData.reduce((accumulator, server) => {
          const value = server?.data?.[key];
          return accumulator + (value !== undefined ? value : 0);
        }, 0);
        maxSum = Math.max(maxSum, sumAtIndex);
      });
    }

    // Round up the maximum value to the nearest multiple of 5
    const roundedMaxValue = Math.ceil(maxSum / 5) * 5;
    setMaxSize(roundedMaxValue);
  }, [tableLineData, labels]);

  // Set line labels based on the length of labels array
  useEffect(() => {
    // Add empty strings at the start and end of labels array
    if (labels?.length < 8 && labels?.length > 0) {
      const finalLabels = addEmptyStrings(labels);
      setLineLabels(finalLabels);
    } else if (labels?.length > 8) {
      const modifiedLabels = ["", ...labels, ""];
      setLineLabels(modifiedLabels);
    }
  }, [labels]);

  const handleResize = () => {
    setWindowWidth(window.innerWidth);
  };

  // Handle window resize to adjust the number of displayed calendars
  useEffect(() => {
    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [windowWidth,]);

  useEffect(() => {
    if (windowWidth <= 490) {
      if (labels?.length > 0) {
        const modifiedLabels = ["", ...labels, ""];
        setLineLabels(modifiedLabels);
      }
   
    }else{
      if (labels?.length < 8 && labels?.length > 0) {
        const finalLabels = addEmptyStrings(labels);
        setLineLabels(finalLabels);
      } else if (labels?.length > 0) {
        const modifiedLabels = ["", ...labels, ""];
        setLineLabels(modifiedLabels);
      }

    }
  }, [windowWidth]);
  
   return (
    <Card className="server-bar-container p-0">
      <Card.Header className="w-100 p-0 m-0 border-0">
        <div className=" px-0  my-custom-gutter-row">
          <Col className="mx-0" style={{ paddingRight: "0px" }}>
            <InfoCard
              content={"Total Archived Data"}
              count={
                archivedTableData?.total_data_archived
                  ? `${parseFloat(
                      archivedTableData?.total_data_archived
                    ).toFixed(2)} MB`
                  : "0 MB"
              }
              logo={UploadImae}
            />
          </Col>
          <Col className="mx-0" style={{ paddingRight: "0px" }}>
            <InfoCard
              content={"Total Archived Rows"}
              count={
                archivedTableData?.total_rows
                  ? archivedTableData?.total_rows
                  : "0"
              }
              logo={TableImage}
            />
          </Col>
        </div>
      </Card.Header>
      <div className="body-chart-container ">
        <div className="stack-bar-container">
          <LineChart labels={linelabels} datasets={data} maxSize={maxSize} />
        </div>
      </div>
    </Card>
  );
};

export default TablesLineChart;
