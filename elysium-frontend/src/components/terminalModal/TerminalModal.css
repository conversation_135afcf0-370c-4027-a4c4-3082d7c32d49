.terminal-modal .modal-content {
  background-color: #1e1e1e;
  color: #f0f0f0;
  border-radius: 8px;
}

.terminal-modal .modal-header {
  border-bottom: 1px solid #333;
  padding: 12px 20px;
}

.terminal-modal .modal-title {
  color: #f0f0f0;
  font-family: 'Ubuntu Mono', monospace;
  font-size: 16px;
}

.terminal-modal .close {
  color: #f0f0f0;
}

.terminal-window {
  background-color: #1e1e1e;
  font-family: 'Ubuntu Mono', monospace;
  padding: 10px;
  height: 400px;
  overflow-y: auto;
  border-radius: 4px;
}

.terminal-output {
  white-space: pre-wrap;
  word-break: break-word;
}

.log-line {
  padding: 2px 0;
  line-height: 1.4;
  font-size: 14px;
}

.prompt {
  color: #4caf50;
  font-weight: bold;
  margin-right: 5px;
}

.command {
  color: #4caf50;
}

.info {
  color: #42a5f5;
}

.success {
  color: #4caf50;
}

.error {
  color: #f44336;
}

.warning {
  color: #ff9800;
}

.system {
  color: #9e9e9e;
  font-style: italic;
}

/* Add styles for the diagnostic button */
.diagnostic-button {
  display: flex;
  align-items: center;
  gap: 5px;
}

.diagnostic-button svg {
  font-size: 18px;
}

.terminal-modal .modal-footer {
  border-top: 1px solid #333;
  padding: 12px 20px;
}

.terminal-modal .btn-secondary {
  background-color: #444;
  border-color: #555;
}

.terminal-modal .btn-secondary:hover {
  background-color: #555;
  border-color: #666;
}

.terminal-modal .btn-info {
  background-color: #0c5460;
  border-color: #0c5460;
}

.terminal-modal .btn-info:hover {
  background-color: #138496;
  border-color: #138496;
}
