import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, But<PERSON> } from 'react-bootstrap';
import { TbNetwork } from 'react-icons/tb';
import { serverInfo } from '../../services/serversInfo';
import { dbServer } from '../../services/dbServers';
import './TerminalModal.css';

const TerminalModal = ({ show, onHide, logs, title, agentId, currentDbServerId }) => {
  const terminalRef = useRef(null);
  const [isRunningDiagnostics, setIsRunningDiagnostics] = useState(false);
  const [showDiagnosticButton, setShowDiagnosticButton] = useState(false);

  useEffect(() => {
    // Auto-scroll to bottom when logs update
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
    }
    
    // Check if we should show the diagnostic button
    const hasTimeoutError = logs.some(log => 
      log.message.includes('timed out') || 
      log.message.includes('Cannot connect')
    );
    
    setShowDiagnosticButton(hasTimeoutError);
  }, [logs]);

  const runNetworkDiagnostics = async () => {
    // Determine if we're dealing with an agent or a database server
    const id = agentId || currentDbServerId;
    if (!id) return;
    
    setIsRunningDiagnostics(true);
    
    // Add a message to indicate diagnostics are running
    const newLog = { type: 'system', message: 'Running advanced network diagnostics...' };
    logs.push(newLog);
    
    try {
      let response;
      
      // Call the appropriate API based on what type of ID we have
      if (agentId) {
        response = await serverInfo.runNetworkDiagnostics(agentId);
      } else if (currentDbServerId) {
        response = await dbServer.runDBNetworkDiagnostics(currentDbServerId);
      }
      
      if (response && response.data && response.data.logs) {
        // Process and add diagnostic logs
        const diagnosticLogs = response.data.logs.map(log => {
          // Determine log type based on content
          let type = 'info';
          if (log.includes('Error') || log.includes('ERROR')) {
            type = 'error';
          } else if (log.includes('WARNING')) {
            type = 'warning';
          } else if (log.includes('successful') || log.includes('Success')) {
            type = 'success';
          } else if (log.includes('Attempting') || log.includes('ping') || log.includes('traceroute') || log.includes('nc -z')) {
            type = 'command';
          } else if (log.includes('Recommendations') || log.includes('suggests')) {
            type = 'warning';
          }
          
          return { type, message: log };
        });
        
        // Add a separator
        logs.push({ type: 'system', message: '--- Advanced Network Diagnostics ---' });
        
        // Add all diagnostic logs
        logs.push(...diagnosticLogs);
      } else {
        logs.push({ type: 'error', message: 'Failed to run network diagnostics.' });
      }
    } catch (error) {
      logs.push({ type: 'error', message: 'Error running diagnostics: ' + error.message });
    } finally {
      setIsRunningDiagnostics(false);
      
      // Force update to scroll to bottom
      if (terminalRef.current) {
        terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
      }
    }
  };

  return (
    <Modal
      show={show}
      onHide={onHide}
      size="lg"
      aria-labelledby="terminal-modal"
      centered
      className="terminal-modal"
    >
      <Modal.Header closeButton>
        <Modal.Title id="terminal-modal">
          {title || 'Connection Terminal'}
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <div className="terminal-window" ref={terminalRef}>
          <div className="terminal-output">
            {logs.map((log, index) => (
              <div key={index} className={`log-line ${log.type}`}>
                {log.type === 'command' && <span className="prompt">$ </span>}
                {log.message}
              </div>
            ))}
            {logs.length === 0 && (
              <div className="log-line info">Initializing connection...</div>
            )}
          </div>
        </div>
      </Modal.Body>
      <Modal.Footer>
        {showDiagnosticButton && (
          <Button 
            variant="info" 
            onClick={runNetworkDiagnostics}
            disabled={isRunningDiagnostics}
            className="diagnostic-button"
          >
            <TbNetwork /> {isRunningDiagnostics ? 'Running Diagnostics...' : 'Run Network Diagnostics'}
          </Button>
        )}
        <Button variant="secondary" onClick={onHide}>
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default TerminalModal;
