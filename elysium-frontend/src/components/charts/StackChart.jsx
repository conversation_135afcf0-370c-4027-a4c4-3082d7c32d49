import React, { useEffect, useState } from "react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import "./style.css";
import { Bar } from "react-chartjs-2";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

export function StackChart({ labels, datasets, maxSize }) {
  const [maxValue, setMaxValue] = useState(0);
  const [stepSize, setStepSize] = useState(0);

  useEffect(() => {
    if (maxSize > 0) {
      setMaxValue(Math.ceil(maxSize));
    } else {
      setMaxValue(1000);
    }
  }, [maxSize, datasets]);
  useEffect(() => {
    const stepSize = Math.ceil(maxValue / 5);
    setStepSize(stepSize);
  }, [maxValue]);

  const options = {
    maintainAspectRatio: false,
    responsive: true,
    // events: ["mousemove", "mouseout", "click", "touchstart", "touchmove"],
    plugins: {
      datalabels: {
        display: true,
        color: "black",
      },
      legend: {
        display: false,
      },
      tooltip: {
        enabled: false,
        // events: ['mousemove', 'click', 'touchstart', 'touchmove'],
        external: function (context) {
          let tooltipEl = document.getElementById("chartjs-tooltip");

          if (!tooltipEl) {
            tooltipEl = document.createElement("div");
            tooltipEl.id = "chartjs-tooltip";
            tooltipEl.innerHTML = `
              <div style="min-width: 262px; max-height: 200px; direction: ltr;  margin-right: 2px; border-radius: 4px;  padding: 8px;">
                <div style="width: 100%;"></div>
                              </div>`;
            document.body.appendChild(tooltipEl);
          }

          const tooltipModel = context.tooltip;
          if (tooltipModel.opacity === 0) {
            tooltipEl.style.opacity = 0;
            return;
          }

          tooltipEl.classList.remove("above", "below", "no-transform");
          if (tooltipModel.yAlign) {
            tooltipEl.classList.add(tooltipModel.yAlign);
          } else {
            tooltipEl.classList.add("no-transform");
          }

          function getBody(bodyItem) {
            return bodyItem.lines;
          }

          if (tooltipModel.body) {
            const titleLines = tooltipModel.title || [];
            const bodyLines = tooltipModel.body.map(getBody);
            bodyLines.reverse();
            var sum = context.tooltip.body.reduce((acc, bodyItem) => {
              const valueString = bodyItem.lines[0].split(":")[1].trim();
              const value = parseFloat(valueString.replace(",", ""));

              return acc + value;
            }, 0);

            let innerHtml =
              '<div style="display: flex; justify-content: space-between; margin-bottom:8px;   max-height: 200px; overflow-y: auto; "><span class="tooltip-label">' +
              titleLines.join(" ") +
              `</span><span class="tooltip-total"> ${sum.toFixed(
                2
              )} MB</span></div>`;

            const labelColors = tooltipModel.labelColors?.reverse();
            bodyLines.forEach(function (body, i) {
              const line = body[0];
              const valueString = line.split(":")[1].trim();
              const value = parseFloat(valueString.replace(",", ""));
              const name = line.split(": ")[0].trim();
              const percentage =
                value !== 0 ? ((value / sum) * 100) : 0;
                          const colors = labelColors[i];
              const spanStyle =
                "width: 100%; display: flex; justify-content: space-between; align-items: center;";

              const span = `<span style="${spanStyle}"><div style="display:flex; align-items:center; justify-content: center; gap:8px"><div style="background:${
                colors.backgroundColor
              }; border-radius: 50%;  width: 8px; height: 8px;"></div><span class="tooltip-body">${name}</span></div><span class="tooltip-body-value">${value.toFixed(
                2
              )} MB</span><span class="tooltip-percentage">${percentage.toFixed(2)}%</span></span>`;

              innerHtml +=
                "<div style='width: 100%; margin-bottom:4px'>" +
                span +
                "</div>";
            });

            innerHtml += "</span>";

            let tableRoot = tooltipEl.querySelector("div");
            tableRoot.innerHTML = innerHtml;
          }

          const position = context.chart.canvas.getBoundingClientRect();
          tooltipEl.style.opacity = 1;
          tooltipEl.style.position = "absolute";

          tooltipEl.style.borderRadius = "4px";
          tooltipEl.style.border =
            "1px solid var(--Shades-Desaturate-10, #DADDE5)";
          tooltipEl.style.background = "var(--dark-light-light, #FFF)";
          tooltipEl.style.boxShadow =
            "0px 12px 16px -4px rgba(0, 0, 0, 0.08), 0px 4px 6px -2px rgba(0, 0, 0, 0.03)";

          let leftPosition =
            position.left +
            window.scrollX +
            tooltipModel.caretX -
            tooltipModel.width / 2 +
            (labels?.length > 27 ? 40 : 20);

          const tooltipWidth = tooltipEl.offsetWidth;
          if (leftPosition + tooltipWidth > window.innerWidth) {
            leftPosition = window.innerWidth - tooltipWidth - 100;
          }

          tooltipEl.style.left = leftPosition + "px";

          let topPosition =
            position.top + window.scrollY + tooltipModel.caretY - 50;

          if (topPosition < position.top + window.scrollY) {
            topPosition = position.top + window.scrollY + 10;
          }

          tooltipEl.style.top = topPosition + "px";

          tooltipEl.style.padding =
            tooltipModel.padding + "px " + tooltipModel.padding + "px";
          tooltipEl.style.pointerEvents = "none";
          tooltipEl.style.overflowY = "scroll";
          tooltipEl.style.scrollBehavior = "smooth";
        },
      },
      title: {
        display: false,
      },
    },
    includeInvisible: true,
    interaction: {
      mode: "index",
      // axis: "x",
      // mode: 'point',
      // mode: 'nearest',
      // mode: 'dataset'
      intersect: false,
    },
    scales: {
      x: {
        stacked: true,
        grid: {
          display: false,
        },
        ticks: {
          font: {
            family: "Ubuntu",
            size: "12",
            weight: "500",
            style: "normal",
            height: "20px",
            letterSpacing: "0.24px",
            textAlign: "center",
          },
          color: "var(--Text-On-White-High-Contrast, #000117)",
          weight: 500,
        },
      },
      y: {
        // reverse: true, // will reverse the scale

        border: {
          display: false,
        },
        stacked: true,
        beginAtZero: true,
        max: maxValue,
        ticks: {
          reverse: true,
          beginAtZero: true,
          fontColor: "white",
          stepSize: stepSize,
          callback: (value) => `${value.toLocaleString()} MB`,
          font: {
            family: "Ubuntu",
            size: "12px",
            style: "normal",
            weight: "400",
            letterSpacing: "0.24px",
            color: "#000117",
            textAlign: "center",
          },
          color: "var(--Text-On-White-High-Contrast, #000117)",
        },
        grid: {
          drawBorder: false,
          borderSkipped: "left",
        },
      },
    },
    layout: {
      padding: {
        right: 0,
        top: 0,
      },
    },
    barPercentage:
      labels?.length > 12
        ? 80 / 70
        : labels?.length > 1 && labels?.length < 8
        ? 67 / 100
        : 1,
    categoryPercentage: labels?.length > 1 ? 0.7 : 0.95,
  };

  const data = {
    labels,
    datasets: datasets?.reverse(),
  };

  return (
    <div
      style={{
        minHeight: "230px",
        width: "100%",
        marginTop: "",
        marginBottom: "-20px",
      }}
    >
      <Bar options={options} data={data} />
    </div>
  );
}
