import React, { useEffect, useState } from "react";
import { Chart as ChartJS, ArcElement, <PERSON><PERSON><PERSON>, Legend } from "chart.js";
import { Doughnut } from "react-chartjs-2";
import { colors } from "../../constants/colors";

ChartJS.register(ArcE<PERSON>, Tooltip, Legend);

export function DoughnutBar({ doughnutChartData }) {
  const [pieDataSet, setPieDataSet] = useState([]);
  const [total, setTotal] = useState(0);
  useEffect(() => {
    if (doughnutChartData) {
      setPieDataSet(
        doughnutChartData?.map((server) => server?.total_storage_archived)
      );
    }
  }, [doughnutChartData]);
  const totalCount = doughnutChartData?.map(
    (server) => server?.total_single_server_archived || 0
  );
  const totalArchived = totalCount?.reduce((acc, value) => acc + value, 0);
  const isEmptyData =
    totalArchived === 0 ||
    totalArchived === undefined ||
    totalArchived === null ||
    totalArchived === "";

    const generateColors = (length) => {
      return Array.from({ length }, (_, i) => colors[i % colors.length]);
    };
    
    const data = {
      labels: isEmptyData ? ["No Server"] : ["server 1", "server 2", "server 3"],
      datasets: [
        {
          label: "MB",
          data: isEmptyData ? [1] : pieDataSet,
          backgroundColor: isEmptyData ? ["#DADDE5"] : generateColors(pieDataSet.length),
          borderColor: isEmptyData ? ["#DADDE5"] : generateColors(pieDataSet.length),
          borderWidth: 1,
        },
      ],
    };
    
  const options = {
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: false,
      },
    },
    responsive: true,
    maintainAspectRatio: true,
    width: "100%",
    height: "100%",
  };

  useEffect(() => {
    setTotal(totalArchived);
  }, [totalArchived, isEmptyData]);

  return (
    <div className="pie-chart-container-dognut">
      <Doughnut data={data} options={options} />
      <div className="pie-absolute-text">
        <div>{total ? total.toFixed(2) : "0.00"}</div> MB
      </div>
    </div>
  );
}
