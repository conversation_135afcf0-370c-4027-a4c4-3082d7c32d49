.chart-card-container {
  min-width: 80%;
  border: 1px solid #bcc2d3;
  border-radius: 10px;
  overflow: hidden;
  background-color: white;
  box-shadow: 0 0 10px rgba(216, 157, 157, 0.1);
}

.cards-container {
  display: flex;
  justify-content: space-between;
}
.bottom-chart-bar-container {
  display: flex;
  justify-content: space-between;
  border-top: 2px solid #bcc2d3;
  padding: 6%;
  margin-top: 6%;
  min-width: 800px;
  margin-left: 2%;
  margin-right: 2%;
}
.progress-bar-container {
  width: 500px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-bar-container > :first-child {
  margin-right: 2%;
}

.three-progress-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.chart-container {
  width: 70%;
}

.table-container-chart {
  width: 100%;
  border: 2px solid var(--shades-desaturate-9, #dadde5);
  border-radius: 8px;
}

.border-table {
  border: 1px solid #c6ccdc;
  margin-left: 12px;
  /* margin-right: 12px; */
  height: 24px;
}

.table-container {
  border-collapse: collapse;
  min-width: 700px;
  overflow-x: scroll;
  width: 100%; /* Set initial width to 100% */
}

th,
td {
  text-align: left;
  padding: 8px;
}
.table-rows {
  color: var(--text-on-white-medium-contrast, #5c5d6a);

  /* Paragraph/S/Regular */
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}
.table-rows-color {
  color: #000117;
}
.table-rows-link {
  color: var(--Link, #174be6);

  /* Link/S/Regular */
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 142.857% */
  text-decoration-line: underline;
  cursor: pointer;
}
tr {
  width: 100%;
}
th {
  border-bottom: 1px solid var(--shades-desaturate-9, #c6ccdc);
  color: var(--text-on-white-medium-contrast, #5c5d6a);
  font-family: Ubuntu;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px; /* 166.667% */
  letter-spacing: 0.24px;
}

tr:nth-child(even) {
  background-color: #f6f8fc;
}

.header-container {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
}
.progress-percentage {
  display: flex;
  align-items: center;
  gap: 5px;
}

@media (max-width: 1120px) {
  .table-container-chart {
    min-width: 0px;
    overflow-x: scroll;
  }
}
.server-name {
  min-width: 107px;
}

.server-storage {
  min-width: 156px;
}

.storage-archived-heading {
  width: 153px;
}
.cost-heading {
  width: 122px;
}

.tooltip-body {
  color: var(--Text-On-White-High-Contrast, #000117);
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}

.tooltip-body-value {
  color: var(--Text-On-White-High-Contrast, #000117);
  font-family: Ubuntu;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0.24px;
  margin-left: 5px;
  margin-right: 5px;
}
.convas-container {
  height: "230px";
  width: "100%";
  margin-bottom: "-20px";
}

.tooltip-percentage {
  color: var(--Text-On-White-Medium-Contrast, #5c5d6a);
  font-family: Ubuntu;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 166.667% */
  letter-spacing: 0.24px;
}
.tooltip-label {
  color: var(--Text-On-White-High-Contrast, #000117);
  text-align: center;
  font-family: Ubuntu;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0.24px;
}

.tooltip-total {
  color: var(--Text-On-White-High-Contrast, #000117);

  /* Caption/Regular */
  font-family: Ubuntu;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px; /* 166.667% */
  letter-spacing: 0.24px;
}
.pie-chart-container-dognut {
  position: relative;
  width: 200px;
  height: 200px;
}

.pie-absolute-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: var(--Text-On-White-High-Contrast, #000117);
  font-family: Ubuntu;
  font-weight: 500;
  font-size: 16px;
}

.progress-table-server-lengh {
  display: flex;
  height: 24px;
  padding: 12px 24px;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  border-top: 1px solid var(--Shades-Desaturate-10, #dadde5);
}

.server-length {
  color: var(--Text-On-White-Medium-Contrast, #5c5d6a);

  /* Caption/Regular */
  font-family: Ubuntu;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 166.667% */
  letter-spacing: 0.24px;
}

.pagination-number {
  color: var(--Text-On-White-Medium-Contrast, #5c5d6a);
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 142.857% */
  display: flex;
  width: 32px;
  padding: 12px;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  align-self: stretch;
  cursor: pointer;
}
.pagination-number-active {
  display: flex;
  width: 32px;
  padding: 12px;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  align-self: stretch;
  color: var(--Text-On-White-High-Contrast, #000117);

  /* Paragraph/S/Regular */
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 142.857% */
}

.pagination-table-dashboard {
  width: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
}

::-webkit-scrollbar {
  width: 2px;
  height: 4px;
  transform: rotate(180deg);
}

::-webkit-scrollbar-thumb {
  background-color: var(--Shades-Desaturate-8, #9ba4bc);
  border-radius: 5px;
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

::-webkit-scrollbar-corner {
  background-color: transparent;
}

/* Color of the scrollbar thumb */
/*  */

/* Optional: Border radius of the scrollbar thumb */
/*  */

#chartjs-tooltip {
  max-height: 200px;
  overflow-y: scroll;
  /* pointer-events: none ; */
  /* pointer-events: auto ; */
  /* pointer-events:all ; */
  direction: rtl;
  scrollbar-width: thin;
  scrollbar-color: var(--Shades-Desaturate-8, #9ba4bc) transparent;
  scrollbar-width: thin;
  /* scroll-behavior: "smooth";   */
}

.table-rows-link {
  cursor: pointer;
}
