import React, { useState, useEffect } from "react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Line } from "react-chartjs-2";
import { hasSingleNonEmptyString } from "../../utils/helpers";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

export function LineChart({ labels, datasets, maxSize }) {
  const [maxValue, setMaxValue] = useState(0);
  const [stepSize, setStepSize] = useState(0);
  const [chartKey, setChartKey] = useState(0);
 

   useEffect(() => {
    if (maxSize > 0) {
      setMaxValue(Math.ceil(maxSize));
    } else {
      setMaxValue(1000);
    }
  }, [maxSize, datasets]);
  useEffect(() => {
    const stepSize = Math.ceil(maxValue / 5);
    setStepSize(stepSize);
  }, [maxValue, labels, maxSize]);
  const options = {
    maintainAspectRatio: false,
    redraw:true,
    responsive: true,
    aspectRatio: 1,
    // barValueSpacing: 1.0,
    // barDatasetSpacing: 1.0,

    plugins: {
     
      legend: {
        position: false,
      },
      title: {
        display: false,
      },
      tooltip: {
        enabled: false,
      },
    },
    scales: {
      x: {
        padding: 50,
        offset: false,
        grid: {
          offset: false,
          display: false,
        },
        ticks: {
          font: {
            family: "Ubuntu",
            size: 12,
            weight: "550",
            style: "normal",
            height: "20px",
            letterSpacing: 0.24,
            textAlign: "center",
          },
          color: "#000117",
        },
      },

      y: {
        border: {
          display: false,
        },
        // offset: false,
        // stacked: true,
        beginAtZero: true,
        max: maxValue,
        ticks: {
          beginAtZero: true,
          fontColor: "white",

          stepSize: stepSize,
          callback: (value) => `${value} MB`,
          font: {
            family: "Ubuntu",
            size: 12,
            style: "normal",
            weight: "405",
            letterSpacing: 0.24,
            color: "#000117",
            textAlign: "center",
          },
          color: "#000117",
        },
        grid: {
          drawBorder: false,
          borderSkipped: "left",
        },
      },
    },
    layout: {
      padding: {
        right: 8,
        top: 0,
      },
    },
    interaction: {
      mode: "index",
      intersect: false,
    },
    elements: {
      point: {
        radius: hasSingleNonEmptyString(labels) ? 2 : 0,
        hoverRadius: hasSingleNonEmptyString(labels) ? 2 : 0,
      },
      line: {
        borderWidth: 2,
      },
    },
    color: "#1E88E5",
  };


  useEffect(() => {
    const handleResize = () => {
      // Trigger a re-render by changing the key
      setChartKey((prevKey) => prevKey + 1);
    };

    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);
  const data = {
    labels,
    datasets,
  };
 
  return (
    <div style={{ width: "100%", height: "300px", margin: "auto" }}>
      <Line key={chartKey} options={options} data={data} />
    </div>
  );
}
