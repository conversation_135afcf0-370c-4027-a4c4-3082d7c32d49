.payment-title {
  font-weight: bolder;
  font-size: 32px;
  line-height: 40px;
  letter-spacing: -0.02em;
}

.card .plan-title {
  font-weight: 700;
  font-size: 24px;
  line-height: 32px;
  letter-spacing: -0.02em;
}

.row .subscribe-btn {
  padding: 14px 0;
  border-radius: 8px;
}

.row .choose-other-plan-btn {
  background: #FFFFFF;
  border: 1px solid #DADDE5;
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);
}

.bankForm-bg {
  background: #F6F8FC;
  border-radius: 6px;
  padding: 16px;
  gap: 32px;
}

.card-detail-title {
  font-size: 14px;
  line-height: 20px;
  letter-spacing: -0.154px;
  color: #3C4257;
  margin-bottom: 4px;
}

.row .header-logo-wrapper {
  width: 130px;
  height: 32px;
  padding: 0;
}

.StripeElement {
  padding: 9px;
  color: #32325d;
  background-color: white;
  border: 1px solid #D5DBE1;
  border-radius: 4px;
  width: 100%;
}

.StripeElement--webkit-autofill {
  background-color: #fefde5 !important;
}

.renew-text {
  font-size: 13.8px;
  line-height: 20px;
}

.ElementsApp .InputElement,
.ElementsApp .InputContainer input {
  font-size: 14px !important;
}