import React, { useState } from 'react'
import { Container, Row, Col, Button, Card } from 'react-bootstrap';
import { useLocation, useNavigate } from 'react-router-dom';
import elysiumlogo from '../../assets/images/headerlogo.png';
import { BsCheck } from "react-icons/bs";
import BankDetailForm from './BankDetailForm';
import Footers from '../../pages/footers/Footers';

import "./style.css"

function Payment() {

  const location = useLocation();
  const navigate = useNavigate();

  const priceObj = location.state.pricePlanObj

  const [showBankDetailsForm, setShowBankDetailsForm] = useState(false)

  const handleSubcription = () => {
    setShowBankDetailsForm(true)
  }

  const handleChooseOtherPlan = () => {
    navigate('/priceplan');
  }

  return (
    <>
      <Container fluid>
        <div className='d-flex flex-column justify-content-center align-items-center my-5'>
          <Row className='mt-1'>
            <div className='header-logo-wrapper'>
              <img src={elysiumlogo} alt='elysium' />
            </div>
          </Row>
          <Row>
            <h4 className='payment-title mt-3 pt-3 mb-4 px-0'> Payment Checkout</h4>
          </Row>
          <Row>

            <Card className='px-0'>
              <Card.Body className='my-3 py-3 px-4'>
                <Card.Title className='plan-title mb-3 pb-3'> {priceObj.title}  Plan </Card.Title>
                <Row className='mb-2 justify-content-between align-items-center'>
                  <Col>Billing</Col>
                  <Col className='text-end'> {priceObj.tag}</Col>
                </Row>
                <Row className='mb-2 justify-content-between align-items-center'>
                  <Col>{priceObj.title}</Col>
                  {priceObj.price.includes('Custom') ?
                    <Col className='text-end'> {priceObj.price}</Col> :
                    <Col className='text-end'> {priceObj.price}.00</Col>
                  }
                </Row>
                <hr />
                <Row className='mb-2 justify-content-between align-items-center'>
                  <Col md={4}> <b className='plan-title'>Total </b></Col>
                  <Col className='text-end' md={8}>
                    {priceObj.price.includes('Custom') ? (
                      <b className='plan-title'>{priceObj.price}</b>
                    ) : (
                      <>
                        <b className='plan-title'>{priceObj.price}.00 </b> USD
                      </>
                    )}
                  </Col>
                </Row>
                {!showBankDetailsForm &&
                  <Row className='mt-4  mx-auto'>
                    <Button variant="dark"
                      className='subscribe-btn'
                      type='button'
                      onClick={handleSubcription}
                    > Subscribe
                    </Button>
                  </Row>
                }
                {showBankDetailsForm && (
                  <Row className='p-2' style={{ width: '300px' }}>
                    <BankDetailForm plan_price_end_date={priceObj.plan_price_end_date} priceObjId={priceObj.id} />
                  </Row>
                )}
                <Row className='mt-2  mx-auto'>
                  <Button variant="light"
                    className='choose-other-plan-btn subscribe-btn'
                    type='button'
                    onClick={handleChooseOtherPlan}
                  > Choose another plan
                  </Button>
                </Row>
                {!showBankDetailsForm &&
                  <Card.Text className='mt-4 renew-text'>
                    {/* the date should be come from the choose plan api according to the selected plan */}
                    <BsCheck className='me-2' style={{ color: '#169C00' }} />
                    Your plan will be renew on {priceObj.plan_price_end_date}
                  </Card.Text>
                }
              </Card.Body>
            </Card>
          </Row>
          <Row className='mt-5'>
            <Footers />
          </Row>
        </div>
      </Container>
    </>
  )
}

export default Payment