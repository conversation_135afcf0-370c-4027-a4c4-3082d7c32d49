
import React from 'react';
import { stripeKey } from "../../config/constants";
import { BsCheck } from "react-icons/bs";
import "./style.css";
import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import StripeForm from './StripeForm';
function BankDetailForm({
  plan_price_end_date,
  priceObjId
}) {

  const stripePromise = loadStripe(`${stripeKey}`);
  const appearance = {
    theme: 'stripe',
  };
  const options = {
    appearance,
  };
  return (
    <>
      <div className='mt-2 mb-4 renew-text p-0'>
        <BsCheck style={{ color: '#169C00' }} className='me-1' />
        Your plan will be renewed on {plan_price_end_date}
      </div>
      <Elements stripe={stripePromise} options={options}>
        <StripeForm priceObjId={priceObjId} />
      </Elements>
    </>
  );
}

export default BankDetailForm;

