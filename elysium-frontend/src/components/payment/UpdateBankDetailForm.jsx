import React from "react";
import { stripeKey } from "../../config/constants";
import "./style.css";
import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import UpdateStripeForm from './UpdateStripeForm';
import { Button } from "react-bootstrap";
function UpdateBankDetailForm({
  handleUpdatePaymentModalClose,
  bankDetailResponse }) {

  const stripePromise = loadStripe(`${stripeKey}`);
  const appearance = {
    theme: 'stripe',
  };
  const options = {
    appearance,
  };
  return (
    <Elements stripe={stripePromise} options={options}>
      <UpdateStripeForm bankDetailResponse={bankDetailResponse} handleUpdatePaymentModalClose={handleUpdatePaymentModalClose} />
      <Button className="mt-2 mb-4 cancel-modal btn" onClick={handleUpdatePaymentModalClose}>Cancel</Button>
    </Elements>
  );
}

export default UpdateBankDetailForm;

