import React, { useRef, useState, useEffect } from 'react';
import { useStripe, useElements, CardNumberElement, CardExpiryElement, CardCvcElement } from '@stripe/react-stripe-js';
import { handleCookies } from '../../utils/cookies';
import axios from 'axios';
import { Form, Row, Col, InputGroup, Button } from 'react-bootstrap';
import Loader from '../loader/Loader';
import { baseUrl } from "../../config/constants";


function StripeForm({ bankDetailResponse, handleUpdatePaymentModalClose }) {
  const stripe = useStripe();
  const elements = useElements();
  const cardElementRef = useRef(null);
  const [errorMessage, setErrorMessage] = useState({ cardNumber: "", cardExpiry: "", cardCvc: "" });
  const [cardComplete, setCardComplete] = useState({
    cardNumber: false, cardExpiry: false, cardCvc: false
  })
  const [open, setOpen] = useState(false);
  const [isSubmitDisabled, setIsSubmitDisabled] = useState(false)

  const handleSubmit = async (event) => {
    event.preventDefault();
    if (!stripe || !elements) {
      return;
    }
    setOpen(true);
    const cardElement = cardElementRef.current;

    try {
      const auth_token = 'Bearer ' + handleCookies.fetchCookies();
      const intentResponse = await axios.post(`${baseUrl}/getSetupIntent`, {}, {
        headers: {
          Authorization: auth_token,
        },
      })

      const result = await stripe.confirmCardSetup(`${intentResponse.data.data.intent}`, {
        payment_method: {
          card: cardElement,
        },
      });
      if (result.error) {
        setOpen(false);
        bankDetailResponse({ responseMessage: result.error.message, isError: true });
      } else {
        const payload = {
          token: result.setupIntent.payment_method,
        };
        const rechargeResponse = await axios.post(`${baseUrl}/updatePaymentMethod`, payload, {
          headers: {
            Authorization: auth_token,
          },
        });
        setOpen(false);
        bankDetailResponse({ responseMessage: rechargeResponse.data.message, isError: false })
        handleUpdatePaymentModalClose();
      }
    } catch (error) {
      setOpen(false);
      bankDetailResponse({ responseMessage: error.message, isError: true })
    }
  };
  const handleCardInputChange = (e) => {
    const { error, elementType } = e;
    setErrorMessage((prevErrorMessage) => ({ ...prevErrorMessage, [elementType]: error ? error.message : null }));
    setCardComplete({ ...cardComplete, [e.elementType]: e.complete })
  };
  useEffect(() => {
    let allTrue = Object.values(cardComplete).every(value => value === true);
    setIsSubmitDisabled(!allTrue);
  }, [cardComplete]);

  return (
    <>
      <Loader open={open} />
      <Form onSubmit={handleSubmit} className='bankForm-bg px-3'>
        <div className='card-detail-title'>Card Details</div>
        <Row>
          <Col>
            <Form.Group>
              <InputGroup>
                <CardNumberElement onReady={(element) => cardElementRef.current = element} onChange={handleCardInputChange} />
              </InputGroup>
            </Form.Group>
            {errorMessage.cardNumber && <p className='text-danger renew-text'>{errorMessage.cardNumber}</p>}
          </Col>
        </Row>
        <Row>
          <Col className='pe-0'>
            <Form.Group>
              <InputGroup>
                <CardExpiryElement onChange={handleCardInputChange} />
              </InputGroup>
            </Form.Group>
            {errorMessage.cardExpiry && <p className='text-danger renew-text'>{errorMessage.cardExpiry}</p>}
          </Col>
          <Col className='ps-0'>
            <Form.Group>
              <InputGroup>
                <CardCvcElement onChange={handleCardInputChange} />
              </InputGroup>
            </Form.Group>
            {errorMessage.cardCvc && <p className='text-danger renew-text'>{errorMessage.cardCvc}</p>}
          </Col>
        </Row>
        <Row className='mt-4'>
          <Col>
            <Button variant='dark' type='submit' className='w-100' disabled={isSubmitDisabled}>
              Update payment information
            </Button>
          </Col>
        </Row>
      </Form>
    </>
  );
}

export default StripeForm;
