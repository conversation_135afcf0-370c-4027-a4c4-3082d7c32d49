.schedule-dashboard-main-container {
  display: flex;
  padding: 48px 60px;
  flex-direction: column;
  align-items: flex-start;

  flex: 1 0 0;
  align-self: stretch;
}

.sch-sub-header-container {
  display: flex;
  justify-content: space-between;
  width: 100%;
}
.schedule-headidng {
  align-self: stretch;
  color: var(--Gray-900, #101828);

  /* Heading/Desktop/H4/Bold */
  font-family: Ubuntu;
  font-size: 32px;
  font-style: normal;
  font-weight: 700;
  line-height: 40px; /* 125% */
  letter-spacing: -0.64px;
}

.add-db-btn {
  display: flex;
  height: 40px;
  padding: 0px 12px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  background: var(--dark-light-dark, #000117);
  color: var(--dark-light-light, #fff);

  /* Button/S/Regular */
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px; /* 114.286% */
  letter-spacing: 0.28px;
  outline: none;
  border: none;
}
/* .add-db-btn:hover {
    background-color: white;
    color: black;
    border: 1px solid rgb(216, 212, 206);
  } */
.img-container-add-db {
  height: 20px;
  width: 20px;
  fill: white; /* Set the default fill color */
}

.img-container-add-db:hover {
  fill: black; /* Set the default fill color */
}

.schedule-server-name {
  align-self: stretch;
  color: var(--Gray-900, #101828);

  /* Heading/Desktop/H4/Bold */
  font-family: Ubuntu;
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: 40px; /* 125% */
  letter-spacing: -0.64px;
  margin-bottom: 10PX;
  margin-top: 32px;
}

.schedule-table-main-container {
  width: 100%;
  border: 1px solid var(--Shades-Desaturate-9, #c6ccdc);
  border-radius: 12px;
  overflow: hidden;
}

.schedule-table-header {
  display: flex;
  height: 54px;
  padding: 13px 16px;
  justify-content: space-between;
  align-items: center;
  border-radius: 12px;
}

.remove-db-from-schedule-dashboard {
  display: flex;
  height: 32px;
  padding: 0px 12px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 6px;
  border: 1px solid var(--Shades-Desaturate-10, #dadde5);
  background: var(--dark-light-light, #fff);

  /* Shadow/XS */
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
  color: var(--States-Negative, #ef0c0c);

  /* Button/S/Regular */
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px; /* 114.286% */
  letter-spacing: 0.28px;
}

.scdle-database-name {
  color: var(--Gray-900, #101828);

  /* Paragraph/L/Bold */
  font-family: Ubuntu;
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
  line-height: 28px; /* 155.556% */
}

.add-more-db-table-sec-container {
  display: flex;
  padding: 12px 16px;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  border-top: 1px solid var(--Shades-Desaturate-11, #f6f8fc);
  background: var(--Shades-Desaturate-11, #f6f8fc);
}

.add-more-table-btn {
  display: flex;
  height: 32px;
  padding: 0px 12px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 6px;
  border: 1px solid var(--Shades-Desaturate-10, #dadde5);
  background: var(--dark-light-light, #fff);

  /* Shadow/XS */
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
  color: var(--Text-On-White-High-Contrast, #000117);

  /* Button/S/Regular */
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px; /* 114.286% */
  letter-spacing: 0.28px;
}

.batch-size-time-setup-container {
  display: flex;
  min-width: 252px;
  overflow: hidden;
  height: 32px;
  padding: 10px 8px;
  align-items: center;
  border-radius: 6px;
  border: 1px solid var(--Shades-Desaturate-9, #c6ccdc);
  background: var(--dark-light-light, #fff);
  display: flex;
  padding: 10px 8px;
  align-items: center;
  gap: 8px;
  flex: 1 0 0;
  align-self: stretch;
  color: var(--Text-On-White-High-Contrast, #000117);

  /* Caption/Regular */
  font-family: Ubuntu;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 166.667% */
  letter-spacing: 0.24px;
}

.batch-size-time-setup-container.started {
  border-radius: 6px;
  border: 1px solid var(--Shades-Desaturate-10, #dadde5);
  background: var(--Shades-Desaturate-10, #dadde5);
}

.schedule-time-setup-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.setup-btn-schudule {
  display: flex;
  height: 32px;
  width: 34px;
  padding: 0px 8px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 6px;
  border: 1px solid var(--Shades-Desaturate-9, #c6ccdc);
  background: var(--dark-light-light, #fff);

  /* Shadow/XS */
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
}

.setup-btn-schudule.selected {
  background: var(--dark-light-dark, #000117);
}

.setup-btn-schudule.started {
  background: var(--Gradient, linear-gradient(90deg, #6206b8 0%, #00b6d6 100%));
}

.border-table-server-table {
  border: 1px solid #c6ccdc;
  margin-right: 12px;
  height: 20px;
}
.schedule-setup-between-pipe {
  background-color: #c6ccdc;
  width: 1px;
  height: 22px;
}

.schedule-dashboard-table-with-empty-data {
  width: 100%;
}

.schedule-table-no-data {
  display: flex;
  justify-content: center;
  height: 70vh;
  width: 100%;
  align-items: center;
}

.schedule-table-no-data-text {
  color: var(--Shades-Desaturate-8, #9ba4bc);
  text-align: center;
  width: 257px;
  /* Paragraph/L/Regular */
  font-family: Ubuntu;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 28px; /* 155.556% */
}

.tb-img-name-schedule {
  padding: 5px;
}

.custom-table-container-wrapper {
  overflow-x: auto;
}

.multi-table-container {
  display: flex;
  flex-direction: column;
  gap: 32px;
}
