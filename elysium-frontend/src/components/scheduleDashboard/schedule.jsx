import React, { useEffect, useState } from "react";
import Header from "../../pages/header/Header";
import "./style.css";
import add from "./../../assets/images/add.svg";
import AddDatabaseModal from "../addDatabaseModal/AddDatabaseModal";
import ScheduleTable from "./ScheduleTable";
import Notification from "../notificationModal/Notification";
import { serviceCall } from "../../services/scheduleServices";
import Loader from "../loader/Loader";

const ScheduleDashboard = () => {
  const [modalShow, setModalShow] = useState(false);
  const [open, setOpen] = useState(false);
  const [responseMessage, setResponseMessage] = useState();
  const [showNotification, setShowNotification] = useState(false);
  const [isError, setIsError] = useState(false);
  const [serverData, setServerData] = useState([]);

  const handleToastClose = () => {
    setShowNotification(false);
    setResponseMessage("");
    setIsError(false);
  };

  const getScheduleServerDbTableListing = async (payload) => {
    setOpen(true);
    try {
      const response = await serviceCall.getScheduleServerDbTableListing(
        payload
      );
      if (response?.status === "Success") {
        setServerData(response?.data?.schedule_listing);
      } else {
        setResponseMessage(
          response?.messages
            ? response?.messages
            : response?.response.data?.message || "Something went wrong"
        );
        setIsError(true);
        setShowNotification(true);
      }
    } catch (error) {
    
    } finally {
      setOpen(false);
    }
  };

  const handleReload = () => {
    getScheduleServerDbTableListing();
  };

  useEffect(() => {
    getScheduleServerDbTableListing();
  }, []);

  return (
    <>
      <Loader open={open} />
      <Header />
      <div className="schedule-dashboard-main-container">
        <div className="sch-sub-header-container">
          <div className="schedule-headidng">Schedule</div>
          <button className="add-db-btn" onClick={() => setModalShow(true)}>
            <img className="img-container-add-db" alt="no icon" src={add}></img>{" "}
            Add Database
          </button>
        </div>
        <div className="schedule-dashboard-table-with-empty-data">
          <ScheduleTable data={serverData} handleReload={handleReload} />
        </div>
      </div>
      {modalShow && (
        <AddDatabaseModal
          show={modalShow}
          onHide={() => setModalShow(false)}
          handleReload={handleReload}
        />
      )}

      {showNotification && (
        <Notification
          responseMessage={responseMessage}
          isError={isError}
          handleToastClose={handleToastClose}
        />
      )}
    </>
  );
};

export default ScheduleDashboard;
