import "./style.css";
import React, { useState } from "react";
import DeleteIcon from "./../../assets/images/delete.svg";
import AddIcon from "./../../assets/images/add (1).svg";
import CalendarImage from "./../../assets/images/calendar--heat-map.svg";
import Play from "./../../assets/images/play--filled--alt.svg";
import PlayFinished from "./../../assets/images/play--filled--alt (1).svg";
import StopFiled from "./../../assets/images/stop--filled--alt.svg";
import Trash from "./../../assets/images/trash-can.svg";
import ScheduleModal from "../tableScheduleModal/TableScheduleModal";
import AddDatabaseModal from "../addDatabaseModal/AddDatabaseModal";
import { serviceCall } from "../../services/scheduleServices";
import Notification from "../notificationModal/Notification";
import Loader from "../loader/Loader";
import ToastMessage from "../common/ToastMessage";
import ConfirmationModal from "../common/ConfirmationModal";

const ScheduleTable = ({ data, handleReload }) => {
  const [modalShow, setModalShow] = useState(false);
  const [selectedDatabaseModal, setselectedDatabaseModal] = useState(false);
  const [tableModalShow, setTableModalShow] = useState(false);
  const [startModalShow, setStartModalShow] = useState(false);
  const [pauseModalShow, setPauseModalShow] = useState(false);
  const [updateStatus, setUpdateStatus] = useState();
  const [scheduleModalShow, setScheduleModalShow] = useState(false);
  const [scheduleId, setScheduleId] = useState();
  const [databaseId, setDatabaseId] = useState();
  const [serverId, setServerId] = useState();
  const [isUndo, setIsUndo] = useState(false);
  const [isDbUndo, setIsDbUndo] = useState(false);
  const [dbScheduleTableIds, setDbScheduleTableIds] = useState([]);
  const [dbScheduleId, setDbScheduleId] = useState([]);
  const [undoScheduleId, setUndoScheduleId] = useState(null);
  const [open, setOpen] = useState(false);
  const [isError, setIsError] = useState();
  const [responseMessage, setResponseMessage] = useState();
  const [showNotification, setShowNotification] = useState(false);
  const [scheduleData, setScheduleData] = useState({
    tableName: "",
    scheduleId: "",
    retention: "",
    s3BucketName: "",
    batchsize: "",
    isTimeZone: false,
  });

  const deleteScheduleTable = async (payload) => {
    setOpen(true);
    setIsUndo(false);
    try {
      const response = await serviceCall.deleteScheduleTable(payload);
      if (response?.status === "Success") {
        handleReload();
        setUndoScheduleId(payload);
        setIsUndo(true);
      } else {
        setResponseMessage(
          response?.messages
            ? response?.messages
            : response?.response.data?.message || "Something went wrong"
        );
        setIsError(true);
        setShowNotification(true);
      }
    } catch (error) {
   
    } finally {
      setOpen(false);
    }
  };

  const deleteScheduleDatabase = async (payload) => {
    setOpen(true);
    setIsDbUndo(false);
    try {
      const response = await serviceCall.deleteScheduleDatabase(payload);
      if (response?.status === "Success") {
        handleReload();
        setIsDbUndo(true);
      } else {
        setResponseMessage(
          response?.messages
            ? response?.messages
            : response?.response.data?.message || "Something went wrong"
        );
        setIsError(true);
        setShowNotification(true);
      }
    } catch (error) {
    
    } finally {
      setOpen(false);
    }
  };

  const updateScheduleTaleStatus = async (payload) => {
    setOpen(true);
    try {
      const response = await serviceCall.changeScheduleArchivingStatus(payload);
      if (response?.status === "Success") {
        handleReload();
        setResponseMessage(
          payload?.archiving_status === 0
            ? "Archiving process stoped successfully"
            : "Data archiving has started"
        );
        setIsError(false);
        setShowNotification(true);
      } else {
        setResponseMessage(
          response?.messages
            ? response?.messages
            : response?.response.data?.message || "Something went wrong"
        );
        setIsError(true);
        setShowNotification(true);
      }
    } catch (error) {
     
    } finally {
      setOpen(false);
      setUpdateStatus();
    }
  };

  const undoTable = async (payload) => {
    setOpen(true);
    try {
      const response = await serviceCall.undoScheduleTable(payload);
      if (response?.status === "Success") {
        handleReload();
        setIsUndo(false);
      } else {
        setResponseMessage(
          response?.messages
            ? response?.messages
            : response?.response.data?.message || "Something went wrong"
        );
        setIsError(true);
        setShowNotification(true);
      }
    } catch (error) {
    
    } finally {
      setOpen(false);
    }
  };

  const undoDb = async (payload) => {
    setOpen(true);
    try {
      const response = await serviceCall.undoScheduleDatabase(payload);
      if (response?.status === "Success") {
        handleReload();
        setIsDbUndo(false);
      } else {
        setResponseMessage(
          response?.messages
            ? response?.messages
            : response?.response.data?.message || "Something went wrong"
        );
        setIsError(true);
        setShowNotification(true);
      }
    } catch (error) {
      
    } finally {
      setOpen(false);
    }
  };

  const handleDbRemove = (ok) => {
    deleteScheduleDatabase({ database_id: databaseId });
    setModalShow(!ok);
  };

  const handleTableRemove = (ok) => {
    setTableModalShow(false);
    deleteScheduleTable({ schedule_id: scheduleId });
  };

  const handleToastClose = () => {
    setShowNotification(false);
    setResponseMessage("");
    setIsError(false);
  };

  const handlePlay = (id, status, isSetup) => {
    const payload = {
      schedule_id: id,
      archiving_status: status === 0 ? 1 : 0,
    };
    setUpdateStatus(payload);
    if (isSetup && status === 0) {
      setStartModalShow(true);
    } else if (isSetup && status === 1) {
      setPauseModalShow(true);
    } else {
      setResponseMessage("Please set up a schedule time before archiving.");
      setIsError(true);
      setShowNotification(true);
    }
  };

  const handleTableUndo = () => {
    setIsUndo(false);
    undoTable(undoScheduleId);
  };

  const handleDbUndo = () => {
    setIsUndo(false);
    undoDb({ schedule_ids: dbScheduleTableIds, db_schedule_id: dbScheduleId });
  };

  const handlePlayUpdate = (ok) => {
    setStartModalShow(false);
    setPauseModalShow(false);
    updateScheduleTaleStatus(updateStatus);
  };
  const clearState = () => {
    setIsUndo(false);
    setIsDbUndo(false);
    setDbScheduleTableIds([]);
    setDbScheduleId();
    setUndoScheduleId(null);
  };
  const handleToast = () => {
    setResponseMessage("Time was successfully scheduled ");
    setIsError(false);
    setShowNotification(true);
  };
  const handleSetIdsDbRemove = async (db) => {
    const isActive = db?.schedule_data?.some((table) => table?.is_active === 1);
    if (isActive) {
      setResponseMessage(
        "There are active schedules. Please deactivate them before deleting the database."
      );
      setIsError(true);
      setShowNotification(true);
    } else {
      const scheduleIds = await db?.schedule_data?.map(
        (table) => table?.schedule_id
      );
      setDatabaseId(db.database_id);
      setDbScheduleTableIds(scheduleIds);
      setDbScheduleId(db?.db_schedule_id);
      setModalShow(true);
    }
  };

  const handleScheduleModalShow = (table) => {
    if (table?.is_active === 0) {
      setScheduleId(table?.schedule_id);
      setTableModalShow(true);
    } else {
      setResponseMessage(
        "Schedule is active. Please deactivate it before deleting."
      );
      setIsError(true);
      setShowNotification(true);
    }
  };

  return (
    <>
      <Loader open={open} />
      {data?.length > 0 ? (
        data?.map((server, index) => {
          const serverId = server?.server_id;
          return (
            <div key={index}>
              <div className="schedule-server-name">{server?.server_name}</div>
              <div className="multi-table-container">
                {server?.database_data?.map((db, i) => {
                  return (
                    <div key={i}>
                      {" "}
                      <div className="schedule-table-main-container">
                        <div className="schedule-table-header">
                          <span className="scdle-database-name">
                            {db.database_name}
                          </span>{" "}
                          <button
                            className="remove-db-from-schedule-dashboard"
                            onClick={() => {
                              handleSetIdsDbRemove(db);
                            }}
                          >
                            <img src={DeleteIcon} alt="no icon" /> Remove
                          </button>
                        </div>
                        <div className="select-server-db-container">
                          <div className="add-more-db-table-sec-container">
                            <button
                              className="add-more-table-btn"
                              onClick={() => {
                                setServerId(serverId);
                                setDatabaseId(db?.database_id);
                                setselectedDatabaseModal(true);
                              }}
                            >
                              {" "}
                              <img
                                style={{ width: "16px", height: "16px" }}
                                src={AddIcon}
                                alt="no icon"
                              />{" "}
                              Add Table{" "}
                            </button>
                          </div>
                        </div>
                        <div className="custom-table-container-wrapper">
                          <table className="table-container">
                            <tbody>
                              <tr className="table-header-borders">
                                <th
                                  className="min-width-table-th"
                                  style={{
                                    padding: "5px",
                                    paddingLeft: "10px",
                                  }}
                                >
                                  <div className="header-container ">
                                    <span>Table name</span>{" "}
                                    <span className="border-table-server-table"></span>
                                  </div>
                                </th>

                                <th className="min-width-table-th">
                                  <div className="header-container ">
                                    <span>Retention</span>{" "}
                                    <span className="border-table-server-table"></span>
                                  </div>
                                </th>
                                <th className="min-width-table-th">
                                  <div className="header-container">
                                    <span>S3/File</span>{" "}
                                    <span className="border-table-server-table"></span>
                                  </div>
                                </th>
                                <th className="min-width-table-th">
                                  <div className="header-container">
                                    <span className="">RI</span>{" "}
                                    <span className="border-table-server-table"></span>
                                  </div>
                                </th>
                                <th className="min-width-table-th">
                                  <div className="header-container">
                                    <span className="">Partition</span>{" "}
                                    <span className="border-table-server-table"></span>
                                  </div>
                                </th>
                                <th className="min-width-table-th">
                                  <div className="header-container">
                                    <span className="">Total Rows</span>{" "}
                                    <span className="border-table-server-table"></span>
                                  </div>
                                </th>
                                <th className="min-width-table-th min-width-table-th-schedule-setup">
                                  <div className="header-container">
                                    <span className="">Batch size</span>{" "}
                                  </div>
                                </th>
                              </tr>

                              {db.schedule_data?.length > 0 ? (
                                db.schedule_data?.map((table, index) => {
                                  const isTimeZone =
                                    table?.start_time &&
                                    table?.end_time &&
                                    table?.time_zone;
                                  return (
                                    <tr className="table-rows" key={index}>
                                      <td>
                                        <div className="tb-img-name-schedule font-modal-table-td">
                                          {table?.table_name}
                                        </div>
                                      </td>
                                      <td className="table-rows-color font-modal-table-td">
                                        <span>{table?.retention}</span>
                                      </td>
                                      <td>
                                        <div className="progress-percentage font-modal-table-td">
                                          <span>{table?.s3_file}</span>
                                        </div>
                                      </td>
                                      <td>
                                        <div className="progress-percentage font-modal-table-td">
                                          <span>
                                            {table?.retention_index !== 0
                                              ? table?.retention_index
                                              : "-"}
                                          </span>
                                        </div>
                                      </td>
                                      <td className="table-rows-color font-modal-table-td">
                                        <span>
                                          {table?.is_partitioned !== 0
                                            ? table?.is_partitioned
                                            : "-"}
                                        </span>
                                      </td>
                                      <td>
                                        <div className="">
                                          {table?.total_rows}
                                        </div>
                                      </td>
                                      <td className="table-rows-color font-modal-table-td">
                                        <div className="schedule-time-setup-container">
                                          <span style={{ minWidth: "133px" }}>
                                            {table?.batch_size?.toLocaleString()}
                                          </span>
                                          <button
                                            disabled={
                                              isTimeZone && table?.is_active
                                            }
                                            onClick={() => {
                                              setScheduleData({
                                                tableName: table?.table_name,
                                                scheduleId: table?.schedule_id,
                                                retention: table?.retention,
                                                s3BucketName: table?.s3_file,
                                                batchsize: table?.batch_size,
                                                isTimeZone: isTimeZone,
                                              });
                                              setScheduleModalShow(true);
                                            }}
                                            className={`batch-size-time-setup-container ${
                                              table?.is_active === 1 &&
                                              isTimeZone
                                                ? "started"
                                                : ""
                                            }`}
                                          >
                                            <img
                                              style={{
                                                height: "20px",
                                                width: "20px",
                                              }}
                                              src={CalendarImage}
                                              alt=""
                                            />{" "}
                                            <span
                                              style={{ whiteSpace: "nowrap" }}
                                            >
                                              {isTimeZone
                                                ? `${table?.time_zone},  ${
                                                    table?.start_time
                                                  } - ${
                                                    table?.end_time
                                                  }, ${table?.day_of_week
                                                    ?.map(
                                                      (day, index) =>
                                                        `${day}${
                                                          index ===
                                                          table.day_of_week
                                                            .length -
                                                            1
                                                            ? ""
                                                            : "/"
                                                        }`
                                                    )
                                                    .join("")}`
                                                : "Setup Schedule"}
                                            </span>
                                          </button>

                                          <button
                                            onClick={() =>
                                              handlePlay(
                                                table?.schedule_id,
                                                table?.is_active,
                                                isTimeZone
                                              )
                                            }
                                            className={`setup-btn-schudule ${
                                              table?.is_active === 1 &&
                                              isTimeZone
                                                ? "started"
                                                : isTimeZone
                                                ? "selected"
                                                : ""
                                            }`}
                                          >
                                            <img
                                              src={
                                                table?.is_active === 1 &&
                                                isTimeZone
                                                  ? StopFiled
                                                  : isTimeZone
                                                  ? PlayFinished
                                                  : Play
                                              }
                                              alt="no icon"
                                            />
                                          </button>
                                          <span className="schedule-setup-between-pipe"></span>

                                          <button
                                            className="setup-btn-schudule"
                                            onClick={() => {
                                              handleScheduleModalShow(table);
                                            }}
                                          >
                                            <img src={Trash} alt="no icon" />
                                          </button>
                                        </div>
                                      </td>
                                    </tr>
                                  );
                                })
                              ) : (
                                <tr
                                  style={{
                                    height: "64px",
                                    backgroundColor: "#FFF",
                                  }}
                                >
                                  <td colSpan="8">
                                    <div className="no-atabase-selected-container">
                                      <div className="no-atabase-selected-container-text">
                                        <div>No table for schedule</div>{" "}
                                      </div>
                                    </div>
                                  </td>
                                </tr>
                              )}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          );
        })
      ) : (
        <div className="schedule-table-no-data">
          {" "}
          <span className="schedule-table-no-data-text">
            No Database <div>Add Database to start Schedule</div>
          </span>
        </div>
      )}

      <ConfirmationModal
        show={modalShow}
        onHide={() => setModalShow(false)}
        onAction={handleDbRemove}
        isWarningImage={true}
        heading={"Remove Database"}
        warningText={"Are you sure you want remove database ?"}
        text={"Remove"}
      />
      <ConfirmationModal
        show={tableModalShow}
        onHide={() => setTableModalShow(false)}
        onAction={handleTableRemove}
        isWarningImage={false}
        heading={"Remove Table"}
        text={"Remove"}
        warningText={"Are you sure you want remove table ?"}
      />
      <ConfirmationModal
        show={startModalShow}
        onHide={() => {
          setStartModalShow(false);
          setUpdateStatus();
        }}
        onAction={handlePlayUpdate}
        isWarningImage={false}
        text={"Start"}
        bgColor={"#000117"}
        heading={"Start Schedule"}
        warningText={"Are you sure you want to start schedule ?"}
      />
      <ConfirmationModal
        show={pauseModalShow}
        onHide={() => {
          setPauseModalShow(false);
          setUpdateStatus();
        }}
        onAction={handlePlayUpdate}
        text={"Stop"}
        bgColor={"#000117"}
        isWarningImage={false}
        heading={"Stop Schedule"}
        warningText={"Are you sure you want to stop the schedule ?"}
      />
      {scheduleModalShow && (
        <ScheduleModal
          show={scheduleModalShow}
          onHide={() => setScheduleModalShow(false)}
          showToast={() => handleToast()}
          scheduleData={scheduleData}
          handleReload={handleReload}
        />
      )}
      {selectedDatabaseModal && (
        <AddDatabaseModal
          show={selectedDatabaseModal}
          onHide={() => setselectedDatabaseModal(false)}
          slectedDatabaseTable={{ serverId: serverId, DataBaseId: databaseId }}
          handleReload={handleReload}
        />
      )}
      {showNotification && (
        <Notification
          responseMessage={responseMessage}
          isError={isError}
          handleToastClose={handleToastClose}
        />
      )}
      {isUndo && (
        <ToastMessage
          isVisibleToast={isUndo}
          onUndo={handleTableUndo}
          undoState={() => clearState()}
          message={"Table has been removed"}
        />
      )}
      {isDbUndo && (
        <ToastMessage
          isVisibleToast={isDbUndo}
          onUndo={handleDbUndo}
          undoState={() => clearState()}
          message={"Database has been removed"}
        />
      )}
    </>
  );
};

export default ScheduleTable;
