import React, { useEffect, useState } from "react";
import <PERSON><PERSON>ef<PERSON> from "./../../assets/images/arrow--left.svg";
import ArrowRight from "./../../assets/images/arrow--right.svg";
import DownArrow from "./../../assets/images/chevron--sort--down.svg";
import UpArrow from "./../../assets/images/chevron--sort.svg";
import ArrowRightVisible from "./../../assets/images/arrow--right-black.svg";
import ArrowleftVisible from "./../../assets/images/arrow-left-black.svg";
import ProgressBar from "../common/Progress";
import { useNavigate } from "react-router";
import { colors } from "../../constants/colors";

const itemsPerPage = 3;
const ArchivingTable = ({
  name,
  totalRows,
  totalData,
  heading,
  data,
  tableName,
  databaseName,
  serverId,
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [sortedData, setSortedData] = useState(data);
  const [sortOrder, setSortOrder] = useState({
    table_name: false,
    total_table_rows_archived: false,
    total_table_size_archived: false,
  });
  const navigate = useNavigate();
  const lastPageIndex = Math.ceil(sortedData?.length / itemsPerPage);

  const sortData = (column) => {
    const newData = [...sortedData].sort((a, b) => {
      switch (column) {
        case "table_name":
          return sortOrder.table_name
            ? a[column].localeCompare(b[column])
            : b[column].localeCompare(a[column]);
        case "total_table_rows_archived":
          return sortOrder.total_table_rows_archived
            ? parseInt(b.total_table_rows_archived) -
                parseInt(a.total_table_rows_archived)
            : parseInt(a.total_table_rows_archived) -
                parseInt(b.total_table_rows_archived);
        case "total_table_size_archived":
          return sortOrder.total_table_size_archived
            ? parseFloat(b.total_table_size_archived) -
                parseFloat(a.total_table_size_archived)
            : parseFloat(a.total_table_size_archived) -
                parseFloat(b.total_table_size_archived);
        default:
          return 0;
      }
    });

    setSortedData(newData);
  };

  const renderData = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return sortedData?.slice(startIndex, endIndex);
  };

  const handlePrevious = () => {
    setCurrentPage((prevPage) => Math.max(1, prevPage - 1));
  };

  const handleNext = () => {
    setCurrentPage((prevPage) => Math.min(lastPageIndex, prevPage + 1));
  };

  const handleSort = (column) => {
    if (!Array.isArray(sortedData)) {
      return;
    }
    sortData(column);
    setSortOrder((prevSortOrder) => ({
      ...Object.keys(prevSortOrder).reduce((acc, key) => {
        acc[key] = key === column ? !prevSortOrder[key] : false;
        return acc;
      }, {}),
    }));
  };

  useEffect(() => {
    setSortedData(data);
  }, [data]);

  return (
    <>
      <div className="server-container-chart server-table-bg">
        <div className="archiving-table-header">{heading}</div>
        <div style={{ overflowX: "auto" }}>
          <table className="table-container" style={{ overflowX: "scroll" }}>
            <tbody>
              <tr className="header-container-border">
                <th style={{ paddingLeft: "24px", width: "50%" }}>
                  <div className="header-container server-name-table">
                    <span>
                      {name}
                      <img
                        className="sorting-table-icons"
                        onClick={() => handleSort("table_name")}
                        src={sortOrder?.table_name ? UpArrow : DownArrow}
                        alt="sort"
                      />
                    </span>
                    <span className="border-table-server"></span>
                  </div>
                </th>
                <th style={{ minWidth: "180px", width: "234px" }} className="">
                  <div className="header-container server-table-heading">
                    <span className="server-table-heading">
                      <span>{totalRows}</span>
                      <img
                        className="sorting-table-icons"
                        onClick={() => handleSort("total_table_rows_archived")}
                        src={
                          sortOrder?.total_table_rows_archived
                            ? UpArrow
                            : DownArrow
                        }
                        alt="sort"
                      />
                    </span>
                    <span className="border-table"></span>
                  </div>
                </th>
                <th style={{ minWidth: "412px" }}>
                  <div className="header-container server-table-heading">
                    <span className="server-table-heading">
                      <div>{totalData}</div>
                      <img
                        className="sorting-table-icons"
                        onClick={() => handleSort("total_table_size_archived")}
                        src={
                          sortOrder?.total_table_size_archived
                            ? UpArrow
                            : DownArrow
                        }
                        alt="sort"
                      />{" "}
                    </span>
                  </div>
                </th>
              </tr>
              {renderData()?.length === 0 ? (
                <tr style={{ height: "184px" }}>
                  <td colSpan="8">
                    <div className="no-atabase-selected-container">
                      <div className="no-atabase-selected-container-text">
                        <div>
                          No database table archiving activities available
                        </div>{" "}
                      </div>
                    </div>
                  </td>
                </tr>
              ) : (
                renderData()?.map((table, index) => {
                  return (
                    <tr key={index} className="table-rows">
                      <td style={{ paddingLeft: "24px" }}>
                        {" "}
                        <div
                          onClick={() =>
                            navigate("/tables", {
                              state: {
                                tableId: table?.table_id,
                                databaseId: table?.database_id,
                                serverId: table?.server_id
                                  ? table?.server_id
                                  : serverId,
                                name: tableName,
                                tableName: table?.table_name,
                                databaseName: databaseName,
                              },
                            })
                          }
                          className="table-rows-link"
                          style={{
                            display: "flex",
                            alignItems: "center",
                            gap: "8px",
                          }}
                        >
                          {table?.table_name}
                        </div>
                      </td>
                      <td className="table-rows-color">
                        <span>{table?.total_table_rows_archived}</span>
                      </td>
                      <td>
                        {" "}
                        <div
                          style={{
                            display: "flex",
                            gap: "8px",
                            alignItems: "center",
                          }}
                        >
                          <div className="table-progress-container">
                            <span
                              style={{ minWidth: "68px", textAlign: "right" }}
                            >
                              {table?.total_table_size_archived} MB
                            </span>
                            <ProgressBar
                              key={index}
                              bgcolor={colors[index]}
                              completed={table?.total_table_archive_percentage}
                            />
                          </div>
                          <span>
                            {table?.total_table_archive_percentage?.toFixed(2)}%
                          </span>
                        </div>
                      </td>
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>
        <div className="archiving-table-footer">
          <button
            className={`archiving-table-footer-button ${
              currentPage > 1 ? "active" : ""
            }`}
            onClick={handlePrevious}
            disabled={currentPage === 1}
          >
            {" "}
            <img
              className="btns-arrow"
              alt=">"
              src={currentPage > 1 ? ArrowleftVisible : ArrowLeft}
            />
            <span>Previous</span>{" "}
          </button>
          <span className="table-count">{currentPage}</span>
          <button
            className={`archiving-table-footer-button ${
              data?.length > 3
                ? currentPage !== lastPageIndex
                  ? "active"
                  : ""
                : ""
            }`}
            onClick={handleNext}
            disabled={currentPage === lastPageIndex}
          >
            {" "}
            <span>Next</span>{" "}
            <img
              className="btns-arrow"
              alt=">"
              src={
                data?.length > 3
                  ? currentPage !== lastPageIndex
                    ? ArrowRightVisible
                    : ArrowRight
                  : ArrowRight
              }
            />{" "}
          </button>
        </div>
      </div>
    </>
  );
};

export default ArchivingTable;
