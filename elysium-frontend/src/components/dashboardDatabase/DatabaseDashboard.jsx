import React, { useEffect, useState } from "react";
import Header from "../../pages/header/Header";
import { Container, Row, Col } from "react-bootstrap";
import DateRangePicker from "../common/Calendar";
import DatabaseBarCard from "./DatabaseBarCard";
import { Link, useLocation, useNavigate } from "react-router-dom";
import CrumbArrow from "./../../assets/images/chevron--right.svg";
import {
  convertToISODate,
  convertToMonthName,
  formatDateIntoDays,
} from "../../utils/helpers";
import { serviceCall } from "../../services/dashboard";
import { calculateDaysFlag} from "../../constants/calender";
import Loader from "../loader/Loader";
import ArchivingTable from "./ArchivedListing";
import Notification from "../notificationModal/Notification";

function DatabaseDashboard() {
  // Get the current location and set initial state variables
  const location = useLocation();
  const serverId = location?.state?.serverId;
  const databaseId = location?.state?.id;
  const databaseName = location?.state?.name;
  const serverName = location?.state?.serverName;
  const [open, setOpen] = useState(false);
  const [filter, setFilter] = useState();
  const navigate = useNavigate();
  const [databaseArchivedCardData, setDatabaseArchivedCardData] = useState();
  const [responseMessage, setResponseMessage] = useState();
  const [showNotification, setShowNotification] = useState(false);
  const [isError, setIsError] = useState(false);
  const [databaseArchivedBarPanelData, setDatabaseArchivedBarPanelData] =
    useState({
      data: "",
      label: "",
    });

  const [
    databaseArchivedListingPanelData,
    setDatabaseArchivedListingPanelData,
  ] = useState();

  // Function to fetch archived data for a given date range
  const getArchivedData = async (range) => {
    setOpen(true);
    try {
      const response = await serviceCall.getDatabaseArchivedData(range);
      if (response?.status === "Success") {
        setDatabaseArchivedCardData(response?.data?.database_archived_data[0]);
      } else {
        setDatabaseArchivedCardData();
        setResponseMessage(
          response?.messages
            ? response?.messages
            : response?.response.data?.message || "Something went wrong"
        );
        setIsError(true);
        setShowNotification(true);
      }
    } catch (error) {
    
    } finally {
      setOpen(false);
    }
  };

  // Function to fetch server bar data for a given date range
  const getDatabaseBarData = async (range) => {
    setOpen(true);
    try {
      const response = await serviceCall.getDatabaseBarData(range);
      if (response?.status === "Success") {
        const dateParts = response?.data?.database_barChart[0]?.dates[0]?.split("-");
        let label;
        if (dateParts?.length === 2) {
          label = await convertToMonthName(response?.data?.database_barChart[0]?.dates);
        } else if (dateParts?.length === 3) {
          label = await formatDateIntoDays(response?.data?.database_barChart[0]?.dates);
        } else if (dateParts?.length === 1) {
          label = response?.data?.database_barChart[0]?.dates;
        }
        setDatabaseArchivedBarPanelData({
          data: response.data?.database_barChart[1],
          label: label,
        });
      } else {
        setDatabaseArchivedBarPanelData({
          data: "",
          label: "",
        });
        setResponseMessage(
          response?.response?.data?.message || "Something went wrong"
        );
        setIsError(true);
        setShowNotification(true);
      }
    } catch (error) {
      
    } finally {
      setOpen(false);
    }
  };
  

  // Function to fetch table data for a given date range
  const getTableData = async (range) => {
    setOpen(true);
    try {
      const response = await serviceCall.getDatabaseListingData(range);
      if (response?.status === "Success") {
        setDatabaseArchivedListingPanelData(response?.data?.database_tables_list);
      } else {
        setDatabaseArchivedListingPanelData();
        setResponseMessage(
          response?.messages
            ? response?.messages
            : response?.response.data?.message || "Something went wrong"
        );
        setIsError(true);
        setShowNotification(true);
      }
    } catch (error) {
     
    } finally {
      setOpen(false);
    }
  };

 /* eslint-disable react-hooks/exhaustive-deps */

  useEffect(() => {}, [serverId]);
  const handleDateRangeChange = async (date) => {
    const range = await convertToISODate(date);
    const daysInRange = calculateDaysFlag(
      range?.start_date,
      range?.end_date
    );

    if (range?.start_date && range?.end_date) {
      const payload = {
        start_date: range?.start_date,
        end_date: range?.end_date,
        flag: daysInRange,
        id: serverId,
        database_id: databaseId,
      };
      getArchivedData(payload);
      getDatabaseBarData(payload);
      getTableData(payload);
    }
  };

  const handleFilterLabel = (label) => {
    setFilter(label);
  };

  const handleToastClose = () => {
    setShowNotification(false);
    setResponseMessage("");
    setIsError(false);
  };

  // Effect to redirect to the dashboard if required data is missing
  useEffect(() => {
    if (
      !location.state ||
      !serverId ||
      !databaseId ||
      !databaseName ||
      !serverName
    ) {
      navigate("/");
    }
  }, [location]);

  return (
    <>
      <Header />
      <Loader open={open} />
      <Container fluid className="server-dashboard-container">
        <Row>
          <Col xs={12}>
            <div className="server-breadcrumb">
              <Link to="/dashboard" className="breadcrumb-link">
                Dashboard
              </Link>
              <img
                style={{ height: "12px", width: "12px" }}
                alt=">"
                src={CrumbArrow}
              />
              <span
                onClick={() =>
                  navigate("/server", {
                    state: {
                      server_id: serverId,
                      server_name: serverName,
                    },
                  })
                }
                className="breadcrumb-link"
              >
                {serverName}
              </span>
              <img
                style={{ height: "12px", width: "12px" }}
                alt=">"
                src={CrumbArrow}
              />
              <span className="active-link-crumb">{databaseName}</span>
            </div>
          </Col>
          <Col xs={12}>
            <h5 className="dashboard-heading-databases">
              {databaseName} Statisctics
            </h5>
          </Col>
        </Row>
        <Row style={{ width: "100%" }} className="m-0 p-0">
          <div className="server-bar-width">
            <div className="archived-header">
              <h6 className="archived-heading">Database Archived Data</h6>
              <DateRangePicker
                handleDateRangeChange={handleDateRangeChange}
                handleFilterLabel={handleFilterLabel}
              />
            </div>
            <DatabaseBarCard
              databaseArchivedCardData={databaseArchivedCardData}
              databaseArchivedBarPanelData={databaseArchivedBarPanelData?.data}
              databaseLabels={databaseArchivedBarPanelData?.label}
              tableData={databaseArchivedListingPanelData}
            />
            <ArchivingTable
              heading={"Database Table Archiving Activities"}
              name={"Table name"}
              totalRows={"Total Rows Archived"}
              totalData={`Total Archive Data (${
                filter ? filter : "This week"
              })`}
              data={databaseArchivedListingPanelData}
              tableName={serverName}
              databaseName={databaseName}
              serverId={serverId}
            />
          </div>
        </Row>
        {showNotification && (
          <Notification
            responseMessage={responseMessage}
            isError={isError}
            handleToastClose={handleToastClose}
          />
        )}
      </Container>
    </>
  );
}

export default DatabaseDashboard;
