import React, { useState, useEffect } from "react";
import { Col, Card } from "react-bootstrap";
import InfoCard from "../common/InfoCard";
import UploadImae from "./../../assets/images/fetch-upload--cloud.svg";
import TableImage from "./../../assets/images/table--split (1).svg";
import { StackChart } from "../charts/StackChart";
import { colors } from "../../constants/colors";
import { extractServerDataValues } from "../../utils/helpers";

const DatabaseBarCard = ({
  databaseArchivedCardData,
  databaseArchivedBarPanelData,
  databaseLabels,
}) => {
  const [maxSize, setMaxSize] = useState();
  
  // Extracting data for the StackChart component
  const totalColor = colors.length;
  const data = databaseArchivedBarPanelData
    ? databaseArchivedBarPanelData?.map((server, index) => {
        const data = server?.data;
        const values = extractServerDataValues(data);
        const colorIndex = index % totalColor;
        return {
          label: server?.name,
          data: values,
          backgroundColor: colors[colorIndex],
          borderColor: "transparent",
        };
      })
    : [];

     // Effect to calculate the maximum size for the chart
  useEffect(() => {
    if (
      !databaseArchivedBarPanelData ||
      !databaseArchivedBarPanelData[0]?.data
    ) {
      return;
    }

    let maxSum = 0;
    const keys = Object.keys(databaseArchivedBarPanelData[0].data);
 // Calculate total sum if there is only one label
    if (databaseLabels?.length === 1) {
      const totalSum = databaseArchivedBarPanelData.reduce(
        (accumulator, server) => {
          const value = Object.values(server?.data);
          return (
            accumulator +
            (Array.isArray(value) ? value.reduce((sum, v) => sum + v, 0) : 0)
          );
        },
        0
      );
      maxSum = totalSum;
    }

    if (keys.length > 0) {
      keys.forEach((key) => {
        const sumAtIndex = databaseArchivedBarPanelData.reduce(
          (accumulator, server) => {
            const value = server?.data?.[key];
            return accumulator + (value !== undefined ? value : 0);
          },
          0
        );
        maxSum = Math.max(maxSum, sumAtIndex);
      });
    }
     // Round the maximum value to the nearest multiple of 5
    const roundedMaxValue = Math.ceil(maxSum / 5) * 5;
    setMaxSize(roundedMaxValue);
  }, [databaseArchivedBarPanelData, databaseLabels]);
  return (
    <Card className="server-bar-container p-0">
      <Card.Header className="w-100 p-0 m-0 border-0">
        <div className=" px-0  my-custom-gutter-row">
          <Col className="mx-0" style={{ paddingRight: "0px" }}>
            <InfoCard
              content={"Total Archived Data"}
              count={
                databaseArchivedCardData?.total_table_data_archived
                  ? `${parseFloat(
                      databaseArchivedCardData?.total_table_data_archived
                    ).toFixed(2)} MB`
                  : "0 MB"
              }
              logo={UploadImae}
              fontSize={"32px"}
            />
          </Col>
          <Col className="mx-0" style={{ paddingRight: "0px" }}>
            <InfoCard
              content={"Total Archived table"}
              count={
                databaseArchivedCardData?.total_tables_archived
                  ? databaseArchivedCardData?.total_tables_archived
                  : "0"
              }
              logo={TableImage}
              fontSize={"32px"}
            />
          </Col>
        </div>
      </Card.Header>
      <div className="body-chart-container ">
        <div className="stack-bar-container">
          <StackChart labels={databaseLabels} datasets={data} maxSize={maxSize} />
        </div>
      </div>
    </Card>
  );
};

export default DatabaseBarCard;
