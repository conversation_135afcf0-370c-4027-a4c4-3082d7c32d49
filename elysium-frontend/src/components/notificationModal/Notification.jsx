import React, { useState } from "react";
import { Toast } from "react-bootstrap";
import { CiCircle<PERSON><PERSON>ck, CiCircleRemove } from "react-icons/ci";
import "./style.css";

function Notification({
  responseMessage,
  isError,
  handleToastClose,
}) {
  const [showNotificationStatus, setShowNotificationStatus] = useState(true);
  const toggleShow = () => {
    setShowNotificationStatus(!showNotificationStatus)
    handleToastClose && handleToastClose(false);
  };

  return (
    <Toast
      show={showNotificationStatus}
      onClose={toggleShow}
      className="success-box position-fixed"
    >
      <Toast.Header className="p-3">
        {isError ? (
          <>
            <CiCircleRemove
              className="me-2"
              style={{ color: "red", width: "20px", height: "20px" }}
            />
            <h6 className="me-5 m-0">
              {Array.isArray(responseMessage)
                ? responseMessage.map((res, index) => (
                    <div key={index}>{res}</div>
                  ))
                : responseMessage}
            </h6>
          </>
        ) : (
          <>
            <CiCircleCheck
              className="me-2"
              style={{ color: "#169C00", width: "20px", height: "20px" }}
            />
            <h6 className="me-5 m-0 dark-color">
              {Array.isArray(responseMessage)
                ? responseMessage.map((res, index) => (
                    <div key={index}>{res}</div>
                  ))
                : responseMessage}
            </h6>
          </>
        )}
      </Toast.Header>
    </Toast>
  );
}

export default Notification;
