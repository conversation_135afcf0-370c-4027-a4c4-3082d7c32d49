.choose-plan-title {
  font-weight: 700;
  font-size: 32px;
  line-height: 40px;
  letter-spacing: -0.02em;
}

.btn-yearly {
  background: #F6F8FC;
  padding: 2px 8px;
}

.btn-dark {
  background: '#000117';
  color: white;
}

.btn-light {
  background: #F6F8FC;
}


.container-fluid .trail-btn-row {
  margin: 40px 0 32px 0;

}

.trail-btn-row .btn {
  padding: 14px 20px;
  width: max-content;
  cursor: pointer;
}

.row .selected-card {
  background: black;
  color: white;
}

.row .selected-card {
  accent-color: white;
}

.key-benefits-text {
  font-weight: 700;
  font-size: 18px;
  line-height: 24px;
}

.disabled-benefit-list {
  opacity: 0.5;
}

.row .price-text {
  font-weight: 700;
  font-size: 24px;
  line-height: 32px;
  letter-spacing: -0.02em;
}

.row .price-plan-card {
  width: 282px;
  height: 408px;
  padding: 32px 24px
}

.card-label {
  background: linear-gradient(90deg, #6206B8 0%, #00B6D6 100%);
  border-radius: 8px 8px 0px 0px;
  color: white;
  font-size: 12px;
}

.price-plan-radio-btn {
  width: 20px;
  height: 20px;
  cursor: pointer;
}

.row .contiune-btn-invalid {
  background: #FFFFFF;
  border: 1px solid #C6CCDC;
  border-radius: 9px;
}

.row .contiune-btn-valid {
  background: #FFFFFF;
  border: 1px solid #DADDE5;
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);
  border-radius: 9px;
}

.keys-services {
  font-size: 14px;
  line-height: 20px;
}

.keys-val-services {
  font-size: 14px;
  line-height: 20px;
  list-style: none;
  margin-bottom: 8px;
}

.button-toggle {
  padding: 4px;
  background: #F6F8FC;
  border-radius: 5px;
}