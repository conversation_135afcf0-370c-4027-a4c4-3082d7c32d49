import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom';
import { Row, Col, Button, Card } from 'react-bootstrap';
import Model from '../alertModel/Model';
import { BsCheck, BsArrowRight } from "react-icons/bs";
import { freeTrailDetails } from '../../constants/pricingConstant';
import { serviceCall } from '../../services/pricePlan';
import { baseUrl } from "../../config/constants";
import Loader from '../loader/Loader';
import axios from 'axios';
import { handleCookies } from '../../utils/cookies'

function PricePlanCard() {

  const navigate = useNavigate();
  const token = 'Bearer ' + handleCookies.fetchCookies()
  const [selectedCard, setSelectedCard] = useState();
  const [pricePlanArray, setPricePlanArray] = useState([])
  const [pricePlanObj, setPricePlanObj] = useState({})
  const [cardColor, setCardColor] = useState(null);
  const [toggle, setToggle] = useState('Monthly');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [open, setOpen] = useState(true);
  const [checkTrialperiod, setCheckTrialPeriod] = useState(true);
  const [enterprisePlan, setEnterprisePlan] = useState(false);

  useEffect(() => {
    fetchPricePlans();
  }, [toggle]);

  const fetchPricePlans = async () => {
    try {
      let payload = {
        "subscription_type_id": toggle === 'Monthly' ? 2 : 3
      }
      const response = await serviceCall.getAllSubscriptions(payload);
      if (response.status === 'Success' && response.data.subscriptionPlans) {
        getUserInfo();
        setOpen(false);
        setPricePlanArray(response.data.subscriptionPlans);
      }
    } catch (error) {
      setOpen(false);
      setPricePlanArray([]);
    }
  };

  const getUserInfo = async () => {
    try {
      const response = await axios.post(`${baseUrl}/auth/user`, {}, {
        headers: {
          Authorization: token,
        },
      });
      if (response && response.data.data.user) {
        setCheckTrialPeriod(!response.data.data.user.is_trial_active && response.data.data.user.trial_ends_at === null);
      }
    } catch (error) {
      setCheckTrialPeriod(true);
    }
  }

  const handleTrialModel = () => {
    setIsModalOpen(true);
  };

  const handleModalSuccess = async () => {
    const response = await serviceCall.postSubscription()
    if (response.status === 'Success') {
      setIsModalOpen(false);
      navigate('/');
    }
  };

  const togglePlan = (val) => {
    setOpen(true);
    setToggle(val);
    setSelectedCard();
    setCardColor();
    setPricePlanObj({});
  };

  const monthlyVariant = toggle === 'Monthly' ? 'dark' : 'light';
  const yearlyVariant = toggle === 'Yearly' ? 'dark' : 'light';

  const handleRadioChange = (event, index, title, price, id, tag, plan_price_end_date) => {
    setCardColor(index);
    setSelectedCard(index);
    setPricePlanObj({ title, price, id, tag, plan_price_end_date })
    setEnterprisePlan(title === 'Enterprise');
  }

  const handleContinueToPayment = () => {
    navigate('/payment', { state: { pricePlanObj } });
  }

  const handleContactSupport = () => {
    const link = document.createElement('a');
    link.href = 'mailto:<EMAIL>';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  return (
    <>
      <Loader open={open} />
      <Row className='mt-4 button-toggle'>
        <Col sm={6} className='px-1'>
          <Button variant={monthlyVariant} onClick={() => togglePlan('Monthly')}> Monthly</Button>
        </Col>
        <Col sm={6} className='px-1 text-center'>
          <Button variant={yearlyVariant} onClick={() => togglePlan('Yearly')}> Yearly</Button>
        </Col>
      </Row>
      {/* for pricing card row */}
      <Row className='mt-2 align-items-end'>

        {pricePlanArray.length > 0 && pricePlanArray.map((card, i) => (
          <Col key={i} className="md-3" >
            {i === 0 && <div className='card-label text-center p-1'>Elysium Recommends</div>}
            <Card
              className={cardColor === i ? "selected-card price-plan-card " : " price-plan-card"}
            >
              <Card.Body className='p-0'>
                <Row className="align-items-center">
                  <Col>
                    <Card.Title className='price-text'> <b>{card.plan_name}</b> </Card.Title>
                  </Col>
                  <Col xs="auto">
                    <input
                      type="radio"
                      className='price-plan-radio-btn'
                      id={`radio--${i}`}
                      name={`radio-group`}
                      value={card.list}
                      onChange={(e) => handleRadioChange(e, i, card.plan_name, card.plan_price, card.id, card.subscription_type, card.plan_price_end_date)}
                      checked={selectedCard === i}
                    />
                  </Col>
                </Row>
                <p>
                  <b className='price-text'> {card.plan_price} </b>
                  <span className='keys-services'>
                    {card.priceDescription} {card.subscription_type_id === 2 ?
                      card.plan_name === 'Enterprise' ? '/yearly billing only' : ' /month'
                      : card.plan_name === 'Enterprise' ? '/yearly billing only' : ' /year'}
                  </span>
                </p>

                <li style={{ fontSize: 14, color: '#5C5D6A' }}> {card.table_plan_limit}</li>
                {card.price_per_table &&
                  <li style={{ fontSize: 14, color: '#5C5D6A' }}> {card.price_per_table}</li>
                }

                {i === 3 && <br />}
                <h6 className='mt-3 key-benefits-text'> Key Benefits:</h6>

                {Object.entries(card.key_benefits).map(([text, value], i) => (
                  < li key={i}
                    className={`keys-val-services ${value === 0 ? 'disabled-benefit-list' : ''}`}
                  >
                    <BsCheck style={{ color: '#169C00' }} /> {text} </li>
                ))
                }
              </Card.Body>
            </Card>
          </Col>
        ))}
      </Row >
      <Row className='trail-btn-row'>
        {checkTrialperiod &&
          <Col>
            <Button variant='dark' className='trail-btn' onClick={handleTrialModel}>Get First 14 Days Free</Button>
          </Col>
        }
        <Col>
          <Button variant=''
            disabled={cardColor === 0 || cardColor > 0 ? false : true}
            onClick={enterprisePlan ? handleContactSupport : handleContinueToPayment}
            style={{ color: cardColor === 0 || cardColor > 0 ? '' : '#C6CCDC' }}
            className={cardColor === 0 ? 'contiune-btn-invalid' : 'contiune-btn-valid'}
          >{enterprisePlan ? 'Contact Us' : 'Continue'} <BsArrowRight />
          </Button>
        </Col>
      </Row>

      {
        isModalOpen && (
          <Model onSuccess={handleModalSuccess}
            showModel={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            data={freeTrailDetails}
          />
        )
      }
    </>
  )
}

export default PricePlanCard