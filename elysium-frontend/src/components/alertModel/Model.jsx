import React from 'react';
import { But<PERSON>, Modal } from 'react-bootstrap';

function Model(props) {

  const handleClose = () => {
    props.onClose()
  }

  return (
    <>
      <Modal
        show={props.showModel}
        onHide={handleClose}
        backdrop="static"
        keyboard={false}
      >
        <Modal.Header closeButton>
          <Modal.Title>Free trial</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>
            Experience our service for free! During the 14-day trial, you can:
          </p>
          <ul>
            {props.data.map((item, index) => (
              <li key={index}>{item}</li>
            ))}
          </ul>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleClose}>
            Cancel
          </Button>
          <Button variant="dark" onClick={props.onSuccess}>Proceed</Button>
        </Modal.Footer>
      </Modal>
    </>
  );
}

export default Model