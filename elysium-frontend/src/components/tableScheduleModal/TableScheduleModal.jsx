import Modal from "react-bootstrap/Modal";
import "./style.css";
import close from "./../../assets/images/close (1).svg";
import { useEffect, useState } from "react";
import LabeledInput from "../common/InputField";
import LocationIcon from "./../../assets/images/location--filled.svg";
import TimeIcon from "./../../assets/images/time.svg";
import CalendarIcon from "./../../assets/images/calendar--heat-map.svg";
import WarningMessage from "./WarningMessage";
import { updateUserInfo } from "../../services/updateProfileInfo";
import { serviceCall } from "../../services/scheduleServices";
import Notification from "../notificationModal/Notification";
import Loader from "../loader/Loader";
import { daysName, scheduleTime } from "../../config/constants";

function ScheduleModal(props) {
  const [open, setOpen] = useState(false);
  const [isError, setIsError] = useState();
  const [responseMessage, setResponseMessage] = useState();
  const [showNotification, setShowNotification] = useState(false);
  const [timezone, setTimezone] = useState([]);
  const [formData, setFormData] = useState({
    selectedOption: "",
    selectedDays: [],
    retention: 60,
    batchSize: 10000,
    s3BucketName: "CS",
    scheduleId: "",
    s3BucketDirectoryName: "",
    showWarningMessage: false,
    startSelectedTime: "",
    endSelectedTime: "",
    selectedTimezone: "",
  });

  const getTimezone = async () => {
    const response = await updateUserInfo.getTimezone();
    if (response && response.data) {
      setTimezone(response.data.timezones);
    } else {
      setShowNotification(true);
      setIsError(true);
      setResponseMessage(response);
    }
  };

  useEffect(() => {
    getTimezone();
  }, []);

  const handleInputChange = (field, value) => {
    setFormData((prevData) => ({
      ...prevData,
      [field]: value,
    }));
  };

  const handleRadioChange = (event) => {
    setFormData((prevData) => ({
      ...prevData,
      selectedOption: event.target.value,
      showWarningMessage: event.target.value === "Archive",
    }));
  };

  const handleDayClick = (dayId) => {
    setFormData((prevData) => ({
      ...prevData,
      selectedDays: prevData.selectedDays.includes(dayId)
        ? prevData.selectedDays.filter((day) => day !== dayId)
        : [...prevData.selectedDays, dayId],
    }));
  };

  const addScheduleTimeSetup = async (payload) => {
    setOpen(true);
    try {
      const response = await serviceCall.addScheduleTime(payload);
      if (response?.status === "Success") {
        await props?.handleReload();

        resetFormData();
        props?.onHide();
        props?.showToast();
      } else {
        setResponseMessage(
          response?.messages
            ? response?.messages
            : response?.response.data?.message || "Something went wrong"
        );
        setIsError(true);
        setShowNotification(true);
      }
    } catch (error) {
     
    } finally {
      setOpen(false);
    }
  };

  const getScheduleTimeSetupById = async (payload) => {
    setOpen(true);
    try {
      const response = await serviceCall.getScheduleTime(payload);
      if (response?.status === "Success") {
        const scheduleData = response?.data?.schedule_time[0];

        setFormData((prevData) => ({
          ...prevData,
          startSelectedTime: scheduleData?.start_time,
          endSelectedTime: scheduleData?.end_time,
          selectedTimezone: scheduleData?.time_zone,
          retention: scheduleData?.retention,
          selectedDays: scheduleData?.day_of_week,
          scheduleId: scheduleData?.schedule_id,
          batchSize: scheduleData?.batch_size,
          selectedOption: scheduleData?.schedule_type,
          s3BucketDirectoryName: scheduleData?.s3_directory_name,
        }));
      } else {
        setResponseMessage(
          response?.messages
            ? response?.messages
            : response?.response.data?.message || "Something went wrong"
        );
        setIsError(true);
        setShowNotification(true);
      }
    } catch (error) {
     
    } finally {
      setOpen(false);
    }
  };
  const handleSubmit = () => {
    const payload = {
      schedule_id: formData.scheduleId,
      retention: formData.retention,
      batch_size: formData.batchSize,
      s3_directory: formData.s3BucketDirectoryName,
      time_zone: formData.selectedTimezone,
      start_time: `${formData.startSelectedTime}:00:00`,
      end_time: `${formData.endSelectedTime}:00:00`,
      day_of_week: formData?.selectedDays.reduce(
        (sum, day) => sum + parseInt(day),
        0
      ),
      schedule_type: formData.selectedOption,
    };

    addScheduleTimeSetup(payload);
  };
  const initialFormData = {
    selectedOption: "",
    selectedDays: [],
    retention: 60,
    batchSize: 10000,
    s3BucketName: "CS",
    scheduleId: "",
    s3BucketDirectoryName: "",
    showWarningMessage: false,
    startSelectedTime: "",
    endSelectedTime: "",
    selectedTimezone: "",
  };

  const resetFormData = () => {
    setFormData(initialFormData);
  };

  useEffect(() => {
    if (props?.scheduleData) {
      setFormData((prevData) => ({
        ...prevData,
        scheduleId: props?.scheduleData?.scheduleId,
        s3BucketDirectoryName: props?.scheduleData?.tableName,
        s3BucketName: props?.scheduleData?.s3BucketName,
      }));
    }

    if (props?.scheduleData?.isTimeZone) {
      getScheduleTimeSetupById({
        schedule_id: props?.scheduleData?.scheduleId,
      });
    }
  }, [props?.scheduleData]);

  const handleToastClose = () => {
    setShowNotification(false);
    setResponseMessage("");
    setIsError(false);
  };
  const isButtonVisible =
    formData.retention &&
    formData.batchSize &&
    formData.s3BucketName &&
    formData.selectedOption &&
    formData.endSelectedTime &&
    formData.startSelectedTime &&
    formData.selectedDays.length > 0 &&
    formData.selectedTimezone;
  return (
    <>
      <Loader open={open} />
      <Modal
        show={props.show}
        onHide={props.onHide}
        size="sm"
        aria-labelledby="contained-modal-title-vcenter"
        dialogClassName="db-server-schedule-modal-size"
        centered
      >
        <div className="table-schedule-modal-main-container">
          <div className="schedule-modal-heading-container">
            <div className="heading-schedule-modal-heading">
              Table{" "}
              {formData.selectedOption.charAt(0).toUpperCase() +
                formData.selectedOption.slice(1)}{" "}
              Schedule
            </div>
            <img
              className="close-icon"
              onMouseOver={(e) => {
                e.target.style.border = "1px solid #dadde5";
              }}
              onMouseOut={(e) => {
                e.target.style.border = "1px solid transparent";
              }}
              src={close}
              onClick={props?.onHide}
              alt="X"
            />
          </div>

          <div className="radio-group-container">
            <label className="radio-option">
              <input
                type="radio"
                value="Export"
                className="radio-option-point"
                checked={formData.selectedOption === "Export"}
                onChange={handleRadioChange}
                name="actionGroup"
              />
              <span className="radio-label">Export</span>
            </label>

            <label className="radio-option">
              <input
                type="radio"
                value="Archive"
                className="radio-option-point"
                checked={formData.selectedOption === "Archive"}
                onChange={handleRadioChange}
                name="actionGroup"
              />
              <span className="radio-label">Archive</span>
            </label>
          </div>
          <WarningMessage
            show={formData.showWarningMessage}
            onHide={() => handleInputChange("showWarningMessage", false)}
          />
          <LabeledInput
            label="Data Retention"
            value={formData.retention}
            onChange={(e) => handleInputChange("retention", e.target.value)}
          />
          <LabeledInput
            label="Batch Size"
            value={formData.batchSize}
            onChange={(e) => handleInputChange("batchSize", e.target.value)}
          />
          <LabeledInput
            label="S3 Bucket Name"
            disabled={true}
            value={formData.s3BucketName}
            onChange={(e) => handleInputChange("s3BucketName", e.target.value)}
          />
          <LabeledInput
            label="S3 Bucket Directory Name"
            value={formData.s3BucketDirectoryName}
            onChange={(e) =>
              handleInputChange("s3BucketDirectoryName", e.target.value)
            }
          />
          <div className="schedule-modal-fields-container">
            <div className="usr-time-zone-container">
              <img
                style={{ height: "16px", width: "16px" }}
                alt="Location Icon"
                src={LocationIcon}
              />
              <div className="user-time-zone-selected">
                <select
                  className="user-time-zone-select-from-drp-dwn"
                  value={formData.selectedTimezone}
                  onChange={(e) =>
                    handleInputChange("selectedTimezone", e.target.value)
                  }
                >
                  <option value="">User’s Time Zone</option>
                  {timezone.map((data) => (
                    <option key={data.id} value={data.name}>
                      {data.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div className="usr-time-zone-container">
              <img
                style={{ height: "16px", width: "16px" }}
                alt="Time Icon"
                src={TimeIcon}
              />
              <div className="user-time-zone-selected">
                <select
                  className="user-time-zone-select-from-drp-dwn"
                  value={formData.startSelectedTime}
                  onChange={(e) =>
                    handleInputChange("startSelectedTime", e.target.value)
                  }
                >
                  <option value="">Start Time</option>
                  {scheduleTime.map((time) => (
                    <option key={time?.id} value={time?.id}>
                      {time?.name}
                    </option>
                  ))}
                </select>
              </div>
              <div className="user-time-zone-selected">
                <select
                  className="user-time-zone-select-from-drp-dwn"
                  value={formData.endSelectedTime}
                  onChange={(e) =>
                    handleInputChange("endSelectedTime", e.target.value)
                  }
                >
                  <option value="">End Time</option>
                  {scheduleTime
                    .filter((time) => time.id > formData.startSelectedTime)
                    .map((time) => (
                      <option key={time?.id} value={time?.id}>
                        {time?.name}
                      </option>
                    ))}
                </select>
              </div>
            </div>
            <div className="user-time-zone-selected-days">
              <img
                style={{ height: "16px", width: "16px" }}
                alt="Calendar Icon"
                src={CalendarIcon}
              />
              <div className="days-container-in-modal">
                {daysName?.map((day, i) => (
                  <div
                    key={i}
                    className={`day-name-for-schedule ${
                      formData.selectedDays.includes(day.id) ? "selected" : ""
                    }`}
                    onClick={() => handleDayClick(day.id)}
                  >
                    {day?.name}
                  </div>
                ))}
              </div>
            </div>
          </div>
          <div className="delete-confirm-modal-btn-container">
            <button
              className="delete-confirm-modal-cencel-btn"
              onClick={() => {
                props?.onHide();
                resetFormData();
              }}
            >
              Cancel
            </button>
            <button
              className={`schedule-confirm-modal-add-btn ${
                isButtonVisible ? "selected" : ""
              }`}
              onClick={handleSubmit}
              disabled={!isButtonVisible}
            >
              Schedule
            </button>
          </div>
        </div>
      </Modal>
      {showNotification && (
        <Notification
          responseMessage={responseMessage}
          isError={isError}
          handleToastClose={handleToastClose}
        />
      )}
    </>
  );
}

export default ScheduleModal;
