import React from 'react';
import "./style.css";
import Alert from './../../assets/images/alert-triangle (1).svg';
import CloseIcon from "./../../assets/images/close (1).svg";

const WarningMessage = ({ show, onHide  }) => {
  return (
    <div className={`warning-message-container ${show ? 'visible' : 'hidden'}`}>
      <img style={{ height: '20px', width: "20px", marginRight: "12px" }} src={Alert} alt="" />
      <div className='warning-message-text-font'>By selecting Archive, you agree that the data will be deleted from the table after it is safely archived into Object storage</div>
      <img className='warning-close-icon' src={CloseIcon} onClick={onHide} alt="Close" />
    </div>
  );
}

export default WarningMessage;
