.table-schedule-modal-main-container {
  display: flex;
  width: 393px;
  padding: 24px;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  border-radius: 8px;
  border: 1px solid var(--Shades-Desaturate-10, #dadde5);
  background: var(--dark-light-light, #fff);

  /* Shadow/M */
  box-shadow: 0px 4px 8px -2px rgba(0, 0, 0, 0.1),
    0px 2px 4px -2px rgba(0, 0, 0, 0.06);
}

.schedule-modal-heading-container {
  display: flex;

  justify-content: space-between;
  align-items: center;
  gap: 12px;
  align-self: stretch;
}

.heading-schedule-modal-heading {
  color: var(--Text-On-White-High-Contrast, #000117);

  /* Paragraph/L/Bold */
  font-family: Ubuntu;
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
  line-height: 28px; /* 155.556% */
}

.schedule-modal-radio-btn-container {
}
/* RadioBtnGroup.css */

/* RadioBtnGroup.css */

.radio-group-container {
  display: flex;
  gap: 24px;
}

.radio-option-point {
  accent-color: black;
  height: 20px;
  width: 20px;
  border: 1px solid var(--Shades-Desaturate-10, #dadde5);
  cursor: pointer;
}

.radio-option {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.radio-label {
  margin-left: 8px;
  color: #403a44;

  /* Paragraph/S/Medium */
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px; /* 142.857% */
}

.schedule-modal-fields-container {
  display: flex;
  padding: 16px 12px;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  align-self: stretch;
  border-radius: 8px;
  border: 1px solid var(--Shades-Desaturate-10, #dadde5);
  background: var(--Shades-Desaturate-11, #f6f8fc);
  min-height: 184px;
}
.usr-time-zone-container {
  display: flex;
  align-items: center;
  gap: 12px;
  align-self: stretch;
}
/* Style the container div */
.user-time-zone-selected {
  position: relative;
  width: 293px;
}

/* Style the select element */
.user-time-zone-selected select {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--Shades-Desaturate-10, #dadde5);
  border-radius: 5px;
  font-family: Ubuntu;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  color: var(--Text-On-White-High-Contrast, #000117);
  background-color: #fff;
}

/* Style the arrow icon for the select element */
.user-time-zone-selected select::after {
  content: "\25BC"; /* Unicode character for down arrow */
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
}
.user-time-zone-select-from-drp-dwn {
  /* Add your default styles here */
cursor: pointer;
  /* Styles for focused state */
  &:focus {
    border-color: #474950; 
    outline-color: #474950; 
  }

  /* Styles for selected state */
  &:checked {
    border-color: #474950; 
    outline-color: #474950; 
  }
}


/* Style the options inside the dropdown */
.user-time-zone-selected select option {
  background-color: #fff;
  color: var(--Text-On-White-High-Contrast, #000117);
  font-family: Ubuntu;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  border: 1px solid var(--Shades-Desaturate-10, #dadde5) !important;
}

/* Style the selected option */
.user-time-zone-selected select option:checked {
  background-color: #f0f0f0;
}

.user-time-zone-selected-days {
  display: flex;
  align-items: center;
  gap: 12px;
  align-self: stretch;
}

.day-name-for-schedule {
  display: flex;
  height: 40px;
  min-width: 36px;
  justify-content: center;
  align-items: center;
  flex: 1 0 0;
  border-radius: 8px;
  border: 1px solid var(--Shades-Desaturate-10, #dadde5);
  background: var(--dark-light-light, #fff);
  color: var(--Text-On-White-High-Contrast, #000117);

  /* Paragraph/S/Regular */
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 142.857% */
  cursor: pointer;
}

.days-container-in-modal {
  display: flex;
  gap: 6px;
}

.day-name-for-schedule.selected {
  border-radius: 8px;
  border: 1px solid var(--dark-light-dark, #000117);
  background: var(--dark-light-light, #fff);
}

.schedule-confirm-modal-add-btn {
  display: flex;
  height: 40px;
  padding: 0px 16px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex: 1 0 0;
  border-radius: 8px;
  background: var(--Shades-Desaturate-10, #dadde5);
  color: var(--Shades-Desaturate-8, #9ba4bc);
  text-align: center;

  /* Button/S/Regular */
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px; /* 114.286% */
  letter-spacing: 0.28px;
  border: none;
  outline: none;
}
.schedule-confirm-modal-add-btn.selected {
  background: var(--dark-light-dark, #000117);
  color: var(--dark-light-light, #fff);
}

.warning-message-container {
  display: flex;
  width: 343px;
  padding: 16px 11px;
  align-items: flex-start;
  display: none;
  border-radius: 12px;
  border: 1px solid var(--States-Warning, #e46b07);
  background: var(--Warning-25, #fffcf5);
}

.warning-message-text-font {
  color: var(--States-Warning, #e46b07);

  /* Paragraph/S/Bold */
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  line-height: 20px; /* 142.857% */
}

.warning-message-container.visible {
  display: flex; /* show the component when visible class is present */
}

.warning-message-container.hidden {
  display: none; /* hide the component when hidden class is present */
}


.db-server-schedule-modal-size{
  min-width: 393px !important;
}
@media (max-width: 580px) {
  .db-server-schedule-modal-size {
    max-width: 393px !important;
    margin-top: -150px !important;
  }
}

.warning-close-icon {
  width: 16px;
  height: 16px;
  cursor: context-menu; /* Use context-menu cursor for a warning icon */
  border: 1px solid transparent;
  border-radius: 2px;
  transition: border-color 0.3s ease; /* Add transition for a smooth effect when changing border color */
}

/* Change border color on hover */
.warning-close-icon:hover {
  border-color: #e46b07; /* Adjusted border color for the warning effect */
  border-radius: 2px;
  cursor: pointer; /* Change cursor to pointer on hover */
}

/* Add a class to your image element, for example, close-icon */
.close-icon {
  width: 16px;
  height: 16px;
  border: 1px solid transparent;
  border-radius: 2px; /* Add border-radius for a slightly rounded appearance */
  box-shadow: none; /* Optional: Set an initial box-shadow, adjust as needed */
  transition: border-color 0.3s ease, box-shadow 0.3s ease; /* Add box-shadow transition */
}

/* Change border color and box-shadow on hover */
.close-icon:hover {
  border-color: #56575c;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2); /* Optional: Add a subtle box-shadow on hover */
  cursor: pointer ;
}
