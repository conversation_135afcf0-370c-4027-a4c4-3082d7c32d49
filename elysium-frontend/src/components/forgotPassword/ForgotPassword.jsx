import React, { useState } from 'react'
import { Container, Row, Col, Button, Card, Form } from 'react-bootstrap';
import { serviceCall } from '../../services/forgotPassword'
import { useFormik } from "formik";
import { useNavigate } from 'react-router-dom';
import { forgotPasswordSchema } from '../../utils/forgotPasswordValidation'
import "./style.css"
import Sidebar from '../../pages/sidebar/Sidebar'
import Loader from '../loader/Loader';

function ForgotPassword() {
  const [open, setOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const navigate = useNavigate();
  const initialValues = {
    email: ''
  };
  const { values, handleChange, handleSubmit, errors, touched } =
    useFormik({
      initialValues,
      validationSchema: forgotPasswordSchema,
      validateOnChange: true,
      validateOnBlur: false,
      onSubmit: async (values, action) => {
        setOpen(true);
        const response = await serviceCall.forgotPassword(values);
        setOpen(false);
        if (response.status === 'Success') {
          navigate('/emailverification', {
            state:
              { navigationFlag: true, email: values.email }
          })
        } else {
          action.resetForm();
          setErrorMessage(response.message);
        }
      }
    });
  return (
    <>
      <Loader open={open} />
      <Container fluid>
        <Row>
          <Col sm={12} md={4} lg={3} className='sidebar-wrapper'>
            <Sidebar />
          </Col>
          <Col sm={12} md={8} lg={9}>
            <div className='d-flex justify-content-center align-items-center vh-100'>
              <Card className='mx-auto border-0'>
                <Card.Body>
                  <div className='forgot-pwd-title'>Forgot password ?</div>
                  <Card.Text>
                    Enter the email to which your account is assigned and <br /> we will send a verification code.
                  </Card.Text>
                  <Form onSubmit={handleSubmit}>
                    <Form.Group>
                      <Form.Control
                        placeholder="Email"
                        type="email"
                        autoComplete="off"
                        name="email"
                        id="email"
                        value={values.email}
                        onChange={handleChange} />

                      {errors.email && touched.email &&
                        <div className="forgot-password-field-error p-1">
                          {errors.email}
                        </div>
                      }
                    </Form.Group>
                    {errorMessage && (
                      <div className="signin-field-error">{errorMessage}</div>
                    )}
                    <Button variant="dark" className='send-code-btn mt-2' type='submit'>Send Code</Button>
                  </Form>
                </Card.Body>
              </Card>
            </div>
          </Col>
        </Row>
      </Container>
    </>
  )
}

export default ForgotPassword