import React, { useState } from "react";
import Header from "../../pages/header/Header";
import { Container, Row, Col } from "react-bootstrap";
import "./style.css";
import ActivityList from "../activityList/ActivityList";
import ChartCard from "../chartCard/ChartCard";
import DateRangePicker from "../common/Calendar";
import { serviceCall } from "../../services/dashboard";
import Loader from "../loader/Loader";
import {
  convertToISODate,
  convertToMonthName,
  formatDateIntoDays,
} from "../../utils/helpers";
import { calculateDaysFlag } from "../../constants/calender";
import Notification from "../notificationModal/Notification";

function Dashboard() {
  const [open, setOpen] = useState(false);
  const [archivedData, setArchivedData] = useState();
  const [activityData, setActivityData] = useState();
  const [serverArchivingDates, setServerArchivingDates] = useState();
  const [serverData, setServerData] = useState();
  const [tableData, setTableData] = useState();
  const [responseMessage, setResponseMessage] = useState();
  const [showNotification, setShowNotification] = useState(false);
  const [isError, setIsError] = useState(false);

  // Function to fetch archived data based on date range
  const getArchivedData = async (range) => {
    setOpen(true);
    try {
      const response = await serviceCall.getArchivedData(range);
      if (response?.status === "Success") {
        setArchivedData(response?.data?.dashboard_total_archive_stats[0]);
      } else {
        setArchivedData();
        setResponseMessage(
          response?.messages
            ? response?.messages
            : response?.response.data?.message || "Something went wrong"
        );
        setActivityData();
        setIsError(true);
        setShowNotification(true);
      }
    } catch (error) {
    } finally {
      setOpen(false);
    }
  };

  // Function to fetch bar chart data based on date range
  const getBarData = async (range) => {
    setOpen(true);
    try {
      const response = await serviceCall.getBarData(range);
      if (response?.status === "Success") {
        // Process and set server data and archiving date
        const dateParts =
          response?.data?.dashboard_barChart[0]?.dates[0]?.split("-");
        let label;
        if (dateParts?.length === 2) {
          label = await convertToMonthName(
            response?.data?.dashboard_barChart[0]?.dates
          );
        } else if (dateParts?.length === 3) {
          label = await formatDateIntoDays(
            response?.data?.dashboard_barChart[0]?.dates
          );
        } else if (dateParts?.length === 1) {
          label = response?.data?.dashboard_barChart[0]?.dates;
        }
        setServerData(response.data.dashboard_barChart[1]);
        setServerArchivingDates(label);
      } else {
        setServerData();
        setServerArchivingDates();
        setResponseMessage(
          response?.messages
            ? response?.messages
            : response?.response.data?.message || "Something went wrong"
        );
        setIsError(true);
        setShowNotification(true);
      }
    } catch (error) {
    } finally {
      setOpen(false);
    }
  };

  // Function to fetch activity data based on date range
  const getActivityData = async (range) => {
    setOpen(true);
    try {
      const response = await serviceCall.getActivity(range);
      if (response?.status === "Success") {
        setActivityData(response?.data?.dashboard_activities);
      } else {
        setResponseMessage(
          response?.messages
            ? response?.messages
            : response?.response.data?.message || "Something went wrong"
        );
        setActivityData();
        setIsError(true);
        setShowNotification(true);
      }
    } catch (error) {
    } finally {
      setOpen(false);
    }
  };

  // Function to fetch table data based on date range
  const getTableData = async (range) => {
    setOpen(true);
    try {
      const response = await serviceCall.getDashboardListingData(range);
      if (response?.status === "Success") {
        setTableData(response?.data?.dashboard_servers_listing);
      } else {
        setTableData();
        setResponseMessage(
          response?.messages
            ? response?.messages
            : response?.response.data?.message || "Something went wrong"
        );
        setIsError(true);
        setShowNotification(true);
      }
    } catch (error) {
    } finally {
      setOpen(false);
    }
  };

  // Function to handle date range change and fetch data accordingly
  const handleDateRangeChange = async (date) => {
    const range = await convertToISODate(date);
    const daysInRange = calculateDaysFlag(range?.start_date, range?.end_date);

    if (range?.start_date && range?.end_date) {
      const payload = {
        start_date: range?.start_date,
        end_date: range?.end_date,
        flag: daysInRange,
      };
      // Fetch data for different components based on date range
      getArchivedData(payload);
      getBarData(payload);
      getTableData(payload);
      getActivityData(payload);
    }
  };

  const handleToastClose = () => {
    setShowNotification(false);
    setResponseMessage("");
    setIsError(false);
  };
  return (
    <>
      <Header />
      <Loader open={open} />
      <Container fluid className="main-dashboard-container">
        <Row>
          <Col xs={12}>
            <h5 className="dashboard-heading">Dashboard</h5>
          </Col>
        </Row>
        <Row style={{ width: "100%" }} className="m-0 p-0">
          <div className="chart-width">
            <div className="archived-header">
              <h6 className="archived-heading">
                Archived Data by Database Server
              </h6>
              <DateRangePicker handleDateRangeChange={handleDateRangeChange} />
            </div>
            <ChartCard
              archivedData={archivedData}
              serverArchivingDates={serverArchivingDates}
              serverData={serverData}
              tableData={tableData}
            />
          </div>
          <div className="activity-width">
            <ActivityList activityLogs={activityData} />
          </div>
        </Row>
        {showNotification && (
          <Notification
            responseMessage={responseMessage}
            isError={isError}
            handleToastClose={handleToastClose}
          />
        )}
      </Container>
    </>
  );
}

export default Dashboard;
