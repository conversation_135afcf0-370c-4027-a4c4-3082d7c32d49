.main-dashboard-container {
  display: flex;
  padding: 48px 60px !important;
  flex-direction: column;
  align-items: flex-start;
  margin: auto;
  gap: 32px;
  align-self: stretch;
  min-height: 89vh;
  background: var(--shades-desaturate-11, #f6f8fc) !important;
}
.dashboard-container {
  width: 100%;
  display: flex;
  gap: 24px;
  height: 743px;
}
.dashboard-heading {
  padding-left: 17px;
  color: var(--gray-900, #101828);
  align-self: stretch;
  font-family: Ubuntu;
  font-size: 32px;
  font-style: normal;
  font-weight: 700;
  line-height: 40px;
  letter-spacing: -0.64px;
}
.archived-heading {
  color: var(--text-on-white-high-contrast, #000117);
  flex: 1 0 0;
  font-family: Inter;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 28px; /* 140% */
}
.archived-header-activity-container {
  display: flex;
  gap: 8px;
}
.archived-header {
  display: flex;
  justify-content: space-between;
  width: 100%;
}
.chart-card-dashboard-container {
  width: 80%;
}

.d-activity-container {
  width: 20%;
}

.calender-container {
  border: 1px solid var(--shades-desaturate-10, #dadde5);
  background: var(--dark-light-light, #fff);
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.1),
    0px 1px 2px 0px rgba(0, 0, 0, 0.06);
}
.my-input-group {
  color: var(--text-on-white-high-contrast, #000117) !important;
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}

.input-group-text {
  /* Base styles for input-group-text */
  border: 1px solid var(--shades-desaturate-10, #dadde5) !important;
  background: var(--dark-light-light, #fff) !important;
}

.input-group-text img {
  /* Styles for images inside input-group-text */
  margin: 0;
}

.input-group-text:nth-child(2) {
  width: 147px !important;
  border-left: 0 !important;
  margin-left: -12px !important;
}

.input-group-text:last-child {
  margin-right: 0 !important;
}

.activity-width {
  width: 23% !important;
}
.chart-width {
  width: 76% !important;
}
@media (max-width: 1200px) {
  .chart-width {
    width: 100% !important;
  }
  .activity-width {
    width: 100% !important;
    margin-top: 20px !important;
  }
}
@media (max-width: 1510px) {
  .main-dashboard-container {
    padding: 48px 10px !important;
  }
  .pie-chart-container {
    padding-left: 0px !important;
  }
}
@media (max-width: 1390px) {
  .pie-chart-container {
    flex-wrap: wrap;
    justify-content: center !important;
  }
  .activity-list-container {
    height: 894px !important;
  }
  .progress-bar-place {
    width: 95% !important; 
  }
}
@media (max-width: 768px) {
  .main-dashboard-container {
    height: auto !important;
    padding: 2% !important;
  }

  .chart-card-dashboard-container {
    width: 100%;
  }
  .dashboard-container {
    flex-wrap: wrap;
    height: auto;
  }
  .d-activity-container {
    width: 100%;
  }
  .chart-width {
    width: 100%;
  }
  .activity-width {
    width: 100%;
    margin-top: 20px;
  }
  .archived-header {
    display: block;
  }
  .input-group-text:nth-child(2) {
    width: 130px;
  }

  /* Add more styles as needed for smaller screens */
}

.mobile-head {
  width: 786px;
}
