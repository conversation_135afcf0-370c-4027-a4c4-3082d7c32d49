import React, { useEffect, useState } from "react";
import ProgressBar from "../common/Progress";
import { colors } from "../../constants/colors";
import { useNavigate } from "react-router";
import ArrowLeft from "./../../assets/images/arrow--left.svg";
import ArrowRight from "./../../assets/images/arrow--right.svg";
import ArrowRightVisible from "./../../assets/images/arrow--right-black.svg";
import ArrowleftVisible from "./../../assets/images/arrow-left-black.svg";
const itemsPerPage = 10;
/* eslint-disable react-hooks/exhaustive-deps */

const DasboardTable = ({ data }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageNumbers, setPageNumbers] = useState([]);
  const [renderNumbers, setRenderNumbers] = useState([]);
  const [rightNumbers, setRightNumbers] = useState([]);
  const [isPageClick, setIsPageClick] = useState(false);
  const navigate = useNavigate();

  const lastPageIndex = Math.ceil(data?.length / itemsPerPage);

  const renderData = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return data?.slice(startIndex, endIndex);
  };

  const handlePrevious = () => {
    if (
      currentPage >= 2 &&
      currentPage <= pageNumbers[pageNumbers?.length - 3] &&
      renderNumbers[0] > 1
    ) {
      renderNumbers.pop();
      renderNumbers.unshift(pageNumbers[renderNumbers[0] - 2]);
    }

    setCurrentPage((prevPage) => Math.max(1, prevPage - 1));
  };

  const handleNext = () => {
    const pageDiff = rightNumbers[0] - renderNumbers[1];
    if (pageNumbers.length > 2 && currentPage >= 2 && pageDiff >= 2) {
      renderNumbers?.shift();
      renderNumbers?.push(pageNumbers[renderNumbers[0]]);
      setCurrentPage((prevPage) => Math.min(lastPageIndex, prevPage + 1));
    } else {
      setCurrentPage((prevPage) => Math.min(lastPageIndex, prevPage + 1));
    }
  };

  const renderPageNumbers = () => {
    const pagesLength = Math.max(
      data?.length > itemsPerPage ? Math.ceil(data?.length / itemsPerPage) : 1
    );
    const pageNumber = [];
    for (let i = 1; i <= pagesLength; i++) {
      pageNumber.push(i);
    }
    setPageNumbers(pageNumber);
    setRenderNumbers([pageNumber[0], pageNumber[1]]);
    setRightNumbers([
      pageNumber[pageNumber?.length - 2],
      pageNumber[pageNumber?.length - 1],
    ]);
  };

  const handleLastPages = (page) => {
    setCurrentPage(page);
    if (page === pageNumbers[pageNumbers.length - 1]) {
      setRenderNumbers([page - 3, page - 2]);
    } else {
      setRenderNumbers([page - 2, page - 1]);
    }
  };

  useEffect(() => {
    if (renderNumbers[1] === currentPage && currentPage + 1 < rightNumbers[0]) {
      setRenderNumbers([currentPage, currentPage + 1]);
    } else {
      if (currentPage > 1 && currentPage < rightNumbers[0])
        setRenderNumbers([currentPage - 1, currentPage]);
    }
  }, [isPageClick]);

  useEffect(() => {
    renderPageNumbers();
  }, [data]);

  useEffect(() => {
    if (renderNumbers[1] === currentPage && currentPage + 1 < rightNumbers[0]) {
      setRenderNumbers([currentPage, currentPage + 1]);
    } else {
      if (currentPage > 1 && currentPage < rightNumbers[0])
        setRenderNumbers([currentPage - 1, currentPage]);
    }
  }, [isPageClick]);

  return (
    <>
      <div className="table-container-chart">
        <table className="table-container">
          <tbody>
            <tr className="t-hd header-container-border">
              <th>
                <div className="header-container server-name">
                  <span className="">Server name</span>{" "}
                  <span className="border-table"></span>
                </div>
              </th>
              <th className="">
                <div className="header-container server-storage">
                  <span>Total Storage Size(MB)</span>{" "}
                  <span className="border-table"></span>
                </div>
              </th>
              <th className="">
                <div className="header-container">
                  <span className="storage-archived-heading">
                    % of Total Storage being archived
                  </span>{" "}
                  <span className="border-table"></span>
                </div>
              </th>
              <th className="cost-heading">Total Storage Cost Savings</th>
            </tr>
            {renderData()?.length === 0 ? (
              <tr style={{ height: "183px" }}>
                <td colSpan="8">
                  <div className="no-atabase-selected-container">
                    <div className="no-atabase-selected-container-text">
                      <div>No server found.</div>{" "}
                    </div>
                  </div>
                </td>
              </tr>
            ) : (
              renderData()?.map((server, index) => {
                return (
                  <tr className="table-rows" key={index}>
                    <td style={{ paddingLeft: "16px" }}>
                      <div
                        className="table-rows-link"
                        onClick={() =>
                          navigate("/server", {
                            state: {
                              server_id: server?.server_id,
                              server_name: server?.server_name,
                            },
                          })
                        }
                      >
                        {server?.server_name}
                      </div>
                    </td>
                    <td className="table-rows-color">
                      <span>{server?.total_storage_size.toFixed(2)}</span>
                    </td>
                    <td>
                      <div className="progress-percentage">
                        <ProgressBar
                          width={"196px"}
                          height={"16px"}
                          key={index}
                          bgcolor={colors[index]}
                          completed={server?.total_storage_archived}
                        />
                        <span>
                          {server?.total_storage_archived.toFixed(2)}%
                        </span>
                      </div>
                    </td>
                    <td className="table-rows-color">
                      ${server?.total_cost_saving.toFixed(2)}
                    </td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>

        <div className="progress-table-server-lengh">
          <span className="server-length">{data?.length ?? 0} Servers</span>
        </div>
        <div className="archiving-table-footer">
          <button
            className={`archiving-table-footer-button ${
              currentPage > 1 ? "active" : ""
            }`}
            onClick={handlePrevious}
            disabled={currentPage === 1}
          >
            {" "}
            <img
              className="btns-arrow"
              alt=">"
              src={currentPage > 1 ? ArrowleftVisible : ArrowLeft}
            />
            <span>Previous</span>{" "}
          </button>
          <div className="pagination-table-dashboard">
            {pageNumbers.length < 4 ? (
              renderNumbers?.map((pageNumber, index) => (
                <span
                  key={index}
                  className={`pagination-number ${
                    pageNumber === currentPage ? "pagination-number-active" : ""
                  }`}
                  onClick={() => setCurrentPage(pageNumber)}
                >
                  {pageNumber}
                </span>
              ))
            ) : (
              <>
                {" "}
                {renderNumbers?.map((pageNumber, index) => (
                  <span
                    key={index}
                    className={`pagination-number ${
                      pageNumber === currentPage
                        ? "pagination-number-active"
                        : ""
                    }`}
                    onClick={() => {
                      setCurrentPage(pageNumber);
                      setIsPageClick(!isPageClick);
                    }}
                  >
                    {pageNumber}
                  </span>
                ))}
                <span>...</span>
                {rightNumbers?.map((pageNumber, index) => (
                  <span
                    key={index}
                    className={`pagination-number ${
                      pageNumber === currentPage
                        ? "pagination-number-active"
                        : ""
                    }`}
                    onClick={() => handleLastPages(pageNumber)}
                  >
                    {pageNumber}
                  </span>
                ))}
              </>
            )}
          </div>
          <button
            className={`archiving-table-footer-button ${
              data?.length > 3
                ? currentPage !== lastPageIndex
                  ? "active"
                  : ""
                : ""
            }`}
            onClick={handleNext}
            disabled={currentPage === lastPageIndex}
          >
            {" "}
            <span>Next</span>{" "}
            <img
              className="btns-arrow"
              alt=">"
              src={
                data?.length > 3
                  ? currentPage !== lastPageIndex
                    ? ArrowRightVisible
                    : ArrowRight
                  : ArrowRight
              }
            />{" "}
          </button>
        </div>
      </div>
    </>
  );
};

export default DasboardTable;
