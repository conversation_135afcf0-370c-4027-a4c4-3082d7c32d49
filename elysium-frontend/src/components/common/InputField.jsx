import React from 'react';
import PropTypes from 'prop-types';

const LabeledInput = ({ label, placeholder, value, onChange, name, disabled }) => {
  return (
    <div className="labeled-input-container">
      <label className="label-common">{label}</label>
      <input
        disabled={disabled}
        type="text"
        name={name}
        className="input-field-common"
        placeholder={placeholder}
        value={value}
        onChange={onChange}
      />
    </div>
  );
};

LabeledInput.propTypes = {
  label: PropTypes.string.isRequired,
  placeholder: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onChange: PropTypes.func,
};

export default LabeledInput;
