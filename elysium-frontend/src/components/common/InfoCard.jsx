import React from "react";
import Image from "react-bootstrap/Image";
import "./style.css";

const InfoCard = ({width, content, count, logo, fontSize }) => {
  return (
    <div className="info-cards-data " style={{width:width, '--bs-card-spacer-y': 'unset', '--bs-card-spacer-x':'unseet'}}>
      <div className="info-card-body-data">
        <div className="info-content-data">
          <div className="info-description-data">{content}</div>
          <div style={{fontSize:fontSize}} className="info-heading-data">{count}</div>
        </div>
        <div className="info-logo">
          <Image src={logo} alt="Icon" fluid />
        </div>
      </div>
    </div>
  );
};

export default InfoCard;
