const ProgressBar = (props) => {
  const { bgcolor, completed, height } = props;

  const containerStyles = {
    minWidth: "196px",
    width:"75%",
    height: props?.height ? height : "32px",
    backgroundColor: "#DADDE5",
    display: "flex",
    alignItem: "center",
  };

  const fillerStyles = {
    height: "100%",
    width: `${completed}%`,
    backgroundColor: bgcolor,
    borderRadius: "inherit",
    textAlign: "right",
  };

  const labelStyles = {
    padding: 5,
    color: "white",
    fontWeight: "bold",
  };

  return (
    <div style={containerStyles}>
      <div style={fillerStyles}>
        <span style={labelStyles}></span>
      </div>
    </div>
  );
};

export default ProgressBar;
