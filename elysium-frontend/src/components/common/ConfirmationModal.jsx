import Modal from "react-bootstrap/Modal";
import "./style.css";
import WarningTriangle from "./../../assets/images/alert-triangle.svg";

function ConfirmationModal(props) {
  return (
    <div>
      <Modal
        show={props.show} // Add show prop
        onHide={props.onHide}
        size="sm"
        aria-labelledby="contained-modal-title-vcenter"
        centered
        dialogClassName="warning-schedule-modal-size"
      >
        <div className="delete-confirm-modal-container">
          <div className="warning-image-text-container">
            {props?.isWarningImage && (
              <div>
                <img
                  style={{ height: "24px", width: "24px" }}
                  src={WarningTriangle}
                  alt="no icon"
                />
              </div>
            )}

            <div>
              <div className="delete-confirm-modal-heading">
                {props?.heading}
              </div>
              <div className="delete-confirm-modal-text">
                {" "}
                {props?.warningText}
              </div>
            </div>
          </div>
          <div className="delete-confirm-modal-btn-container">
            <button
              className="delete-confirm-modal-cencel-btn"
              onClick={props?.onHide}
            >
              Cancel
            </button>
            <button
              className="delete-confirm-modal-remove-btn"
              onClick={() => props?.onAction(true)}
              style={{ backgroundColor: props?.bgColor ? props?.bgColor : "" }}
            >
              {props?.text}
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
}

export default ConfirmationModal ;
