import React, { useState, useEffect } from "react";
import { InputGroup } from "react-bootstrap";
import "./style.css";
import ArrowLeft from "./../../assets/images/chevron--sort--up.svg";
import ArrowRight from "./../../assets/images/chevron--sort--up (1).svg";
import CalendarImage from "./../../assets/images/calendar--heat-map.svg";
import {
  formatDateString,
  formatRange,
  leadingData,
  weekDays,
} from "../../constants/calender";
import { Calendar } from "react-multi-date-picker";
import DateObject from "date-object";
 /* eslint-disable react-hooks/exhaustive-deps */


const DateRangePicker = ({ handleDateRangeChange, handleFilterLabel }) => {
  const [showBox, setShowBox] = useState(false);
  const [selectedItem, setSelectedItem] = useState(2);
  const [values, setValues] = useState(getInitialDates() || new DateObject());
  const [calendarLabel, setCalenderLabel] = useState();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const [maxDisplayedCalendars, setMaxDisplayedCalender] = useState(2);
  const [isSelected, setIsSelected] = useState(false);
  const [dates, setDates] = useState({
    startDate: leadingData[selectedItem]?.start_date,
    endDate: leadingData[selectedItem]?.end_date,
  });

  // Function to get initial dates based on the selected item
  function getInitialDates() {
    const startDateString = leadingData[selectedItem].start_date;
    const endDateString = leadingData[selectedItem].end_date;
    const startDate = formatDateString(startDateString);
    const endDate = formatDateString(endDateString);
    return [startDate, endDate];
  }

    // Handle click to show/hide the date range box
const handleClick = () => {
    setShowBox(!showBox);
  };

  // Handle arrow click to navigate to previous or next date range
  const handleDateRangeLabel = (direction) => {
    if (direction === "prev" && selectedItem > 0) {
       // Update the selected item and trigger date range change
      handleDateRangeChange({
        startDate: leadingData[selectedItem - 1]?.start_date,
        endDate: leadingData[selectedItem - 1]?.end_date,
      });
       // Update filter label if provided
      if (handleFilterLabel) {
        handleFilterLabel(leadingData[selectedItem - 1]?.label);
      }
      setSelectedItem(selectedItem - 1);
    } else if (direction === "next" && selectedItem < leadingData.length - 1) {
      // Update the selected item and trigger date range change
      handleDateRangeChange({
        startDate: leadingData[selectedItem + 1]?.start_date,
        endDate: leadingData[selectedItem + 1]?.end_date,
      });
      if (handleFilterLabel) {
        handleFilterLabel(leadingData[selectedItem + 1]?.label);
      }
      setSelectedItem(selectedItem + 1);
    }
  };

    // Handle item click to select a specific date range from the list
  const handleRangeSelect = (index, listItem) => {
    setIsSelected(!isSelected);
    setSelectedItem(index);
  };

  // Handle date range selection in the calendar
  const handleRange = (value) => {
    let start = null;
    let end = null;

    if (value[0]) {
      start = formatRange(value[0]);
    }

    if (value[1]) {
      end = formatRange(value[1]);
    }

    if (start === end) {
      setCalenderLabel(start);
    }
    const matchingIndex = leadingData.findIndex(
      (item) => item?.start_date === start && item?.end_date === end
    );

    // Update the selected item if there's a match
    if (matchingIndex !== -1) {
      setSelectedItem(matchingIndex);
    }
    setDates({
      startDate: start,
      endDate: end,
    });
  };

   // Handle apply button click to confirm the selected date range
  const handleApply = () => {
    if (dates?.startDate && dates?.endDate) {
      handleDateRangeChange(dates);
      setShowBox(false);
    }
    if (handleFilterLabel) {
      handleFilterLabel(calendarLabel);
    }
  };

  // Update state when selected item or 'i' changes
  useEffect(() => {
    setValues(getInitialDates());
    setDates({
      startDate: leadingData[selectedItem]?.start_date,
      endDate: leadingData[selectedItem]?.end_date,
    });
    setCalenderLabel(leadingData[selectedItem]?.label);
  }, [selectedItem, isSelected]);

  // Trigger date range change when component mounts
  useEffect(() => {
    if (handleDateRangeChange) {
      handleDateRangeChange({
        startDate: leadingData[selectedItem]?.start_date,
        endDate: leadingData[selectedItem]?.end_date,
      });
      if (handleFilterLabel) {
        handleFilterLabel(calendarLabel);
      }
    }
  }, []);

  const handleResize = () => {
    setWindowWidth(window.innerWidth);
  };

    // Handle window resize to adjust the number of displayed calendars
  useEffect(() => {
    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);
  
  useEffect(() => {
    if (windowWidth <= 670) {
      setMaxDisplayedCalender(1);
    } else {
      setMaxDisplayedCalender(2);
    }
  }, [windowWidth]);

  return (
    <div className="calendar-container">
      <InputGroup className="myInputGroupStyle-calendar">
        <InputGroup.Text onClick={handleClick}>
          <img alt="" src={CalendarImage} />
        </InputGroup.Text>
        <InputGroup.Text onClick={handleClick}>
          <span className="my-input-group-calendar">{calendarLabel}</span>
        </InputGroup.Text>
        <InputGroup.Text onClick={() => handleDateRangeLabel("prev")}>
          <img alt="" src={ArrowLeft} />
        </InputGroup.Text>
        <InputGroup.Text onClick={() => handleDateRangeLabel("next")}>
          <img alt="" src={ArrowRight} />
        </InputGroup.Text>
      </InputGroup>

      {showBox && (
        <div className="custom-box clander-box-layout">
          <div className="leading-content">
            {leadingData.map((listItem, index) => (
              <button
                key={index}
                onClick={() => handleRangeSelect(index)}
                className={`date-picker-button ${
                  dates.startDate === listItem?.start_date &&
                  dates.endDate === listItem?.end_date
                    ? "selected"
                    : ""
                }`}
              >
                {listItem.label}
              </button>
            ))}
          </div>
          <div className="calender-content">
            <div className="calender-date-container">
              <Calendar
                value={values}
                onChange={(value) => handleRange(value)}
                format={"YYYY/MM/DD"}
                range
                numberOfMonths={maxDisplayedCalendars}
                showOtherDays
                disableYearPicker={true}
                weekStart={1}
                disableMonthPicker={true}
                monthYearSeparator={" "}
                weekStartDayIndex={1}
                weekDays={weekDays}
              />
            </div>
            <div className="calender-footer">
              <div className="date-range-container">
                <div className="selected-date">{dates?.startDate}</div>
                <div className="hyphen-text-between-date-range">–</div>
                <div className="selected-date">
                  {dates.endDate ? dates.endDate : "-- --, ----"}
                </div>
              </div>
              <div className="footer-btns">
                <button
                  className="cancel-btn"
                  onClick={() => setShowBox(!showBox)}
                >
                  Cancel
                </button>
                <button className="apply-btn" onClick={() => handleApply()}>
                  Apply
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DateRangePicker;
