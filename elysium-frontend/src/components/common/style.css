.info-cards-data {
  background: var(--dark-light-dark, #000117);
  border-right: var(--dark-light-dark, #000117);
  border-radius: 0;
  padding: 16px 24px;
  min-width: 250px;
  max-width: 100%;
}

.info-card-body-data {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2px;
  flex: 1 0 0;
}

.info-content-data {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.info-description-data {
  color: var(--Color-Theme-Background, #f9f7fb);
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 171.429% */
  text-transform: capitalize;
  margin-bottom: 4px;
}

.info-heading-data {
  color: var(--color-theme-background, #f9f7fb);
  font-family: Ubuntu;
  font-size: 24px;
  font-style: normal;
  font-weight: 500;
  line-height: 32px;
  letter-spacing: 0.48px;
}

.info-logo {
  flex: 0 0 auto;
}

/* .table-pro-container {
  width: "84%" !important;
  display: "flex" !important;
  gap: "8px";
  justify-content: "end" !important;
  align-items: "center";
  color: "var(--Text-On-White-High-Contrast, #000117)";
  font-family: "Ubuntu";
  font-size: "12px";
  font-style: "normal";
  font-weight: 400;
  line-height: "20px";
  letter-spacing: "0.24px";
} */
.my-input-group-calendar {
  color: var(--text-on-white-high-contrast, #000117) !important;
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  cursor: pointer;
}

.input-group-text-calendar {
  /* Base styles for input-group-text */
  border: 1px solid var(--shades-desaturate-10, #dadde5) !important;
  background: var(--dark-light-light, #fff) !important;
}

.input-group-text-calendar img {
  /* Styles for images inside input-group-text */
  margin: 0;
}

.input-group-text-calendar:nth-child(2) {
  width: 147px;
  border-left: 0;
  margin-left: -12px;
}

.input-group-text-calendar:last-child {
  margin-right: 0 !important;
}

.calendar-container {
  position: relative;
}

.myInputGroupStyle-calendar {
  position: relative;
}

.custom-box {
  min-width: 848px;
  opacity: 1;
  position: absolute;
  top: 41px;
  left: -60%;
  transform: translateX(-50%);
  z-index: 9999;
}

.clander-box-layout {
  display: flex;
  border-radius: 8px;
  border: 1px solid var(--Shades-Desaturate-10, #dadde5);
  background: var(--Base-White, #fff);
  box-shadow: 0px 8px 8px -4px rgba(16, 24, 40, 0.03),
    0px 20px 24px -4px rgba(16, 24, 40, 0.08);
  min-height: 464px;
}

.leading-content {
  width: 192px;
  display: flex;
  padding: 12px 16px;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  align-self: stretch;
  border-right: 1px solid var(--Shades-Desaturate-10, #dadde5);
}

.calender-content {
  width: 100%;
  box-shadow: 0;
  border: none;
}

.date-picker-button {
  display: flex;
  width: 160px;
  padding: 10px 16px;
  align-items: center;
  border-radius: 6px;
  background: var(--Base-White, #fff);
  outline: none;
  border: none;
  color: var(--Text-On-White-High-Contrast, #000117);
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}
.date-picker-button.selected {
  background: var(--Shades-Desaturate-11, #f6f8fc);
}

.calender-footer {
  width: 100% !important;
  height: 72px;
  display: flex;
  padding: 16px;
  justify-content: space-between;
  align-items: flex-start;
  /* align-self: stretch; */
  border-top: 1px solid var(--Shades-Desaturate-10, #dadde5);
}

.selected-date {
  width: 136px;
  display: flex;
  padding: 10px 14px;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  border-radius: 8px;
  border: 1px solid var(--Shades-Desaturate-10, #dadde5);
  background: var(--Base-White, #fff);

  /* Shadow/xs */
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
  color: var(--Text-On-White-High-Contrast, #000117);

  /* Paragraph/S/Regular */
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 142.857% */
}

.date-range-container {
  display: flex;
  gap: 12px;
  align-items: center;
}
.hyphen-text-between-date-range {
  color: var(--Gray-500, #667085);
  font-family: Inter;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
}
.footer-btns {
  display: flex;
  gap: 8px;
}

.cancel-btn {
  display: flex;
  height: 40px;
  padding: 0px 16px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  border: 1px solid var(--Shades-Desaturate-10, #dadde5);
  background: var(--dark-light-light, #fff);

  /* Shadow/XS */
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
  border: none;
  outline: none;
  color: var(--Text-On-White-High-Contrast, #000117);

  /* Button/S/Regular */
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px; /* 114.286% */
  letter-spacing: 0.28px;
}

.apply-btn {
  display: flex;
  height: 40px;
  padding: 0px 16px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  background: var(--dark-light-dark, #000117);
  border: none;
  outline: none;
  color: var(--dark-light-light, #fff);

  /* Button/S/Regular */
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px; /* 114.286% */
  letter-spacing: 0.28px;
}

.calender-date-container {
  height: 84%;
  display: flex;
  padding: 20px 24px;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  align-self: stretch;
}
.rmdp-range {
  background-color: #f6f8fc !important;
  box-shadow: none !important;
  color: var(--Text-On-White-High-Contrast, #000117) !important;
  text-align: center;

  /* Paragraph/S/Regular */
  font-family: Ubuntu;
  font-size: 14px !important;
  font-style: normal;
  font-weight: 400 !important;
  line-height: 20px !important; /* 142.857% */
}
.rmdp-day span {
  color: var(--Text-On-White-High-Contrast, #000117) !important;
  text-align: center;

  /* Paragraph/S/Regular */
  font-family: Ubuntu !important;
  font-size: 14px !important;
  font-style: normal;
  font-weight: 400 !important;
  line-height: 20px !important; /* 142.857% */
}
.rmdp-day.rmdp-today span {
  border: 1px solid var(--dark-light-dark, #000117) !important;
  background: var(--Gray-100, #f2f4f7) !important;
}
.rmdp-week-day {
  color: var(--Text-On-White-High-Contrast, #000117) !important;
  text-align: center !important;

  /* Paragraph/S/Regular */
  font-family: Ubuntu !important;
  font-size: 14px !important;
  font-style: normal !important;
  font-weight: 400 !important;
  line-height: 20px !important;
}
.rmdp-week {
  width: 264px !important;
}
.rmdp-shadow {
  box-shadow: none !important;
  width: 100% !important;
}
.rmdp-day,
.rmdp-week-day {
  height: 40px !important;
  min-width: 40px !important;
}
.rmdp-header {
  margin-top: 0px !important;
  margin-bottom: 20px !important;
}
.rmdp-day-picker {
  width: 100%;
  display: flex;
  gap: 48px !important;
}

.rmdp-calendar {
  width: 100%;
}

.rmdp-arrow {
  border: 1px solid #000117 !important;
  border-width: 0 1.5px 1.5px 0 !important;
  height: 10px !important;
  width: 10px !important;
  overflow: hidden !important;
}
.rmdp-arrow-container:hover {
  background-color: transparent !important;
  box-shadow: none !important;
}
.rmdp-header-values {
  color: var(--Text-On-White-High-Contrast, #000117) !important;

  /* Paragraph/S/Regular */
  font-family: Ubuntu !important;
  font-size: 14px !important;
  font-style: normal !important;
  font-weight: 400 !important;
  line-height: 20px !important; /* 142.857% */
}
.rmdp-day.rmdp-deactive,
.rmdp-day.rmdp-disabled {
  color: var(--Text-On-White-Medium-Contrast, #5c5d6a) !important;
  text-align: center;

  /* Paragraph/S/Regular */
  font-family: Ubuntu !important;
  font-size: 14px !important;
  font-style: normal !important;
  font-weight: 400 !important;
  line-height: 20px !important; /* 142.857% */
}
.rmdp-week,
.rmdp-ym {
  margin-bottom: 4px !important;
}
.rmdp-day.rmdp-deactive span {
  color: var(--Text-On-White-Medium-Contrast, #5c5d6a) !important;
  text-align: center;

  /* Paragraph/S/Regular */
  font-family: Ubuntu !important;
  font-size: 14px !important;
  font-style: normal !important;
  font-weight: 400 !important;
  line-height: 20px !important; /* 142.857% */
}
.rmdp-day:not(.rmdp-disabled, .rmdp-day-hidden) span:hover {
  background-color: #f9fafb !important;
}

.rmdp-day.rmdp-range.start span {
  background-color: #000117 !important;
  color: #fff !important;
}

.rmdp-day.rmdp-range.end span {
  background-color: #000117 !important;
  color: #fff !important;
}
.rmdp-header-values span {
  padding: 0 0 0 1px !important;
}

.table-progress-container {
  width: 75%;
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  align-items: center;
  color: #000117;
  font-family: "Ubuntu", sans-serif;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0.24px;
}

.btns-arrow {
  height: "12px";
  width: "12px";
}

@media (max-width: 930px) {
  .clander-box-layout {
    display: block;
    /* flex-wrap: wrap; */
  }
  .custom-box {
    min-width: 540px;
    max-width: 650;
    left: -24%;
  }
  .leading-content {
    width: 100% !important;
    flex-direction: row;
    gap: 8px;
    flex-wrap: wrap;
    justify-content: space-between;
    border-right: none;
    border-bottom: 1px solid var(--Shades-Desaturate-10, #dadde5);
  }
}
@media (max-width: 670px) {
  .rmdp-week {
    width: 100% !important;
    min-width: 400px;
  }
  .custom-box {
    min-width: 500px;
    max-width: 600;
    left: -2%;
  }
}

@media (max-width: 420px) {
  .custom-box {
    min-width: 200px;
    max-width: 380px;
    left: 50%;
  }
  .rmdp-week {
    width: 100% !important;
    min-width: 200px;
  }
  .calender-footer {
    height: 144px !important;
    flex-wrap: wrap;
  }
  .date-picker-button {
    width: 150px;
  }
  .cancel-btn {
    width: 100px;
  }
  .apply-btn {
    width: 130px;
  }
  .footer-btns {
    justify-content: space-between;
    width: 95%;
  }
}

.delete-confirm-modal-container {
  display: flex;
  width: 300px;
  padding: 16px;
  flex-direction: column;
  align-items: flex-start;
  gap: 24px;
  border-radius: 8px;
  border: 1px solid var(--Shades-Desaturate-10, #dadde5);
  background: var(--dark-light-light, #fff);

  /* Shadow/S */
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.1),
    0px 1px 2px 0px rgba(0, 0, 0, 0.06);
  height: 144px;
}

.delete-confirm-modal-heading {
  color: var(--Text-On-White-High-Contrast, #000117);

  /* Paragraph/L/Bold */
  font-family: Ubuntu;
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
  line-height: 28px; /* 155.556% */
}
.delete-confirm-modal-text {
  color: var(--Text-On-White-Medium-Contrast, #5c5d6a);

  /* Caption/Regular */
  font-family: Ubuntu;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 166.667% */
  letter-spacing: 0.24px;
}

.delete-confirm-modal-cencel-btn {
  display: flex;
  height: 40px;
  padding: 0px 16px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex: 1 0 0;
  border-radius: 8px;
  border: 1px solid var(--Shades-Desaturate-10, #dadde5);
  background: var(--dark-light-light, #fff);

  /* Shadow/XS */
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
  color: var(--Text-On-White-High-Contrast, #000117);
  text-align: center;

  /* Button/S/Regular */
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px; /* 114.286% */
  letter-spacing: 0.28px;
}

.delete-confirm-modal-remove-btn {
  display: flex;
  height: 40px;
  padding: 0px 16px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex: 1 0 0;
  border-radius: 8px;
  background: var(--States-Negative, #ef0c0c);
  color: var(--dark-light-light, #fff);
  text-align: center;

  /* Button/S/Regular */
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px; /* 114.286% */
  letter-spacing: 0.28px;
  outline: none;
  border: none;
}
.delete-confirm-modal-btn-container {
  width: 100%;
  display: flex;
  gap: 8px;
}

.warning-image-text-container {
  display: flex;
  gap: 5px;
}

/* LabeledInput.css */

.labeled-input-container {
  display: flex;

  align-items: center;
  width: 100%;
  justify-content: space-between;
  gap: 8px;
}

.label-common {
  color: var(--Text-On-White-Medium-Contrast, #5c5d6a);

  /* Paragraph/S/Medium */
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px; /* 142.857% */
  width: 104px;
}

.input-field-common {
  display: flex;
  height: 40px;
  align-items: center;
  flex: 1 0 0;
  border-radius: 8px;
  border: 1px solid var(--Shades-Desaturate-10, #dadde5);
  background: var(--Shades-Desaturate-11, #f6f8fc);
  padding: 8px;
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
}
.input-field-common:focus {
  border-color: #474950;
  outline-color: #474950;
}

.warning-schedule-modal-size {
  min-width: 200px !important;
}
@media (max-width: 580px) {
  .warning-schedule-modal-size {
    max-width: 200px !important;
    margin-top: -150px !important;
    margin-left: 50px !important;
  }
}

.toast-message-undo {
  position: fixed;
  bottom: 3%;
  left: 3%;
  background-color: #fff;
  padding: 10px;
  border-radius: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-width: 300px; /* Adjust the width as needed */
  transition: opacity 0.5s ease-in-out;
  opacity: 0;
  border: 1px solid  #c6ccdc;
  border-radius: 8px;
  padding: 8px;
  font-family: Ubuntu;
  font-size: 16px;
  font-style: normal;
  color: #000117;
}

.toast-message-undo.visible {
  opacity: 1;
}

.toast-content {
  margin-right: 10px;
}

.undo-button {
  background-color: #fff;
  color: #174be6;
  border: none;
  padding: 5px 10px;
  cursor: pointer;
}
