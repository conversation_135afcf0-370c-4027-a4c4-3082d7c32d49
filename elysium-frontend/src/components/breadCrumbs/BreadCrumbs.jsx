import React from 'react';
import { Link, useLocation } from "react-router-dom";

const BreadCrumbs = () => {
  const location = useLocation();
  const { pathname } = location;
  const segments = pathname.split('/').filter((segment) => segment !== '');

  let url = '';
  const breadcrumbLinks = segments.map((segment, i) => {
    url += `/${segment}`;
    const isLastSegment = i === segments.length - 1;
    return (
      <span key={i}>
        {i === 0 ? '' : ' / '}
        {isLastSegment ? (
          decodeURIComponent(segment)
        ) : (
          <Link to={`${url}`}>
            {decodeURIComponent(segment)}
          </Link>
        )

        }
      </span>
    );
  });

  return (
    <div>
      {breadcrumbLinks}
    </div>
  );
}

export default BreadCrumbs;