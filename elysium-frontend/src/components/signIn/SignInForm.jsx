import React, { useState } from "react";
import { <PERSON>, Button } from "react-bootstrap";
import { <PERSON> } from "react-router-dom";
import { useFormik } from "formik";
import { signInSchema } from "../../utils/signinValidation";
import { handleCookies } from "../../utils/cookies";
import { serviceCall } from "../../services/signIn";
import { useNavigate } from "react-router-dom";
import Loader from "../loader/Loader";

function SignInForm() {
  const navigate = useNavigate();
  const [open, setOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  const initialValues = {
    email: "",
    password: "",
    rememberMe: false,
  };

  const { values, handleBlur, handleChange, handleSubmit, errors, touched } =
    useFormik({
      initialValues,
      validationSchema: signInSchema,
      validateOnChange: true,
      validateOnBlur: false,
      onSubmit: async (values, action) => {
        let payload = {
          email: values.email,
          password: values.password,
        };
        setOpen(true);
        const response = await serviceCall.signIn(payload);
        if (response) {
          setOpen(false);
          if (response.status === "Success") {
            handleCookies.setCookies(response.data.token);
            navigate("/", { state: { navigationFlag: true } });
          } else {
            setErrorMessage(response.message || "Invalid email or password");
            action.resetForm();
          }
        }
      },
    });

  return (
    <div>
      <Loader open={open} />
      <Form onSubmit={handleSubmit} className="signin-form-container">
        <h6 className="signin-title"> Sign In to Elysium</h6>

        <Form.Group>
          <div className="signin-field-container">
            <Form.Control
              type="email"
              autoComplete="off"
              name="email"
              id="email"
              placeholder="Email"
              value={values.email}
              onChange={handleChange}
              onBlur={handleBlur}
            />
            {errors.email && touched.email && (
              <div className="signin-field-error">{errors.email}</div>
            )}
          </div>
          <div className="signin-field-container">
            <Form.Control
              type="password"
              autoComplete="off"
              name="password"
              id="password"
              placeholder="Password"
              value={values.password}
              onChange={handleChange}
              onBlur={handleBlur}
            />
            {errors.password && touched.password && (
              <div className="signin-field-error">{errors.password}</div>
            )}
          </div>
        </Form.Group>
        {errorMessage && (
          <div className="signin-field-error">{errorMessage}</div>
        )}
        <Form.Group className="remember-me-sec" controlId="formBasicCheckbox">
          <Form.Check
            type="checkbox"
            label="Stay logged in"
            id="rememberMe"
            name="rememberMe"
            checked={values.rememberMe}
            onChange={handleChange}
          />
          <Link to="/forgotpassword" className="have-account-link">
            <p className="forgot-pwd-btn">Forgot password ?</p>
          </Link>
        </Form.Group>
        <Button variant="primary" className="signin-btn" type="submit">
          Sign In
        </Button>
      </Form>
    </div>
  );
}

export default SignInForm;
