.server-dashboard-container {
  display: flex;
  padding: 48px 60px !important;
  flex-direction: column;
  align-items: flex-start;
  margin: auto;
  gap: 32px;
  align-self: stretch;
  min-height: 89vh;
  background: var(--shades-desaturate-11, #f6f8fc) ;
}
.server-table-container {
  width: 100%;
  display: flex ;
  gap: 24px;
  height: 743px ;
}
.dashboard-heading-databases {
  padding-left: 6px;
  color: var(--gray-900, #101828);
  align-self: stretch;
  font-family: Ubuntu;
  font-size: 32px;
  font-style: normal;
  font-weight: 700;
  line-height: 40px;
  letter-spacing: -0.64px;
}
.archived-heading {
  color: var(--text-on-white-high-contrast, #000117);
  flex: 1 0 0;
  font-family: Inter ;
  font-size: 20px ;
  font-style: normal ;
  font-weight: 600 ;
  line-height: 28px ;
}
.archived-header-activity-container {
  display: flex;
  gap: 8px;
}
.archived-header {
  display: flex;
  justify-content: space-between;
  width: 100%;
}
.chart-card-dashboard-container {
  width: 80% ;
}

.d-activity-container {
  width: 20% ;
}

.calender-container {
  border: 1px solid var(--shades-desaturate-10, #dadde5);
  background: var(--dark-light-light, #fff);
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.1),
    0px 1px 2px 0px rgba(0, 0, 0, 0.06);
}
.my-input-group {
  color: var(--text-on-white-high-contrast, #000117) ;
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}

.input-group-text {
  border: 1px solid var(--shades-desaturate-10, #dadde5) ;
  background: var(--dark-light-light, #fff) ;
}

.input-group-text img {
  margin: 0;
}

.input-group-text:nth-child(2) {
  width: 147px ;
  border-left: 0 ;
  margin-left: -12px ;
}

.input-group-text:last-child {
  margin-right: 0 ;
}

.row-custom {
  --bs-gutter-x: 0 ;
  --bs-gutter-y: 0 ;
  display: flex;
  flex-wrap: wrap;
  margin-top: calc(-1 * var(--bs-gutter-y));
  margin-right: 0 ;
  margin-left: 0 ;
  padding: 0;
}
.custom-md-2-5 {
  width: 20%;
}

/* .activity-width {
  width: 23% ;
} */
.server-bar-width {
  width: 100% ;
}
.active-link-crumb {
  color: var(--black, #383838);
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  cursor: pointer;
}
@media (max-width: 550px) {
  .archived-header {
    display: block;
  }
}
@media (max-width: 768px) {
  .server-dashboard-container {
    height: auto ;
    padding: 2% !important;
  }
  .chart-card-dashboard-container {
    width: 100% ;
  }
  .dashboard-container {
    flex-wrap: wrap;
    height: auto;
  }
  .d-activity-container {
    width: 100% ;
  }
  .chart-width {
    width: 100% ;
  }
  .activity-width {
    width: 100% ;
    margin-top: 20px ;
  }

  .input-group-text:nth-child(2) {
    width: 130px ;
  }
}

.mobile-head {
  width: 786px;
}

.server-bar-container {
  display: flex;
  width: 100%;
  overflow: hidden;
  padding-bottom: 0px;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  margin-top: 12px;

  border-radius: 8px;
  border: 1px solid var(--shades-desaturate-10, #dadde5);
  background: var(--dark-light-light, #fff);
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.1),
    0px 1px 2px 0px rgba(0, 0, 0, 0.06);
}

.cards-container {
  display: flex;
  gap: 1px;
  width: 100%;
}

@media (max-width: 768px) {
  .cards-container {
    flex-direction: column;
    gap: 2px;
  }
}
.stack-border {
  width: 100%;
  overflow: hidden;
  height: 1px;
  background: var(--shades-desaturate-10, #dadde5);
  margin-top: 26px ;
  margin-bottom: 44px;
}

.stack-bar-container {
  height: 50%;
  margin-top: 20px ;
  margin-bottom: 20px;
}

@media (max-width: 1150px) {
  .pie-chart-container {
    flex-wrap: wrap ;
    justify-content: center ;
    align-items: center ;
    margin: auto;
    gap: 30px;
    padding-left: 0px ;
    padding-right: 0px;
  }

}

@media (max-width: 1120px) {
  .pie-chart-container {
    padding-left: 0px ;
  }

}

.my-custom-gutter-row {
  --bs-gutter-x: 2px ;
}
.my-custom-gutter-row {
  display: flex ;
  gap: 1px ;
  flex-direction: row;
  flex-wrap: wrap;
}
@media (max-width: 768px) {
  .my-custom-gutter-row {
    display: flex ;
    gap: 2px ;
    flex-direction: row;
    flex-wrap: wrap;
  }
}

.server-breadcrumb {
  padding-left: 10px ;
  margin-bottom: 12px;
  display: flex;
 flex-wrap: wrap;
  align-items: center;
  gap: 2px;
}

.breadcrumb-link {
  text-decoration: underline;
  color: rgba(30, 136, 229, 1);
  cursor: pointer;
  font-family: Ubuntu;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0em;
  text-align: left;
}

.archiving-table-footer {
  background-color: rgba(255, 255, 255, 1);
  min-width: 700px ;
  display: flex;
  height: 64px;
  padding: 12px 24px;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  border: 0px solid var(--gray-200, #eaecf0);
  border-top: 2px solid var(--shades-desaturate-10, #dadde5) ;
}
.archiving-table-footer-button {
  display: flex;
  height: 36px;
  padding: 0px 12px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 6px;
  border: 1px solid var(--shades-desaturate-9, #c6ccdc);
  background: var(--dark-light-light, #fff);
  color: var(--shades-desaturate-9, #c6ccdc);
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  letter-spacing: 0.28px;
}
.archiving-table-footer-button.active {
  color: var(--Text-On-White-High-Contrast, #000117);
}
.table-count {
  color: var(--text-on-white-high-contrast, #000117);
  font-family: Ubuntu;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}

.server-table-container {
  width: 100% ;
  border-radius: 8px;
}
.server-table-bg {
  background: var(--dark-light-light, #fff);
  border: 2px solid var(--shades-desaturate-9, #dadde5);
  border-radius: 8px;
  overflow: hidden;
  margin-top: 16px;
  width: 100%;
}
.server-name-table {
  width: 100% ;
  min-width: 300px;
}
.border-table-server {
  border: 1px solid #c6ccdc;
  margin-right: 3px;
  height: 24px ;
}

.server-table-heading {
  display: flex;
  align-items: center;
}
.archiving-table-header {
  display: flex;
  padding: 12px 24px;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 12px;
  align-self: stretch;
  color: var(--gray-900, #101828);
  font-family: Inter;
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 28px;
  border-bottom: 2px solid var(--shades-desaturate-10, #dadde5) ;
}
