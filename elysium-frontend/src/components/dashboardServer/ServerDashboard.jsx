import React, { useState, useEffect } from "react";
import Header from "../../pages/header/Header";
import { Container, Row, Col } from "react-bootstrap";
import "./style.css";
import DateRangePicker from "../common/Calendar";
import ServerBarCard from "./ServerBarCard";
import { Link, useLocation, useNavigate } from "react-router-dom";
import <PERSON>rumbArrow from "./../../assets/images/chevron--right.svg";
import ArchivingTable from "../common/ArchivingTable";
import {
  convertToISODate,
  convertToMonthName,
  formatDateIntoDays,
} from "../../utils/helpers";
import { calculateDaysFlag } from "../../constants/calender";
import { serviceCall } from "../../services/dashboard";
import Loader from "../loader/Loader";
import Notification from "../notificationModal/Notification";

function DashboardServer() {
  const location = useLocation();
  const navigate = useNavigate()
  const serverId = location?.state?.server_id;
  const serverName = location?.state?.server_name;
  const [open, setOpen] = useState(false);
  const [serverArchivedCardData, setServerArchivedCardData] = useState();
  const [filter, setFilter] = useState();
  const [responseMessage, setResponseMessage] = useState();
  const [showNotification, setShowNotification] = useState(false);
  const [isError, setIsError] = useState(false);
  const [serverArchivedBarPanelData, setServerArchivedBarPanelData] = useState({
    data: "",
    label: "",
  });
  const [serverArchivedListingPanelData, setServerArchivedListingPanelData] =
    useState();
   const getArchivedData = async (range) => {
    setOpen(true);
    try {
      const response = await serviceCall.getServerArchivedData(range);
      if (response?.status === "Success") {
        setServerArchivedCardData(
          response?.data?.server_total_archive_stats[0]
        );
      } else {
        setServerArchivedCardData();
        setResponseMessage(
          response?.messages
            ? response?.messages
            : response?.response.data?.message || "Something went wrong"
        );
        setIsError(true);
        setShowNotification(true);
      }
    } catch (error) {
     
    } finally {
      setOpen(false);
    }
  };

  const getServerBarData = async (range) => {
    setOpen(true);
    try {
      const response = await serviceCall.getServerBarData(range);
      if (response?.status === "Success") {
        const dateParts = response?.data?.server_barChart[0]?.dates[0]?.split("-");
        let label;
        if (dateParts?.length === 2) {
          label = await convertToMonthName(response?.data?.server_barChart[0]?.dates);
        } else if (dateParts?.length === 3) {
          label = await formatDateIntoDays(response?.data?.server_barChart[0]?.dates);
        } else if (dateParts?.length === 1) {
          label = response?.data?.server_barChart[0]?.dates;
        }
        setServerArchivedBarPanelData({
          data: response.data?.server_barChart[1],
          label: label,
        });
      } else {
        setServerArchivedBarPanelData({
          data: "",
          label: "",
        });
        setResponseMessage(
          response?.message
            ? response?.message
            : response?.response?.data?.message || "Something went wrong"
        );
        setIsError(true);
        setShowNotification(true);
      }
    } catch (error) {
   
    } finally {
      setOpen(false);
    }
  };
  
  const getTableData = async (range) => {
    setOpen(true);
    try {
      const response = await serviceCall.getServerDashboardListingData(range);
      if (response?.status === "Success") {
        setServerArchivedListingPanelData(response?.data?.server_databases_list);
      } else {
        setServerArchivedListingPanelData();
        setResponseMessage(
          response?.messages
            ? response?.messages
            : response?.response.data?.message || "Something went wrong"
        );
        setIsError(true);
        setShowNotification(true);
      }
    } catch (error) {
    
    } finally {
      setOpen(false);
    }
  };

  const handleDateRangeChange = async (date) => {
    const range = await convertToISODate(date);
    const daysInRange = calculateDaysFlag(
      range?.start_date,
      range?.end_date
    );

    if (range?.start_date && range?.end_date) {
      const payload = {
        start_date: range?.start_date,
        end_date: range?.end_date,
        flag: daysInRange,
        id: serverId,
      };
      getArchivedData(payload);
      getServerBarData(payload);
      getTableData(payload);
    }
  };

  const handleFilterLabel = (label) => {
    setFilter(label);
  };
  const handleToastClose = () => {
    setShowNotification(false);
    setResponseMessage("");
    setIsError(false);
  };

 /* eslint-disable react-hooks/exhaustive-deps */
useEffect(() => {
   
    if (!location.state || !location.state.server_id || !location.state.server_name) {
      navigate("/")
    }
  }, [location]);
  return (
    <>
      <Header />
      <Loader open={open} />
      <Container fluid className="server-dashboard-container">
        <Row>
          <Col xs={12}>
            <div className="server-breadcrumb">
              <Link to="/dashboard" className="breadcrumb-link">
                Dashboard
              </Link>
              <img
                style={{ height: "12px", width: "12px" }}
                alt=">"
                src={CrumbArrow}
              />
              <span className="active-link-crumb">
                {serverName ? serverName : serverArchivedCardData?.server_name}
              </span>
            </div>
          </Col>
          <Col xs={12}>
            <h5 className="dashboard-heading-databases">
              {serverName ? serverName : serverArchivedCardData?.server_name}{" "}
              Statisctics
            </h5>
          </Col>
        </Row>
        <Row style={{ width: "100%" }} className="m-0 p-0">
          <div className="server-bar-width">
            <div className="archived-header">
              <h6 className="archived-heading">Server Archived Data</h6>
              <DateRangePicker handleDateRangeChange={handleDateRangeChange} handleFilterLabel={handleFilterLabel} />
            </div>
            <ServerBarCard
              serverArchivedCardData={serverArchivedCardData}
              serverArchivedBarPanelData={serverArchivedBarPanelData?.data}
              serverLabels={serverArchivedBarPanelData?.label}
              tableData={serverArchivedListingPanelData}
            />
            <ArchivingTable
              heading={"Database Archiving Activities"}
              name={"Database name"}
              totalRows={"Total Archived Tables"}
              totalData={`Total Archive Data (${
                filter ? filter : "This week"
              })`}
              data={serverArchivedListingPanelData}
              serverName={
                serverName ? serverName : serverArchivedCardData?.server_name
              }
            />
          </div>
        </Row>
        {showNotification && (
          <Notification
            responseMessage={responseMessage}
            isError={isError}
            handleToastClose={handleToastClose}
          />
        )}
      </Container>
    </>
  );
}

export default DashboardServer;
