 @import url('https://fonts.googleapis.com/css2?family=Ubuntu:wght@400;500&display=swap');


 body {
   font-family: 'Ubuntu' !important;
   margin: 0px;
 }

 button,
 textarea {
   font-family: 'Ubuntu' !important;
 }

 body h1 {
   font-style: normal;
   font-weight: revert;
   font-size: 32px;
   line-height: 40px;
   color: #000;
 }

 body p {
   font-family: 'Ubuntu';
   font-style: normal;
   font-weight: 400;
   font-size: 16px;
   line-height: 24px;
 }

 .img-holder,
 img {
   width: 100%;
   height: auto;
 }

 code {
   font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
     monospace;
 }

 .row .sidebar-wrapper {
   padding: 0;
   height: 100vh;
 }

 .signin-form-section {
   display: flex;
   flex-direction: row;
   align-items: center;
   padding: 0px;
   gap: 12px;
   position: absolute;
   right: 32px;
   top: 27px;
 }

 .have-account-text {
   font-weight: 400;
   font-size: 14px;
   line-height: 20px;
   letter-spacing: 0.02em;
   color: #000000;
   margin: 0;
 }

 .create-account-btn {
   width: 160px;
   height: 32px;
   background: #FFFFFF;
   border: 1px solid #DADDE6;
   box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);
   border-radius: 6px;
 }

 .have-account-link {
   text-decoration: none;
 }

 input::-webkit-outer-spin-button,
 input::-webkit-inner-spin-button {
   display: none;
 }

 .success-img {
   width: 56px;
 }

 .dark-color {
   color: #000117 !important;
 }

 .green {
   color: #169C00 !important;
 }

 .pointer {
   cursor: pointer;
 }

 .btn:hover {
   background-color: inherit !important;
   color: #000117 !important;
   border: 1px solid #DADDE5 !important;
 }

 input:focus {
   box-shadow: none !important;
   border-color: #DDE2E6 !important;
 }

 .not-found-title {
   font-size: 54px;
   line-height: 72px;
 }
