import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  imageSrc: "",
  userName: "",
  userFullName: "",
  userTrialPeriod: "",
  userInfo: "",
};

export const UserInformation = createSlice({
  name: "profileImage",
  initialState,
  reducers: {
    updateProfileImage: (state, action) => {
      state.imageSrc = action.payload;
    },
    userName: (state, action) => {
      state.userName = action.payload;
    },
    userFullName: (state, action) => {
      state.userFullName = action.payload;
    },
    userTrialPeriod: (state, action) => {
      state.userTrialPeriod = action.payload;
    },
    userInfo: (state, action) => {
      state.userInfo = action.payload;
    },
  },
});

export const {
  updateProfileImage,
  userName,
  userTrialPeriod,
  userFullName,
  userInfo,
} = UserInformation.actions;

export default UserInformation.reducer;
