<svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="data--base">
<path id="Vector" fill-rule="evenodd" clip-rule="evenodd" d="M8.66602 3H24.666C25.1964 3 25.7052 3.21071 26.0802 3.58579C26.4553 3.96086 26.666 4.46957 26.666 5V27C26.666 27.5304 26.4553 28.0391 26.0802 28.4142C25.7052 28.7893 25.1964 29 24.666 29H8.66602C8.13558 29 7.62687 28.7893 7.2518 28.4142C6.87673 28.0391 6.66602 27.5304 6.66602 27V5C6.66602 4.46957 6.87673 3.96086 7.2518 3.58579C7.62687 3.21071 8.13558 3 8.66602 3ZM8.66602 11H24.666V5H8.66602V11ZM8.66602 13V19H24.666V13H8.66602ZM8.66602 21V27H24.666V21H8.66602ZM12.666 8C12.666 8.55228 12.2183 9 11.666 9C11.1137 9 10.666 8.55228 10.666 8C10.666 7.44772 11.1137 7 11.666 7C12.2183 7 12.666 7.44772 12.666 8ZM11.666 17C12.2183 17 12.666 16.5523 12.666 16C12.666 15.4477 12.2183 15 11.666 15C11.1137 15 10.666 15.4477 10.666 16C10.666 16.5523 11.1137 17 11.666 17ZM12.666 24C12.666 24.5523 12.2183 25 11.666 25C11.1137 25 10.666 24.5523 10.666 24C10.666 23.4477 11.1137 23 11.666 23C12.2183 23 12.666 23.4477 12.666 24Z" fill="url(#paint0_linear_826_31861)"/>
</g>
<defs>
<linearGradient id="paint0_linear_826_31861" x1="6.66602" y1="16" x2="26.666" y2="16" gradientUnits="userSpaceOnUse">
<stop stop-color="#6206B8"/>
<stop offset="1" stop-color="#00B6D6"/>
</linearGradient>
</defs>
</svg>
