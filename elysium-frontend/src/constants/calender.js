// Returns an array of predefined date ranges for dashboard filtering
export const leadingData = [
  {
    id: 1,
    label: "Today",
    start_date: formatDate(new Date()),
    end_date: formatDate(new Date()),
  },
  {
    id: 2,
    label: "Yesterday",
    start_date: formatDate(getPreviousDate(1)),
    end_date: formatDate(getPreviousDate(1)),
  },
  {
    id: 3,
    label: "This week",
    start_date: formatDate(getStartOfWeek()),
    end_date: formatDate(new Date()),
  },
  {
    id: 4,
    label: "Last week",
    start_date: formatDate(getStartOfLastWeek()),
    end_date: formatDate(getEndOfLastWeek()),
  },
  {
    id: 5,
    label: "This month",
    start_date: formatDate(getStartOfMonth()),
    end_date: formatDate(new Date()),
  },
  {
    id: 6,
    label: "Last month",
    start_date: formatDate(getStartOfLastMonth()),
    end_date: formatDate(getEndOfLastMonth()),
  },
  {
    id: 7,
    label: "This year",
    start_date: formatDate(getStartOfYear()),
    end_date: formatDate(new Date()),
  },
  {
    id: 8,
    label: "Last year",
    start_date: formatDate(getStartOfLastYear()),
    end_date: formatDate(getEndOfLastYear()),
  },
  {
    id: 9,
    label: "All time",
    start_date: "Jan 1, 1900",
    end_date: formatDate(new Date()),
  },
];

// Represents short names and abbreviations for weekdays
export const weekDays = [
  ["sun", "Su"],
  ["mon", "Mo"],
  ["tue", "Tu"],
  ["wed", "We"],
  ["thu", "Th"],
  ["fri", "Fr"],
  ["sat", "Sa"],
];

// Formats a date object into a string using short month, numeric day, and numeric year
export function formatDate(date) {
  const options = { month: "short", day: "numeric", year: "numeric" };
  return new Intl.DateTimeFormat("en-US", options).format(date);
}

// Returns the date for a specified number of days ago
function getPreviousDate(days) {
  const today = new Date();
  const previousDate = new Date(today);
  previousDate.setDate(today.getDate() - days);
  return previousDate;
}

// Returns the start date of the current week
function getStartOfWeek() {
  const today = new Date();
  const startOfWeek = new Date(today);
  const diff =
    today.getDate() - today.getDay() + (today.getDay() === 0 ? -6 : 1);
  startOfWeek.setDate(diff);
  return startOfWeek;
}

// Returns the start date of the previous week
function getStartOfLastWeek() {
  const today = new Date();
  const startOfLastWeek = new Date(today);
  startOfLastWeek.setDate(today.getDate() - today.getDay() - 6);
  return startOfLastWeek;
}

// Returns the end date of the previous week
function getEndOfLastWeek() {
  const today = new Date();
  const endOfLastWeek = new Date(today);
  endOfLastWeek.setDate(today.getDate() - today.getDay());
  return endOfLastWeek;
}

// Returns the start date of the current month
function getStartOfMonth() {
  const today = new Date();
  const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
  return startOfMonth;
}

// Returns the start date of the previous month
function getStartOfLastMonth() {
  const today = new Date();
  const startOfLastMonth = new Date(
    today.getFullYear(),
    today.getMonth() - 1,
    1
  );
  return startOfLastMonth;
}

// Returns the end date of the previous month
function getEndOfLastMonth() {
  const today = new Date();
  const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
  return endOfLastMonth;
}

function getStartOfYear() {
  const today = new Date();
  const startOfYear = new Date(today.getFullYear(), 0, 1);
  return startOfYear;
}

// Returns the start date of the current year
function getStartOfLastYear() {
  const today = new Date();
  const startOfLastYear = new Date(today.getFullYear() - 1, 0, 1);
  return startOfLastYear;
}

// Returns the end date of the previous year
function getEndOfLastYear() {
  const today = new Date();
  const endOfLastYear = new Date(today.getFullYear() - 1, 11, 31);
  return endOfLastYear;
}

// Formats a date object into a string for displaying in a range
export const formatRange = (dateObject) => {
  if (!dateObject) return null;

  const options = { month: "short", day: "numeric", year: "numeric" };
  const date = new Date(
    dateObject.year,
    dateObject.month.number - 1,
    dateObject.day
  );
  return new Intl.DateTimeFormat("en-US", options).format(date);
};

// Formats an input date string into the "YYYY-MM-DD" format
export function formatDateString(inputDateString) {
  const months = {
    Jan: "01",
    Feb: "02",
    Mar: "03",
    Apr: "04",
    May: "05",
    Jun: "06",
    Jul: "07",
    Aug: "08",
    Sep: "09",
    Oct: "10",
    Nov: "11",
    Dec: "12",
  };

  const [month, day, year] = inputDateString.split(" ");
  const formattedDate = `${year}-${months[month]}-${day
    .slice(0, -1)
    .padStart(2, "0")}`;
  return formattedDate;
}

// Formats a date object into the "YYYY-MM-DD" format
export function formatDateIntoDash(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
}

// Calculates the number of days in a date range and returns a label ('d', 'm', or 'y')
export const calculateDaysFlag = (start_date, end_date) => {
  const MS_IN_DAY = 24 * 60 * 60 * 1000;
if(start_date==="1900-01-01" && end_date===formatDateIntoDash(new Date())){
  return "a"
}
  const startDateObj = new Date(start_date)
  const endDateObj = new Date(end_date)


  if (isNaN(startDateObj) || isNaN(endDateObj)) {
  
    return NaN;
  }

  const duration = endDateObj - startDateObj + MS_IN_DAY;
  const numberOfDays = Math.ceil(duration / MS_IN_DAY);

  if (numberOfDays <= 31) {
    return "d";
  } else if (numberOfDays <= 365) {
    return "m";
  } else {
    return "y";
  }
};


