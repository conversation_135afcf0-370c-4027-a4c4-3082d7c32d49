export const baseUrl = process.env.REACT_APP_API_URL
export const stripeKey = process.env.REACT_APP_STRIPE_KEY
export const imageUrl = process.env.REACT_APP_API_IMAGE_URL

// constants.js
// constants.js
export const activityLogConstants = [
    {
      id: 1,
      export_history: {
        started_at: "2024-01-12 15:05:45",
        ended_at: "2024-01-01 15:05:45",
        total_time: 25,
        is_error: 0,
      },
      table_id: 1,
      database_id: 1,
      server_id: 1,
      table_name: "table_1",
      database_name: "database_1",
      server_name: "server_1",
    },
    {
        id: 1,
        export_history: {
          started_at: "2024-01-12 15:05:45",
          ended_at: "2024-01-01 15:05:45",
          total_time: 25,
          is_error: 0,
        },
        table_id: 1,
        database_id: 1,
        server_id: 1,
        table_name: "table_1",
        database_name: "database_1",
        server_name: "server_1",
      },
    {
      id: 2,
      export_history: {
        started_at: "2024-01-11 15:05:45",
        ended_at: "2024-01-01 15:05:45",
        total_time: 35,
        is_error: 1,
      },
      table_id: 2,
      database_id: 2,
      server_id: 2,
      table_name: "table_2",
      database_name: "database_2",
      server_name: "server_2",
    },
    {
        id: 2,
        export_history: {
          started_at: "2024-01-11 15:05:45",
          ended_at: "2024-01-01 15:05:45",
          total_time: 35,
          is_error: 1,
        },
        table_id: 2,
        database_id: 2,
        server_id: 2,
        table_name: "table_2",
        database_name: "database_2",
        server_name: "server_2",
      },
      {
        id: 2,
        export_history: {
          started_at: "2024-01-11 15:05:45",
          ended_at: "2024-01-01 15:05:45",
          total_time: 35,
          is_error: 1,
        },
        table_id: 2,
        database_id: 2,
        server_id: 2,
        table_name: "table_2",
        database_name: "database_2",
        server_name: "server_2",
      },
      {
        id: 2,
        export_history: {
          started_at: "2024-01-11 15:05:45",
          ended_at: "2024-01-01 15:05:45",
          total_time: 35,
          is_error: 1,
        },
        table_id: 2,
        database_id: 2,
        server_id: 2,
        table_name: "table_2",
        database_name: "database_2",
        server_name: "server_2",
      },
      {
        id: 2,
        export_history: {
          started_at: "2024-01-11 15:05:45",
          ended_at: "2024-01-01 15:05:45",
          total_time: 35,
          is_error: 1,
        },
        table_id: 2,
        database_id: 2,
        server_id: 2,
        table_name: "table_2",
        database_name: "database_2",
        server_name: "server_2",
      },
    // Add more entries as needed
  ];
  
  
  export const scheduleTime = [
    { id: "00", name: "12:00 AM" },
    { id: "01", name: "01:00 AM" },
    { id: "02", name: "02:00 AM" },
    { id: "03", name: "03:00 AM" },
    { id: "04", name: "04:00 AM" },
    { id: "05", name: "05:00 AM" },
    { id: "06", name: "06:00 AM" },
    { id: "07", name: "07:00 AM" },
    { id: "08", name: "08:00 AM" },
    { id: "09", name: "09:00 AM" },
    { id: 10, name: "10:00 AM" },
    { id: 11, name: "11:00 AM" },
    { id: 12, name: "12:00 PM" },
    { id: 13, name: "1:00 PM" },
    { id: 14, name: "02:00 PM" },
    { id: 15, name: "03:00 PM" },
    { id: 16, name: "04:00 PM" },
    { id: 17, name: "05:00 PM" },
    { id: 18, name: "06:00 PM" },
    { id: 19, name: "07:00 PM" },
    { id: 20, name: "08:00 PM" },
    { id: 21, name: "09:00 PM" },
    { id: 22, name: "10:00 PM" },
    { id: 23, name: "11:00 PM" },
  ];
  
  export const daysName = [
    { id: 1, name: "Mon" },
    { id: 2, name: "Tue" },
    { id: 4, name: "Wed" },
    { id: 8, name: "Thu" },
    { id: 16, name: "Fri" },
    { id: 32, name: "Sat" },
    { id: 64, name: "Sun" },
  ];