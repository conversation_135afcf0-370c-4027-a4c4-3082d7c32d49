import React, { useState } from "react";
import { Navbar, Container, Nav, Image, Dropdown, Modal } from "react-bootstrap";
import logo from "../../assets/images/header-logo.png";
import settings from "../../assets/images/settings.png";
import NotificationIcon from "./NotificationIcon";
import { navMenu } from "../../constants/pricingConstant";
import ButtonImage from "./../../assets/images/ligtning.svg";
import { useDispatch, useSelector } from "react-redux";
import { imageUrl } from "../../config/constants";
import { updateUserInfo } from "../../services/updateProfileInfo";
import { handleCookies } from "../../utils/cookies";
import Loader from "../../components/loader/Loader";
import Close from "../../assets/images/x-close.svg";
import Menu from "../../assets/images/menu-01.svg";
import { Link, useNavigate } from "react-router-dom";
import lightningImage from "./../../assets/images/ligtning.svg";
import notification from "../../assets/images/notification.svg";
import { useMediaQuery } from "react-responsive";
import UpgradePricePlanModal from "../../components/settingPageTabs/UpgradePricePlanModal";
import UpgradePaymentCheckout from "../../components/settingPageTabs/UpgradePaymentCheckout";
import Notification from "../../components/notificationModal/Notification";
import { userInfo } from "../../redux/features/UserInformation";
import "./style.css";

const CustomDropdownToggle = React.forwardRef(({ children, onClick }, ref) => {
  return (
    <div ref={ref} onClick={onClick}>
      {children}
    </div>
  );
});

function Header() {
  const navigate = useNavigate();
  const isMobile = useMediaQuery({ maxWidth: 767 });
  const [open, setOpen] = useState(false);
  const profileImage = useSelector((state) => state.imageSrc);
  const userName = useSelector((state) => state.userName);
  const userFullName = useSelector((state) => state.userFullName);
  const userTrialPeriod = useSelector((state) => state.userTrialPeriod);
  const userInformation = useSelector((state) => state.userInfo);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [pricingModal, setPricingModal] = useState(false);
  const [upgradePaymentModal, setUpgradePaymentModal] = useState(false);
  const [priceObj, setPriceObj] = useState();
  const [showNotification, setShowNotification] = useState(false);
  const [isError, setIsError] = useState();
  const [responseMessage, setResponseMessage] = useState();
  const dispatch = useDispatch();

  const handleMenuToggle = () => {
    setIsMenuOpen(!isMenuOpen);
  };
  const handleLogout = async () => {
    setOpen(true);
    const response = await updateUserInfo.signOut();
    setOpen(false);
    if (response.status === "Success") {
      handleCookies.deleteCookies();
      window.location.href = "/";
    }
  };

  const handleClose = () => setPricingModal(false);
  const handlePaymentModalClose = () => setUpgradePaymentModal(false);
  const handleChoosePlan = () => {
    setPricingModal(true);
    setUpgradePaymentModal(false);
  }

  const handleContinueToPayment = (title, price, stripe_plan, id, tag, plan_price_end_date) => {
    setPricingModal(false);
    setUpgradePaymentModal(true);
    setPriceObj({
      title,
      price,
      stripe_plan,
      id,
      tag,
      plan_price_end_date
    });
  }

  const handleUpgradePayment = async (plan_id) => {
    setShowNotification(false);
    setUpgradePaymentModal(false);
    setOpen(true);
    const response = await updateUserInfo.upgradePlan(plan_id);
    setShowNotification(true);
    setOpen(false);
    if (response.status === 'Success') {
      setIsError(false);
      setResponseMessage(response.message);
      const userData = await updateUserInfo.userInfo();
      if (userData && userData.data && userData.data.user) {
        dispatch(userInfo(userData.data.user));
      }
    } else {
      setIsError(true);
      setResponseMessage(response);
    }
  }

  return (
    <>
      <Loader open={open} />
      {isMobile ? (
        <>
          {" "}
          <header className="header-background">
            <Navbar expand="lg" className="">
              <Container className="p-o m-0 nav-mobile-container-header">
                <Navbar.Brand className="brand-mobile-container" onClick={() => navigate("/")}>
                  <img alt="" src={logo} />
                </Navbar.Brand>
                {!isMenuOpen ? (
                  <>
                    <div className="nav-close-navigations">
                      <Link to="/settings" className="px-3">
                        <img alt="Settings Icon" src={settings} />
                      </Link>
                      <Nav.Link className="py-0  ps-0 " onClick={() => navigate("/")}>
                        <img
                          className="nav-icon"
                          alt="Notification Icon"
                          src={notification}
                        />
                      </Nav.Link>
                      <Nav className="p-0 user-image close-icons">
                        <Dropdown>
                          <Dropdown.Toggle
                            as={CustomDropdownToggle}
                            variant="link"
                            id="dropdown-basic"
                          >
                            {profileImage ? (
                              <img
                                className="me-3"
                                alt="User"
                                src={`${imageUrl}${profileImage}`}
                              />
                            ) : (
                              <p className="header-username username d-flex align-items-center justify-content-center m-0">
                                {" "}
                                {userName}{" "}
                              </p>
                            )}
                          </Dropdown.Toggle>

                          <Dropdown.Menu style={{ minWidth: "95px" }}>
                            <Dropdown.Item onClick={handleLogout}>
                              Logout
                            </Dropdown.Item>
                          </Dropdown.Menu>
                        </Dropdown>
                      </Nav>
                    </div>
                  </>
                ) : (
                  ""
                )}

                <Navbar.Toggle
                  aria-controls="basic-navbar-nav"
                  className="mobile-menu-toggle-icon"
                  onClick={handleMenuToggle}
                >
                  <img
                    src={isMenuOpen ? Close : Menu}
                    alt={isMenuOpen ? "Close Icon" : "Menu Icon"}
                  />
                </Navbar.Toggle>
                <Navbar.Collapse id="basic-navbar-nav">
                  <Nav className="me-auto mt-2">
                    {navMenu.map((menu, i) => (
                      <Nav.Link
                        key={i}
                        className={`px-3 mt-3 ${window.location.pathname === menu.link
                          ? "active-link-mobile-header"
                          : ""
                          }`}
                        onClick={() => navigate(menu.link)}
                        active={window.location.pathname === menu.link}
                      >
                        <span className="menu-names">{menu.name}</span>
                      </Nav.Link>
                    ))}
                  </Nav>

                  <Nav className="me-auto">
                    <div className="profile-conatiner">
                      <span className="mobile-profile">Profile</span>
                      <Nav.Link
                        className={`py-0 ${window.location.pathname === "/settings"
                          ? "active-link-mobile-header"
                          : ""
                          }`}
                        onClick={() => navigate("/settings")}
                      >
                        <div className="mobile-nav-icons-conatiner">
                          <img
                            style={{ width: "40px", height: "40px" }}
                            alt="Settings Icon"
                            src={settings}
                          />
                          <span className={`mobile-nav-tex`}>Settings</span>
                        </div>
                      </Nav.Link>
                      <NotificationIcon
                        className="nav-line-item-margin"
                        isMobile={true}
                      />{" "}
                      <Nav.Link className="p-0 user-image mt-1">
                        <div className="mobile-nav-icons-conatiner">
                          {profileImage ? (
                            <img
                              className="me-3"
                              alt="User"
                              src={`${imageUrl}${profileImage}`}
                            />
                          ) : (
                            <p className="header-username username d-flex align-items-center justify-content-center m-0">
                              {" "}
                              {userName}{" "}
                            </p>
                          )}
                          <span className="mobile-nav-text">
                            {userFullName}
                          </span>
                        </div>
                      </Nav.Link>
                    </div>
                    <button className="header-button mobile-nav-button btn-text nav-line-item-margin mb-3">
                      <div className="lightning-image">
                        <Image src={lightningImage} alt="Icon" fluid />
                      </div>
                      Upgrade now
                    </button>
                  </Nav>
                </Navbar.Collapse>
              </Container>
            </Navbar>
          </header>{" "}
        </>
      ) : (
        <>
          {" "}
          <header className="header-background">
            <Navbar className="py-3" sticky="top">
              <Navbar.Collapse id="basic-navbar-nav">
                <Navbar.Brand
                  className="ps-3 ms-3 py-0 pe-4 me-0 cursor-pointer"
                  onClick={() => navigate("/")}
                >
                  <img alt="Logo" src={logo} />
                </Navbar.Brand>
                <Nav className="me-auto">
                  {navMenu.map((menu, i) => {
                    return (
                      <Nav.Link
                        key={i}
                        className=" px-3"
                        onClick={() => navigate(menu.link)}
                        active={window.location.pathname === menu.link}
                        style={
                          window.location.pathname === menu.link
                            ? activeLinkStyle
                            : normalLinkStyle
                        }
                      >
                        {menu.name}
                      </Nav.Link>
                    );
                  })}
                </Nav>
              </Navbar.Collapse>

              <Nav className="me-3 px-3 align-items-center">
                <Nav.Link
                  className="header-button btn-text"
                  onClick={() => { !userInformation?.is_subscribed ? navigate("/priceplan") : setPricingModal(true) }}
                >
                  <img src={ButtonImage} alt="Icon" className="img-btn" />
                  Upgrade now
                </Nav.Link>
                <Nav.Link
                  className="py-0 px-3"
                  onClick={() => navigate("/settings")}
                >
                  <img alt="Settings Icon" src={settings} />
                </Nav.Link>
                <NotificationIcon />
                <Nav className="p-0 user-image">
                  <Dropdown>
                    <Dropdown.Toggle
                      as={CustomDropdownToggle}
                      variant="link"
                      id="dropdown-basic"
                    >
                      {profileImage ? (
                        <img
                          className="me-3"
                          alt="User"
                          src={`${imageUrl}${profileImage}`}
                        />
                      ) : (
                        <p className="header-username username d-flex align-items-center justify-content-center m-0">
                          {" "}
                          {userName}{" "}
                        </p>
                      )}
                    </Dropdown.Toggle>

                    <Dropdown.Menu className="custom-dropdown-menu" style={{ minWidth: "95px" }}>
                      <Dropdown.Item onClick={handleLogout}>
                        Logout
                      </Dropdown.Item>
                    </Dropdown.Menu>
                  </Dropdown>
                </Nav>
              </Nav>
            </Navbar>
          </header>
        </>
      )}

      {userTrialPeriod >0 && (
        <div className="trial-bar text-center">
          Trial Active: {userTrialPeriod}{" "}
          {userTrialPeriod === 1 ? "day left" : "days left"}
        </div>
      )}
      <Modal show={pricingModal} onHide={handleClose} className='price-plan-modal'>
        <Modal.Header className='border-0 p-2 m-1' closeButton>
        </Modal.Header>
        <div className='px-4'>
          <h2 className='text-center'>Upgrade your account</h2>
          <UpgradePricePlanModal userCurrentPlanId={userInformation.subscrition_plan_id} handleContinueToPayment={handleContinueToPayment} />
        </div>
      </Modal>
      <Modal show={upgradePaymentModal} onHide={handlePaymentModalClose} className='payment-checkout-modal'>
        <Modal.Header className='border-0 p-2 m-1' closeButton>
        </Modal.Header>
        <div className='px-4'>
          <h2 className='text-center'>Upgrade your account</h2>
          <UpgradePaymentCheckout priceObj={priceObj} handleChoosePlan={handleChoosePlan} handleUpgradePayment={handleUpgradePayment} />
        </div>
      </Modal>
      {showNotification &&
        <Notification responseMessage={responseMessage} isError={isError} />
      }
    </>
  );
}

const activeLinkStyle = {
  display: "flex",
  padding: "8px 12px",
  alignItems: "center",
  gap: "105px",
  borderRadius: "6px",
  background: "var(--shades-desaturate-3, #1E253A)",
  color: "#FFFFFF",
};

const normalLinkStyle = {
  paddingY: "0",
};

export default Header;
