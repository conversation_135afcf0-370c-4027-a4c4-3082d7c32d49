import React from 'react'
import { Nav } from 'react-bootstrap'
import notification from '../../assets/images/notification.svg'
import './style.css'
import { useNavigate } from 'react-router';


function NotificationIcon({isMobile}) {
  const navigate = useNavigate();

    return (
        <Nav.Link className='py-0  ps-0 pe-4'  onClick={() => navigate('/settings')}>
          {isMobile ? <> <div className="mobile-nav-icons-conatiner">
            <img style={{width:"23px", height:"23px", marginLeft:"9px"}} 
                alt="Notification Icon"
                src={notification}
            />
            <span  className="mobile-nav-tex">Notification</span>

            </div></>:<> <img className="nav-icon" 
                alt="Notification Icon"
                src={notification}
            /></>} 
            
        </Nav.Link>
    )
}

export default NotificationIcon
