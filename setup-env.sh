#!/bin/bash

# Check if .env file exists in the backend container
echo "Checking if .env file exists..."
if docker-compose exec backend test -f /var/www/html/.env; then
    echo ".env file exists in the container"
else
    echo ".env file does not exist in the container. Creating it..."
    docker-compose exec backend cp .env.example .env
    echo ".env file created from .env.example"
fi

# Generate Laravel application key if needed
echo "Generating Laravel application key..."
docker-compose exec backend php artisan key:generate --force
echo "Application key generated"

# Create storage link if needed
echo "Creating storage link..."
docker-compose exec backend php artisan storage:link
echo "Storage link created"

# Show .env file content
echo "Contents of .env file:"
docker-compose exec backend cat .env

# Restart the backend container
echo "Restarting backend container..."
docker-compose restart backend
echo "Backend container restarted"

# Show backend logs
echo "Backend logs:"
docker-compose logs backend