# Elysium Docker Setup

This document explains how to run the Elysium project using Docker containers.

## Prerequisites

- <PERSON><PERSON> and <PERSON><PERSON> Compose installed on your system
- Git repository cloned locally

## Container Structure

The setup includes four containers:

1. **Backend Container**: <PERSON>vel application with PHP 8.1
2. **Frontend Container**: React application with Node.js
3. **Database Container**: MySQL 8.0 server
4. **Tools Container**: phpMyAdmin for database management

All containers are connected via the `elysium-network` bridge network.

## Getting Started

1. Make sure you have the project structure with:
   - `elysium-backend/` - Laravel backend
   - `elysium-frontend/` - React frontend

2. Start the containers:
   ```bash
   docker-compose up --build
   ```

3. Access the applications:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - phpMyAdmin: http://localhost:8080 (login with username: elysium, password: elysium_password)

## Development Workflow

- The containers use volume mounting to reflect code changes immediately
- Backend changes will be visible after saving files
- Frontend changes will trigger automatic recompilation

## Environment Configuration

### Frontend Environment Variables

The frontend requires these environment variables to connect to the backend:

- `REACT_APP_API_URL`: URL for the backend API (default: http://localhost:8000/api)
- `REACT_APP_API_IMAGE_URL`: URL for accessing stored images (default: http://localhost:8000/storage)
- `REACT_APP_STRIPE_KEY`: Your Stripe API key for payment processing

These are set in three places:
1. In the `.env` file in the frontend directory
2. In the Dockerfile.frontend
3. In the docker-compose.yml environment section

### Troubleshooting API Connection Issues

If you see errors like "http://localhost:3000/undefined/countries", it means the environment variables aren't being properly loaded. Try these fixes:

1. Rebuild the frontend container:
   ```bash
   docker-compose build frontend
   ```

2. Restart with environment variables:
   ```bash
   docker-compose up -d --force-recreate frontend
   ```

3. Check that the variables are available in the container:
   ```bash
   docker-compose exec frontend env | grep REACT_APP
   ```

## Troubleshooting

- If you encounter permission issues with mounted volumes, run:
  ```bash
  sudo chown -R $USER:$USER elysium-backend elysium-frontend
  ```

- If the backend container fails to start, check Laravel logs:
  ```bash
  docker-compose logs backend
  ```
