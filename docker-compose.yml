version: '3.8'

services:
  # Backend Laravel container
  backend_local:
    build:
      context: ./elysium-backend
      dockerfile: ../bin/Dockerfile.backend
    volumes:
      - ./elysium-backend:/var/www/html
      # Remove the vendor volume mount to allow container to use its own vendor directory
    ports:
      - "8000:8000"
    depends_on:
      - db
    networks:
      - elysium-network
    environment:
      DB_CONNECTION: mysql
      DB_HOST: db
      DB_PORT: 3306
      DB_DATABASE: elysium
      DB_USERNAME: elysium
      DB_PASSWORD: elysium_password
      APP_URL: http://localhost:8000
    # Run composer install and copy .env file before starting the server
    command: bash -c "cp -n .env.example .env && composer install && php artisan key:generate && php artisan serve --host=0.0.0.0"

  # Frontend React container
  frontend_local:
    build:
      context: ./elysium-frontend
      dockerfile: ../bin/Dockerfile.frontend
    volumes:
      - ./elysium-frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    depends_on:
      - backend_local
    environment:
      - REACT_APP_API_URL=http://localhost:8000/api
      - REACT_APP_API_IMAGE_URL=http://localhost:8000/storage
      - REACT_APP_STRIPE_KEY=your_stripe_key_here
    networks:
      - elysium-network
    command: npm start

  
  bastion:
    build:
      context: ./bin/bastion
      dockerfile: Dockerfile.bastion
    container_name: elysium-bastion
    hostname: elysium-bastion
    networks:
      - elysium-network
    volumes:
      - ./bin/bastion-data:/home/<USER>/app
      - ./bin/bastion-data/remote:/home/<USER>/remote
      - ./bin/bastion-data/agent:/home/<USER>/agent
      - ./bin/bastion/id_rsa.pub:/root/.ssh/authorized_keys:ro
    environment:
      # - MYSQL_ROOT_PASSWORD=rootpassword
      - TZ=UTC
    ports:
      - "3307:3306"  # Expose MySQL port
      - "1340:1340"  # Expose MySQL port
      - "2222:22"    # Optional: SSH access
    # command: bash -c "apt-get update && apt-get install -y mysql-server nano && service mysql start && tail -f /dev/null"

    

  # MySQL Database container
  db:
    image: mysql:8.0
    volumes:
      - mysql-data:/var/lib/mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: elysium
      MYSQL_USER: elysium
      MYSQL_PASSWORD: elysium_password
    networks:
      - elysium-network

  # phpMyAdmin container
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    volumes:
      - ./bin/volumes/mysql-data:/var/lib/mysql
    ports:
      - "8080:80"
    environment:
      PMA_HOST: 127.0.0.1
      PMA_PORT: 63306
      MYSQL_ROOT_PASSWORD: root_password
    depends_on:
      - db
    networks:
      - elysium-network

  # Adminer container on port 8181
  adminer:
    image: adminer:latest
    ports:
      - "8181:8080"
    environment:
      ADMINER_DEFAULT_SERVER: db
    depends_on:
      - db
    networks:
      - elysium-network

networks:
  elysium-network:
    driver: bridge

volumes:
  mysql-data:
